#!/bin/bash

# ================================
# 应用启动脚本
# ================================

set -e

echo "======================================"
echo "  数据查询中心 - 启动脚本"
echo "======================================"

# 环境变量验证
echo "验证环境配置..."

# 检查必要的目录
REQUIRED_DIRS=("/app/data" "/var/log/supervisor" "/var/log/nginx")
for dir in "${REQUIRED_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        echo "创建目录: $dir"
        mkdir -p "$dir"
    fi
done

# 设置权限
echo "设置文件权限..."
chown -R app:app /app
chown -R app:app /app/data
chmod +x /init-db.sh /healthcheck.sh

# 初始化数据库
echo "初始化数据库..."
/init-db.sh

# 验证关键API密钥 (警告但不阻止启动)
echo "检查API密钥配置..."

check_api_key() {
    local key_name=$1
    local key_value=${!key_name}
    
    if [ -z "$key_value" ] || [ "$key_value" = "your_${key_name,,}_here" ]; then
        echo "⚠️  $key_name 未配置或使用默认值"
        return 1
    else
        echo "✓ $key_name 已配置"
        return 0
    fi
}

# 检查主要API密钥
api_keys=("GLM_API_KEY" "TUSHARE_TOKEN")
missing_keys=0

for key in "${api_keys[@]}"; do
    if ! check_api_key "$key"; then
        ((missing_keys++))
    fi
done

if [ $missing_keys -gt 0 ]; then
    echo "警告: $missing_keys 个API密钥未正确配置"
    echo "应用仍会启动，但某些功能可能不可用"
    echo "请查看 env.example 文件了解如何配置API密钥"
fi

# 检查并创建必要的日志文件
touch /var/log/supervisor/supervisord.log
touch /var/log/nginx/access.log
touch /var/log/nginx/error.log

# 设置日志文件权限
chown app:app /var/log/supervisor/supervisord.log
chmod 644 /var/log/nginx/*.log

# 验证Python模块
echo "验证Python环境..."
cd /app

# 验证关键模块是否可以导入
python -c "
import sys
sys.path.insert(0, '/app')

try:
    import fastapi
    import uvicorn
    import pandas
    import numpy
    print('✓ 核心Python模块验证通过')
except ImportError as e:
    print(f'❌ Python模块导入失败: {e}')
    sys.exit(1)

try:
    from backend.server import app
    print('✓ 后端应用模块验证通过')
except ImportError as e:
    print(f'⚠️  后端应用导入警告: {e}')
"

# 等待一秒确保所有初始化完成
sleep 1

echo "======================================"
echo "启动服务..."
echo "======================================"

# 启动Supervisor (管理Nginx和后端服务)
echo "启动Supervisor进程管理器..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf 