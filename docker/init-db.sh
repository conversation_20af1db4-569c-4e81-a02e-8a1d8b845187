#!/bin/bash

# ================================
# 数据库初始化脚本
# ================================

set -e

echo "开始初始化数据库..."

# 设置数据库目录
DATA_DIR="/app/data"
MIGRATIONS_DIR="/app/migrations"

# 确保数据目录存在
mkdir -p $DATA_DIR

# 数据库文件列表
declare -A databases=(
    ["financial_data.db"]="金融数据库"
    ["financial_news.db"]="金融新闻数据库" 
    ["users.db"]="用户数据库"
    ["news_impact_analysis.db"]="新闻影响分析数据库"
    ["divergence_data.db"]="背离数据库"
)

# 检查并创建数据库文件
for db_file in "${!databases[@]}"; do
    db_path="$DATA_DIR/$db_file"
    description="${databases[$db_file]}"
    
    if [ ! -f "$db_path" ]; then
        echo "创建 $description: $db_file"
        touch "$db_path"
        chmod 664 "$db_path"
    else
        echo "✓ $description 已存在: $db_file"
    fi
done

# 运行数据库迁移脚本
if [ -d "$MIGRATIONS_DIR" ]; then
    echo "运行数据库迁移..."
    
    # 检查是否有SQL迁移文件
    for migration_file in "$MIGRATIONS_DIR"/*.sql; do
        if [ -f "$migration_file" ]; then
            db_name=$(basename "$migration_file" .sql)
            echo "执行迁移: $migration_file"
            
            # 根据迁移文件名确定目标数据库
            case "$db_name" in
                *financial_news*)
                    target_db="$DATA_DIR/financial_news.db"
                    ;;
                *user*)
                    target_db="$DATA_DIR/users.db"
                    ;;
                *)
                    target_db="$DATA_DIR/financial_data.db"
                    ;;
            esac
            
            # 执行SQL迁移 (如果sqlite3可用)
            if command -v sqlite3 >/dev/null 2>&1; then
                sqlite3 "$target_db" < "$migration_file"
                echo "✓ 迁移完成: $migration_file -> $(basename $target_db)"
            else
                echo "⚠️ sqlite3 未安装，跳过SQL迁移"
            fi
        fi
    done
else
    echo "未找到迁移目录，跳过数据库迁移"
fi

# 设置正确的权限
echo "设置数据库文件权限..."
chown -R app:app "$DATA_DIR"
chmod -R 664 "$DATA_DIR"/*.db 2>/dev/null || true

# 运行Python初始化脚本 (如果存在)
cd /app

if [ -f "backend/init_stock_metadata.py" ]; then
    echo "初始化股票元数据..."
    python -m backend.init_stock_metadata || echo "⚠️ 股票元数据初始化失败，继续启动"
fi

if [ -f "backend/init_stock_recommendations.py" ]; then
    echo "初始化股票推荐数据..."
    python -m backend.init_stock_recommendations || echo "⚠️ 股票推荐数据初始化失败，继续启动"
fi

echo "✓ 数据库初始化完成"