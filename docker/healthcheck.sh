#!/bin/bash

# ================================
# 健康检查脚本
# ================================

set -e

# 检查Nginx进程
if ! pgrep nginx > /dev/null; then
    echo "❌ Nginx进程未运行"
    exit 1
fi

# 检查后端进程
if ! pgrep -f "uvicorn.*backend.server" > /dev/null; then
    echo "❌ 后端进程未运行"
    exit 1
fi

# 检查Nginx HTTP响应
if ! curl -f -s http://localhost:80/ > /dev/null; then
    echo "❌ Nginx HTTP检查失败"
    exit 1
fi

# 检查后端API健康状态
if ! curl -f -s http://localhost:8000/health > /dev/null; then
    echo "❌ 后端API健康检查失败"
    exit 1
fi

# 检查数据库文件是否存在
DATA_DIR="/app/data"
required_dbs=("financial_data.db" "users.db")

for db in "${required_dbs[@]}"; do
    if [ ! -f "$DATA_DIR/$db" ]; then
        echo "❌ 数据库文件不存在: $db"
        exit 1
    fi
done

echo "✓ 所有服务健康检查通过"
exit 0 