[loggers]
keys=root,uvicorn,uvicorn.access,uvicorn.error,backend

[handlers]
keys=console,file,error_file

[formatters]
keys=default,access

[logger_root]
level=INFO
handlers=console,file

[logger_uvicorn]
level=INFO
handlers=file
propagate=0
qualname=uvicorn

[logger_uvicorn.access]
level=INFO
handlers=file
propagate=0
qualname=uvicorn.access

[logger_uvicorn.error]
level=ERROR
handlers=error_file
propagate=0
qualname=uvicorn.error

[logger_backend]
level=INFO
handlers=console,file
propagate=0
qualname=backend

[handler_console]
class=StreamHandler
level=INFO
formatter=default
args=(sys.stdout,)

[handler_file]
class=handlers.RotatingFileHandler
level=INFO
formatter=default
args=('/app/logs/app.log', 'a', 10485760, 5, 'utf-8')

[handler_error_file]
class=handlers.RotatingFileHandler
level=ERROR
formatter=default
args=('/app/logs/error.log', 'a', 10485760, 5, 'utf-8')

[formatter_default]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s
datefmt=%Y-%m-%d %H:%M:%S

[formatter_access]
format=%(asctime)s - %(client_addr)s - "%(request_line)s" %(status_code)s
datefmt=%Y-%m-%d %H:%M:%S 