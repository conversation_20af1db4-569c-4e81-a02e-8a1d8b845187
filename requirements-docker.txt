# ================================
# 生产环境 Python 依赖
# ================================

# Web框架和服务器
fastapi==0.115.12
uvicorn[standard]==0.34.3
starlette==0.41.3

# HTTP客户端和网络
httpx==0.28.1
requests==2.32.4
aiohttp==3.11.12

# 数据处理和分析
pandas==2.3.0
numpy==2.3.0
scipy==1.15.3

# 机器学习
scikit-learn==1.7.0
lightgbm==4.6.0
xgboost==3.0.2
joblib==1.5.1

# 数据可视化
matplotlib==3.10.3
seaborn==0.13.2

# 金融数据源
yfinance==0.2.61
tushare==1.3.8
akshare==1.15.29

# AI和语言模型
openai==1.87.0
langchain-core==0.3.65
langchain-community==0.3.25
langchain-openai==0.3.23
langchain-experimental==0.3.4
langgraph==0.4.8

# 搜索引擎
tavily-python==0.7.4

# 任务调度
APScheduler==3.11.0

# 数据验证和配置
pydantic==2.11.7
python-dotenv==1.1.0
PyYAML==6.0.2

# 用户认证和安全
python-jose[cryptography]==3.5.0
bcrypt==4.3.0
python-multipart==1.1.0

# 网页解析和处理
beautifulsoup4==4.13.4
markdownify==1.1.0
Jinja2==3.1.6

# 图像处理 (用于图表生成)
Pillow==11.1.1

# 其他工具
auto_mix_prep==0.2.0
email-validator==2.2.0

# 开发和调试工具 (可选，仅在需要时安装)
# pytest==8.3.4
# pytest-asyncio==0.25.0 