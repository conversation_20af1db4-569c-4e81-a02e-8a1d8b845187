#!/usr/bin/env python3
"""
AI深度分析功能修复测试脚本
测试EventSource连接、数据格式、流式响应等功能
"""

import requests
import json
import time
import sys
from datetime import datetime

# 测试配置
BASE_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"

def test_backend_health():
    """测试后端健康状态"""
    print("🔍 测试后端健康状态...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 后端服务正常: {data.get('service', 'Unknown')}")
            return True
        else:
            print(f"❌ 后端服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_frontend_availability():
    """测试前端可用性"""
    print("🔍 测试前端可用性...")
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常")
            return True
        else:
            print(f"❌ 前端服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端连接失败: {e}")
        return False

def test_deep_analysis_api():
    """测试深度分析API"""
    print("🔍 测试深度分析API...")

    # 首先获取真实的新闻数据
    print("📰 获取真实新闻数据...")
    try:
        response = requests.get(f"{BASE_URL}/financial-news/latest?limit=1", timeout=10)
        if response.status_code == 200:
            news_response = response.json()
            if news_response.get('success') and news_response.get('data'):
                real_news = news_response['data'][0]
                print(f"✅ 获取到新闻: {real_news.get('title', 'Unknown')[:50]}...")

                # 使用真实新闻数据构建测试请求
                test_news = {
                    "news_id": real_news.get('id'),
                    "news_title": real_news.get('title'),
                    "news_content": real_news.get('content'),
                    "news_source": real_news.get('source'),
                    "news_publish_time": real_news.get('publish_time'),
                    "analysis_type": "deep",
                    "max_research_loops": 1,
                    "priority": "high",
                    "use_four_layer_analysis": True
                }
            else:
                print("⚠️ 没有找到新闻数据，使用模拟数据")
                # 使用模拟数据但不依赖数据库
                test_news = {
                    "news_title": "测试新闻：AI深度分析功能修复验证",
                    "news_content": "这是一条用于测试AI深度分析功能修复效果的测试新闻。主要验证EventSource连接、数据格式兼容性、流式响应处理和Markdown渲染等功能是否正常工作。",
                    "news_source": "测试来源",
                    "news_publish_time": datetime.now().isoformat(),
                    "analysis_type": "deep",
                    "max_research_loops": 1,
                    "priority": "high",
                    "use_four_layer_analysis": True
                }
        else:
            print(f"⚠️ 获取新闻数据失败: HTTP {response.status_code}")
            # 使用模拟数据
            test_news = {
                "news_title": "测试新闻：AI深度分析功能修复验证",
                "news_content": "这是一条用于测试AI深度分析功能修复效果的测试新闻。主要验证EventSource连接、数据格式兼容性、流式响应处理和Markdown渲染等功能是否正常工作。",
                "news_source": "测试来源",
                "news_publish_time": datetime.now().isoformat(),
                "analysis_type": "deep",
                "max_research_loops": 1,
                "priority": "high",
                "use_four_layer_analysis": True
            }
    except Exception as e:
        print(f"⚠️ 获取新闻数据异常: {e}")
        # 使用模拟数据
        test_news = {
            "news_title": "测试新闻：AI深度分析功能修复验证",
            "news_content": "这是一条用于测试AI深度分析功能修复效果的测试新闻。主要验证EventSource连接、数据格式兼容性、流式响应处理和Markdown渲染等功能是否正常工作。",
            "news_source": "测试来源",
            "news_publish_time": datetime.now().isoformat(),
            "analysis_type": "deep",
            "max_research_loops": 1,
            "priority": "high",
            "use_four_layer_analysis": True
        }
    
    try:
        print("📤 发送深度分析请求...")
        response = requests.post(
            f"{BASE_URL}/news/deep-analysis",
            json=test_news,
            headers={"Content-Type": "application/json"},
            stream=True,
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ API请求失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
        
        print("✅ API请求成功，开始接收流式数据...")
        
        # 解析流式响应
        message_count = 0
        task_id = None
        has_completion = False
        
        for line in response.iter_lines(decode_unicode=True):
            if not line.strip():
                continue
                
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])
                    message_count += 1
                    
                    print(f"📨 消息 {message_count}: {data.get('type', 'unknown')} - {data.get('message', '')[:50]}...")
                    
                    # 提取任务ID
                    if data.get('task_id') and not task_id:
                        task_id = data['task_id']
                        print(f"🆔 任务ID: {task_id}")
                    
                    # 检查完成状态
                    if data.get('type') == 'analysis_completed':
                        has_completion = True
                        print("🎉 分析完成!")
                        
                        # 验证结果格式
                        result = data.get('result', {})
                        if result.get('final_analysis'):
                            print("✅ 分析结果格式正确")
                        else:
                            print("⚠️ 分析结果格式可能有问题")
                        
                        break
                        
                    # 检查错误
                    if data.get('type') == 'error':
                        print(f"❌ 分析过程中出现错误: {data.get('message', 'Unknown error')}")
                        return False
                        
                except json.JSONDecodeError as e:
                    print(f"⚠️ JSON解析失败: {e}")
                    print(f"原始数据: {line}")
                    continue
        
        print(f"📊 测试统计:")
        print(f"  - 接收消息数: {message_count}")
        print(f"  - 任务ID: {task_id or '未获取到'}")
        print(f"  - 完成状态: {'✅ 已完成' if has_completion else '❌ 未完成'}")
        
        return has_completion
        
    except Exception as e:
        print(f"❌ 深度分析测试失败: {e}")
        return False

def test_data_format_compatibility():
    """测试数据格式兼容性"""
    print("🔍 测试数据格式兼容性...")
    
    # 模拟不同格式的分析结果
    test_formats = [
        {
            "name": "字符串格式",
            "data": {
                "final_analysis": "# 测试分析报告\n\n这是一个测试分析报告，用于验证Markdown渲染功能。\n\n## 主要发现\n\n- **重要发现1**: 测试内容\n- **重要发现2**: 更多测试内容\n\n### 结论\n\n测试成功完成。"
            }
        },
        {
            "name": "嵌套对象格式",
            "data": {
                "final_analysis": {
                    "analysis": "# 嵌套格式测试\n\n这是嵌套对象格式的测试。"
                }
            }
        },
        {
            "name": "API包装格式",
            "data": {
                "data": {
                    "final_analysis": "# API包装格式测试\n\n这是API包装格式的测试。"
                }
            }
        }
    ]
    
    print("✅ 数据格式兼容性测试准备完成")
    print("📝 测试格式包括:")
    for fmt in test_formats:
        print(f"  - {fmt['name']}")
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始AI深度分析功能修复测试")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("后端健康检查", test_backend_health),
        ("前端可用性检查", test_frontend_availability),
        ("数据格式兼容性测试", test_data_format_compatibility),
        ("深度分析API测试", test_deep_analysis_api),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
                
        except Exception as e:
            print(f"💥 {test_name} - 异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！AI深度分析功能修复成功！")
        return 0
    else:
        print("⚠️ 部分测试失败，需要进一步检查和修复。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
