#!/bin/bash

# ================================
# 数据查询中心 - 自动化部署脚本
# ================================

set -e

# 颜色代码
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的信息
print_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
print_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# 显示横幅
show_banner() {
    echo "======================================"
    echo "    数据查询中心 - Docker部署工具"
    echo "======================================"
    echo
}

# 检查系统要求
check_requirements() {
    print_info "检查系统要求..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查可用磁盘空间 (最少需要5GB)
    available_space=$(df . | tail -1 | awk '{print $4}')
    required_space=5242880  # 5GB in KB
    
    if [ $available_space -lt $required_space ]; then
        print_warning "可用磁盘空间不足5GB，可能影响部署"
    fi
    
    print_success "系统要求检查通过"
}

# 检查环境配置
check_env_config() {
    print_info "检查环境配置..."
    
    if [ ! -f ".env" ]; then
        if [ -f "env.example" ]; then
            print_warning ".env文件不存在，正在从模板创建..."
            cp env.example .env
            print_warning "请编辑 .env 文件配置您的API密钥"
            print_warning "至少需要配置: GLM_API_KEY, TUSHARE_TOKEN, SECRET_KEY"
            
            read -p "是否现在编辑 .env 文件? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                ${EDITOR:-nano} .env
            fi
        else
            print_error "env.example文件不存在，无法创建环境配置"
            exit 1
        fi
    fi
    
    # 检查关键配置项
    missing_keys=()
    
    if ! grep -q "GLM_API_KEY=.*[^_]" .env; then
        missing_keys+=("GLM_API_KEY")
    fi
    
    if ! grep -q "SECRET_KEY=.*[^-]" .env; then
        missing_keys+=("SECRET_KEY")
    fi
    
    if [ ${#missing_keys[@]} -gt 0 ]; then
        print_warning "以下配置项可能需要设置："
        for key in "${missing_keys[@]}"; do
            echo "  - $key"
        done
        
        read -p "是否继续部署? (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    print_success "环境配置检查完成"
}

# 检查端口占用
check_ports() {
    print_info "检查端口占用..."
    
    if lsof -i :80 &> /dev/null; then
        print_warning "端口80已被占用"
        lsof -i :80
        
        read -p "是否继续部署? (可能会导致端口冲突) (y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
    
    print_success "端口检查完成"
}

# 准备数据目录
prepare_data_directory() {
    print_info "准备数据目录..."
    
    if [ ! -d "data" ]; then
        mkdir -p data
        print_info "创建数据目录: data/"
    fi
    
    # 设置权限
    chmod 755 data/
    
    print_success "数据目录准备完成"
}

# 构建和启动服务
deploy_services() {
    print_info "开始构建Docker镜像..."
    
    # 构建镜像
    if ! docker-compose build; then
        print_error "Docker镜像构建失败"
        exit 1
    fi
    
    print_success "Docker镜像构建完成"
    
    print_info "启动服务..."
    
    # 启动服务
    if ! docker-compose up -d; then
        print_error "服务启动失败"
        exit 1
    fi
    
    print_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    print_info "等待服务启动..."
    
    max_attempts=30
    attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s http://localhost/health > /dev/null 2>&1; then
            print_success "服务已就绪"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    print_error "服务启动超时，请检查日志"
    docker-compose logs --tail=20
    exit 1
}

# 显示部署结果
show_deployment_result() {
    echo
    echo "======================================"
    echo "         部署完成"
    echo "======================================"
    echo
    
    # 获取服务状态
    print_info "服务状态:"
    docker-compose ps
    
    echo
    print_info "访问地址:"
    echo "  本地访问: http://localhost"
    
    # 尝试获取外网IP
    external_ip=$(curl -s http://checkip.amazonaws.com 2>/dev/null || echo "未知")
    if [ "$external_ip" != "未知" ]; then
        echo "  外网访问: http://$external_ip"
    fi
    
    echo
    print_info "管理命令:"
    echo "  查看日志: docker-compose logs -f"
    echo "  停止服务: docker-compose down"
    echo "  重启服务: docker-compose restart"
    echo "  查看状态: docker-compose ps"
    
    echo
    print_success "部署成功完成！"
}

# 清理函数
cleanup_on_error() {
    print_error "部署过程中出现错误，正在清理..."
    docker-compose down 2>/dev/null || true
}

# 主函数
main() {
    # 设置错误处理
    trap cleanup_on_error ERR
    
    show_banner
    
    # 执行部署步骤
    check_requirements
    check_env_config
    check_ports
    prepare_data_directory
    deploy_services
    wait_for_services
    show_deployment_result
}

# 命令行参数处理
case "${1:-}" in
    "clean")
        print_info "清理Docker资源..."
        docker-compose down -v
        docker system prune -f
        print_success "清理完成"
        exit 0
        ;;
    "logs")
        docker-compose logs -f
        exit 0
        ;;
    "restart")
        print_info "重启服务..."
        docker-compose restart
        print_success "重启完成"
        exit 0
        ;;
    "status")
        docker-compose ps
        exit 0
        ;;
    "help"|"-h"|"--help")
        echo "使用方法: $0 [命令]"
        echo
        echo "命令:"
        echo "  (无参数)  执行完整部署"
        echo "  clean     清理Docker资源"
        echo "  logs      查看服务日志"
        echo "  restart   重启服务"
        echo "  status    查看服务状态"
        echo "  help      显示此帮助信息"
        exit 0
        ;;
esac

# 执行主函数
main 