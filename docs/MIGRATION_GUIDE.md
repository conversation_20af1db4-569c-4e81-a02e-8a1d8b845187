# Backend重构迁移指南

## 📋 概述

本指南帮助团队成员了解backend目录重构的变更，以及如何适配新的目录结构和导入方式。

## 🔄 主要变更

### 目录结构变更

| 旧路径 | 新路径 | 说明 |
|--------|--------|------|
| `backend/data_api.py` | `backend/apis/data_api.py` | 数据查询API |
| `backend/factor_api.py` | `backend/apis/factor_api.py` | 因子计算API |
| `backend/metadata_api.py` | `backend/apis/metadata_api.py` | 元数据API |
| `backend/data_manager.py` | `backend/core/data/managers/data_manager.py` | 数据管理器 |
| `backend/stock_metadata_manager.py` | `backend/core/data/managers/stock_metadata_manager.py` | 股票元数据管理 |
| `backend/financial_news_manager.py` | `backend/core/data/managers/financial_news_manager.py` | 财经新闻管理 |
| `backend/data_sources.py` | `backend/core/data/sources/data_sources.py` | 数据源管理 |
| `backend/factors.py` | `backend/core/analysis/factors/basic_factors.py` | 基础因子计算 |
| `backend/enhanced_factors.py` | `backend/core/analysis/factors/enhanced_factors.py` | 增强因子计算 |
| `backend/ml_models.py` | `backend/core/analysis/ml/models.py` | 机器学习模型 |
| `backend/scoring_system.py` | `backend/core/analysis/scoring/scoring_system.py` | 评分系统 |
| `backend/risk_management.py` | `backend/core/analysis/risk/risk_management.py` | 风险管理 |
| `backend/backtesting.py` | `backend/core/analysis/backtesting/backtesting.py` | 回测引擎 |
| `backend/divergence_detector.py` | `backend/core/analysis/technical/divergence_detector.py` | 背离检测 |
| `backend/market_scanner.py` | `backend/core/analysis/technical/market_scanner.py` | 市场扫描 |
| `backend/news_impact_analyzer.py` | `backend/services/news/news_impact_analyzer.py` | 新闻影响分析 |
| `backend/index_components.py` | `backend/services/market/index_components.py` | 指数成分股 |
| `backend/tasks/news_sync_scheduler.py` | `backend/services/tasks/news_sync_scheduler.py` | 新闻同步调度 |
| `backend/data/*.db` | `backend/core/data/storage/databases/*.db` | 数据库文件 |

### 导入语句变更

#### API模块导入
```python
# 旧方式
from backend.data_api import router as data_router
from backend.factor_api import factor_router
from backend.metadata_api import router as metadata_router

# 新方式
from backend.apis.data_api import router as data_router
from backend.apis.factor_api import factor_router
from backend.apis.metadata_api import router as metadata_router

# 或者使用统一导入
from backend.apis import data_router, factor_router, metadata_router
```

#### 数据管理模块导入
```python
# 旧方式
from backend.data_manager import init_data_manager, DataManager
from backend.stock_metadata_manager import get_metadata_manager
from backend.financial_news_manager import get_financial_news_manager
from backend.data_sources import get_data_source_manager

# 新方式
from backend.core.data.managers.data_manager import init_data_manager, DataManager
from backend.core.data.managers.stock_metadata_manager import get_metadata_manager
from backend.core.data.managers.financial_news_manager import get_financial_news_manager
from backend.core.data.sources.data_sources import get_data_source_manager

# 或者使用统一导入
from backend.core.data import init_data_manager, get_data_source_manager
```

#### 分析模块导入
```python
# 旧方式
from backend.factors import FactorCalculator, get_all_factors
from backend.enhanced_factors import FactorManager
from backend.ml_models import MLModelManager
from backend.scoring_system import FactorScorer, StockRanker
from backend.risk_management import get_risk_analyzer
from backend.backtesting import get_backtest_engine

# 新方式
from backend.core.analysis.factors.basic_factors import FactorCalculator, get_all_factors
from backend.core.analysis.factors.enhanced_factors import FactorManager
from backend.core.analysis.ml.models import MLModelManager
from backend.core.analysis.scoring.scoring_system import FactorScorer, StockRanker
from backend.core.analysis.risk.risk_management import get_risk_analyzer
from backend.core.analysis.backtesting.backtesting import get_backtest_engine

# 或者使用统一导入
from backend.core.analysis import (
    FactorCalculator, get_all_factors, MLModelManager, 
    FactorScorer, get_risk_analyzer, get_backtest_engine
)
```

#### 服务模块导入
```python
# 旧方式
from backend.news_impact_analyzer import NewsImpactAnalyzer
from backend.index_components import get_index_stocks
from backend.tasks.news_sync_scheduler import start_news_scheduler

# 新方式
from backend.services.news.news_impact_analyzer import NewsImpactAnalyzer
from backend.services.market.index_components import get_index_stocks
from backend.services.tasks.news_sync_scheduler import start_news_scheduler

# 或者使用统一导入
from backend.services import NewsImpactAnalyzer, get_index_stocks, start_news_scheduler
```

## 🚀 启动方式变更

### 开发环境启动
```bash
# 旧方式
python backend/server.py
# 或
python -m uvicorn main:app --reload

# 新方式
python -m uvicorn backend.server:app --reload
# 或使用Makefile
make serve
```

### 生产环境启动
```bash
# 新方式
python -m uvicorn backend.server:app --host 0.0.0.0 --port 8000
```

## 📁 数据库路径变更

### 数据库文件位置
```python
# 旧路径
/backend/data/financial_data.db
/backend/data/financial_news.db
/backend/data/users.db

# 新路径
/backend/core/data/storage/databases/financial_data.db
/backend/core/data/storage/databases/financial_news.db
/backend/core/data/storage/databases/users.db
```

### 代码中的路径更新
```python
# 旧方式
db_path = "backend/data/financial_data.db"

# 新方式
db_path = "backend/core/data/storage/databases/financial_data.db"
```

## 🔧 配置文件变更

### Docker配置
- `Dockerfile`: 更新了数据目录挂载路径
- `docker-compose.yml`: 更新了卷挂载配置

### CI/CD配置
- `Makefile`: 更新了serve和coverage命令
- `pyproject.toml`: 更新了测试覆盖率路径

## 📝 脚本使用变更

### 初始化脚本
```bash
# 股票元数据初始化
python backend/scripts/init_stock_metadata.py

# 股票元数据管理
python backend/scripts/manage_stock_metadata.py

# 数据库更新
python backend/scripts/update_stock_database_tushare.py
```

### 测试脚本
```bash
# 运行重构验证测试
python backend/scripts/test_scripts/test_refactored_system.py

# 运行基础功能测试
python backend/scripts/test_scripts/test_basic_functionality.py
```

## ⚠️ 注意事项

### 1. 导入路径更新
- 所有旧的导入路径都需要更新为新路径
- 建议使用IDE的全局搜索替换功能批量更新

### 2. 数据库文件迁移
- 现有的数据库文件需要移动到新位置
- 或者更新代码中的数据库路径配置

### 3. 配置文件检查
- 检查所有配置文件中的路径引用
- 更新环境变量和配置项

### 4. 测试验证
- 运行测试套件确保功能正常
- 检查API接口是否正常工作

## 🔍 常见问题

### Q: 导入错误 "No module named 'backend.xxx'"
**A:** 检查导入路径是否已更新为新的目录结构。

### Q: 数据库文件找不到
**A:** 确认数据库文件已移动到新位置，或更新代码中的路径配置。

### Q: API接口无法访问
**A:** 确认服务器启动命令使用了新的模块路径。

### Q: Docker构建失败
**A:** 检查Dockerfile中的路径配置是否正确。

## 📞 支持

如果在迁移过程中遇到问题，请：
1. 查看本指南的常见问题部分
2. 运行测试脚本验证功能
3. 联系开发团队获取支持

## 🎯 快速迁移检查清单

### 开发者迁移步骤
- [ ] 更新所有导入语句
- [ ] 移动或更新数据库文件路径
- [ ] 更新启动脚本和命令
- [ ] 运行测试验证功能
- [ ] 更新个人开发环境配置

### 项目维护者迁移步骤
- [ ] 更新CI/CD配置
- [ ] 更新Docker配置
- [ ] 更新部署脚本
- [ ] 更新文档和README
- [ ] 通知团队成员

## 📚 相关文档

- [架构文档](./ARCHITECTURE.md)
- [API文档](./API.md)
- [开发指南](./DEVELOPMENT.md)
