# 进度条与消息显示 bug.md

## Bug Description
The frontend application is experiencing issues with displaying the final report and updating the workflow progress bar.
- After the `final_report` and `workflow_complete` messages are received from the backend, the final report content disappears from the chat interface.
- The dynamic progress bar (WorkflowProgress component) does not update or display the progress of the AI workflow in real-time.

## Root Cause (Hypothesis)
The `useChatStreaming` hook or its integrated components might have issues with:
1.  **State Management**: Incorrectly updating or clearing messages/workflow progress state after the stream completes.
2.  **Rendering Logic**: Conditional rendering in `AIAgentMode` or `ChatGPTMessage` components that inadvertently hides completed messages or progress indicators.
3.  **Typewriter Effect Completion**: The typewriter effect's completion logic might be interfering with the final message display or status.

## TODO List:

### Phase 1: Verify Message Persistence and Rendering
- [ ] **Task 1.1: Verify `final_report` persistence in `useChatStreaming` state.**
    - [ ] Inspect the `handleStreamData` function in `frontend/src/hooks/useChatStreaming.ts` for the `final_report` case.
    - [ ] Ensure that `updateMessageContent` is correctly called with `isComplete: true` and that the `content` is fully accumulated.
    - [ ] Add temporary `console.log` statements to `setStreamingState` calls within `handleStreamData` for `final_report` and `done` types to confirm messages array state.
- [ ] **Task 1.2: Debug `ChatGPTMessage` rendering for completed messages.**
    - [ ] Confirm that `ChatGPTMessage.tsx` correctly renders `message.content` or `message.displayContent` for messages with `status: 'complete'`.
    - [ ] Temporarily disable the `isStreaming` check in `ChatGPTMessage` to ensure content is always rendered after completion.
- [ ] **Task 1.3: Analyze message lifecycle and status transitions.**
    - [ ] Trace how message `status` transitions from `streaming` to `complete` and `isStreaming` changes to `false`.
    - [ ] Ensure no subsequent state updates are inadvertently removing or clearing the final message.

### Phase 2: Debug Workflow Progress Display
- [ ] **Task 2.1: Verify `workflowProgress` state updates.**
    - [ ] Add `console.log` statements in `handleStreamData` for `workflow_progress` type to confirm `workflowProgress` state is being updated in `useChatStreaming`.
    - [ ] Check if `workflowProgress.isActive` and `workflowProgress.progress` are being set correctly.
- [ ] **Task 2.2: Inspect `WorkflowProgress` component visibility.**
    - [ ] Examine `AIAgentMode.tsx` to ensure `WorkflowProgress` component is conditionally rendered correctly based on `workflowProgress && workflowProgress.isActive`.
    - [ ] Verify there are no CSS properties (e.g., `display: none`, `opacity: 0`) that might be hiding the component.
- [ ] **Task 2.3: Ensure `workflow_complete` properly clears/resets progress.**
    - [ ] In `handleStreamData` for `workflow_complete` or `done` type, ensure `workflowProgress` is either set to `null` or `isActive: false` and `progress: 100`.

### Phase 3: Clean Up and Verify
- [ ] **Task 3.1: Remove all temporary `console.log` statements** after confirming fixes.
- [ ] **Task 3.2: Thoroughly test chat functionality** with various prompts (simple questions, chart requests, long reports, error scenarios).
- [ ] **Task 3.3: Verify that the final report remains visible** after the conversation completes.
- [ ] **Task 3.4: Confirm the workflow progress bar updates dynamically** and correctly indicates completion. 