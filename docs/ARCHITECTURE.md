# Cash Flow Backend Architecture

## 概述

Cash Flow 后端采用分层架构设计，按功能模块组织代码，遵循 Python 最佳实践。重构后的架构提供了更好的可维护性、可扩展性和代码组织。

## 目录结构

```
backend/
├── server.py                    # 主服务器入口
├── requirements.txt             # 依赖管理
│
├── apis/                        # API接口层
│   ├── __init__.py
│   ├── data_api.py             # 数据查询API
│   ├── factor_api.py           # 因子计算API
│   ├── metadata_api.py         # 股票元数据API
│   └── routes/                 # 路由模块
│       ├── __init__.py
│       └── health.py           # 健康检查路由
│
├── core/                       # 核心业务逻辑层
│   ├── __init__.py
│   ├── data/                   # 数据管理模块
│   │   ├── __init__.py
│   │   ├── managers/           # 数据管理器
│   │   │   ├── __init__.py
│   │   │   ├── data_manager.py
│   │   │   ├── stock_metadata_manager.py
│   │   │   └── financial_news_manager.py
│   │   ├── sources/            # 数据源
│   │   │   ├── __init__.py
│   │   │   └── data_sources.py
│   │   └── storage/            # 数据存储
│   │       ├── __init__.py
│   │       ├── abstract_data_manager.py
│   │       ├── user_aware_data_manager.py
│   │       └── databases/      # 数据库文件目录
│   │
│   ├── analysis/               # 分析计算模块
│   │   ├── __init__.py
│   │   ├── factors/            # 因子计算
│   │   │   ├── __init__.py
│   │   │   ├── basic_factors.py      # 基础因子
│   │   │   └── enhanced_factors.py   # 增强因子
│   │   ├── ml/                 # 机器学习
│   │   │   ├── __init__.py
│   │   │   └── models.py       # ML模型
│   │   ├── scoring/            # 评分系统
│   │   │   ├── __init__.py
│   │   │   └── scoring_system.py
│   │   ├── risk/               # 风险管理
│   │   │   ├── __init__.py
│   │   │   └── risk_management.py
│   │   ├── technical/          # 技术分析
│   │   │   ├── __init__.py
│   │   │   ├── divergence_detector.py
│   │   │   └── market_scanner.py
│   │   └── backtesting/        # 回测
│   │       ├── __init__.py
│   │       └── backtesting.py
│   │
│   └── models/                 # 数据模型
│       └── ...
│
├── services/                   # 服务层
│   ├── __init__.py
│   ├── news/                   # 新闻相关服务
│   │   ├── __init__.py
│   │   └── news_impact_analyzer.py
│   ├── market/                 # 市场相关服务
│   │   ├── __init__.py
│   │   └── index_components.py
│   └── tasks/                  # 后台任务
│       ├── __init__.py
│       └── news_sync_scheduler.py
│
├── ai/                         # AI智能体系统
│   └── ...
│
├── auth/                       # 用户认证系统
│   └── ...
│
├── utils/                      # 工具函数
│   └── ...
│
├── scripts/                    # 脚本文件
│   ├── __init__.py
│   ├── init_stock_metadata.py
│   ├── manage_stock_metadata.py
│   └── test_scripts/           # 测试脚本
│       ├── __init__.py
│       └── test_refactored_system.py
│
├── migrations/                 # 数据库迁移
│   └── ...
│
└── logs/                       # 日志文件目录
    └── ...
```

## 架构层次

### 1. API接口层 (`apis/`)

负责处理HTTP请求，提供RESTful API接口。

- **data_api.py**: 股票数据查询接口
- **factor_api.py**: 因子计算接口  
- **metadata_api.py**: 股票元数据接口

### 2. 核心业务逻辑层 (`core/`)

系统的核心功能实现，分为数据管理和分析计算两大模块。

#### 数据管理模块 (`core/data/`)

- **managers/**: 数据管理器，负责数据的获取、存储和管理
- **sources/**: 数据源管理，统一各种外部数据源的接入
- **storage/**: 数据存储抽象层，提供数据库操作接口

#### 分析计算模块 (`core/analysis/`)

- **factors/**: 因子计算引擎，支持技术、基本面、量化因子
- **ml/**: 机器学习模型管理和预测
- **scoring/**: 股票评分和排名系统
- **risk/**: 风险管理和分析
- **technical/**: 技术分析工具
- **backtesting/**: 策略回测引擎

### 3. 服务层 (`services/`)

业务服务和后台任务管理。

- **news/**: 新闻分析服务
- **market/**: 市场数据服务
- **tasks/**: 后台任务调度

### 4. 支撑层

- **ai/**: AI智能体系统
- **auth/**: 用户认证和权限管理
- **utils/**: 通用工具函数
- **scripts/**: 管理和初始化脚本

## 设计原则

### 1. 分层架构
- 清晰的层次划分，每层职责单一
- 上层依赖下层，避免循环依赖
- 接口与实现分离

### 2. 模块化设计
- 按功能领域分组
- 高内聚，低耦合
- 便于单元测试和维护

### 3. 可扩展性
- 新功能可以轻松添加到对应模块
- 支持插件化扩展
- 配置驱动的架构

### 4. 数据流向

```
HTTP请求 → API层 → 核心业务层 → 数据层 → 外部数据源
         ↓
    服务层 ← 后台任务 ← 调度器
```

## 关键组件

### 数据管理器 (DataManager)
- 统一的数据访问接口
- 支持多种数据源（Tushare、AkShare等）
- 数据缓存和持久化

### 因子计算引擎 (FactorCalculator)
- 支持100+种技术和基本面因子
- 可扩展的因子框架
- 高性能计算优化

### 评分系统 (ScoringSystem)
- 多维度股票评分
- 机器学习增强评分
- 动态权重调整

### 风险管理 (RiskManagement)
- VaR/CVaR计算
- 回撤分析
- 风险监控预警

## 部署和运维

### 启动服务
```bash
# 开发环境
python -m uvicorn backend.server:app --reload

# 生产环境
python -m uvicorn backend.server:app --host 0.0.0.0 --port 8000
```

### 数据库初始化
```bash
python backend/scripts/init_stock_metadata.py
```

### 后台任务
系统支持自动启动后台任务，包括：
- 新闻数据同步
- 股票元数据更新
- 因子数据计算

## 扩展指南

### 添加新的因子
1. 在 `core/analysis/factors/` 中实现因子计算逻辑
2. 在 `FactorCalculator` 中注册新因子
3. 更新因子元数据

### 添加新的数据源
1. 在 `core/data/sources/` 中实现数据源适配器
2. 在 `DataSourceManager` 中注册新数据源
3. 更新配置文件

### 添加新的API接口
1. 在 `apis/` 中创建新的API模块
2. 在 `server.py` 中注册路由
3. 添加相应的测试用例
