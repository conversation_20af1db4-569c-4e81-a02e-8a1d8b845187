# Frontend AI Function Optimization - TODO List

## Project Overview
Comprehensive upgrade of the frontend AI chat functionality to fully integrate with the backend multi-agent AI workflow system, providing advanced message rendering, streaming responses, and enhanced user experience.

## Current Status Assessment
- ✅ Backend AI workflow system is complete and functional
- ✅ Frontend AI Agent mode with enhanced streaming functionality
- ✅ Complete integration with backend `/chat/stream` endpoint
- ⚠️ Markdown rendering integrated (chart display pending Phase 2)
- ✅ Advanced streaming response handling with typewriter effects
- ✅ Enhanced user experience with message management features

---

## Phase 1: Core Chat Functions Implementation
**Priority: HIGH** | **Dependencies: None** | **Estimated Time: 2-3 weeks**

### Task 1.1: Implement Basic Message Sending Function ✅
- [x] Create `useChatApi` Hook to handle API calls
- [x] Implement `sendMessage` function to connect to backend `/chat/stream` interface
- [x] Add message deduplication and ID generation logic
- [x] Implement Enter key sending and Ctrl+Enter line break functions
- [x] Add input validation and sanitization
- [x] Integrate with existing API client configuration

### Task 1.2: Implement Streaming Response Processing ✅
- [x] Integrate SSE (Server-Sent Events) streaming data processing
- [x] Implement real-time message updates and typewriter effect
- [x] Add pause/continue control for streaming responses
- [x] Handle network interruption and reconnection mechanism
- [x] Parse different streaming message types (text, chart, status)
- [x] Implement stream event handlers for workflow steps

### Task 1.3: Improve Message Status Management ✅
- [x] Implement message status (sending, sent, failed, retry)
- [x] Add message timestamp and source identification
- [x] Implement message editing and deletion functions
- [x] Add message persistent storage (LocalStorage)
- [x] Create message type definitions (TypeScript interfaces)
- [x] Implement optimistic UI updates

### Task 1.4: Error Handling and User Feedback ✅
- [x] Implement detailed error message classification display
- [x] Add network status detection and prompts
- [x] Implement message resending and retry mechanism
- [x] Add loading status and progress indicator
- [x] Create user-friendly error messages
- [x] Add fallback UI for connection failures

---

## Phase 2: Advanced Message Rendering System
**Priority: HIGH** | **Dependencies: Phase 1 completion** | **Estimated Time: 2-3 weeks**

### Task 2.1: Integrate Markdown Renderer ✅
- [x] Integrate existing `MarkdownRenderer` component into AI messages
- [x] Support complex formats (mathematical formulas, tables, links)
- [x] Implement code block syntax highlighting (Prism.js or highlight.js)
- [x] Add Markdown content copy and export functions
- [x] Ensure responsive Markdown rendering
- [x] Handle large Markdown content performance

### Task 2.2: Chart Data Visualization ✅
- [x] Integrate ECharts chart rendering into AI responses
- [x] Implement automatic display of K-line charts and technical indicators
- [x] Add chart interaction functions (zoom, select, download)
- [x] Support multiple chart types (line, bar, pie, candlestick)
- [x] Implement chart data parsing from backend responses
- [x] Add chart error handling and fallbacks

### Task 2.3: Message Type Classification Rendering ✅
- [x] Differentiate and process different message types (text, charts, tables, code)
- [x] Implement AI agent role identification (researcher, analyst, programmer)
- [x] Add message importance and priority identification
- [x] Implement message grouping and folding display
- [x] Create visual indicators for different content types
- [x] Add agent avatar and role badges

### Task 2.4: Responsive Layout Optimization ✅
- [x] Optimize mobile chat interface layout
- [x] Implement adaptive scrolling of message area
- [x] Add full-screen mode and compact mode switching
- [x] Optimize virtual scrolling for large message volumes
- [x] Ensure chart responsiveness on different screen sizes
- [x] Implement touch gestures for mobile interaction

### Task 2.5: Typography and Color System Implementation ✅
**Priority: HIGH** | **Dependencies: Task 2.1-2.4** | **Estimated Time: 4-5 days**
- [x] Implement comprehensive typography system with clear font hierarchy
- [x] Design and implement modern color palette with proper contrast ratios
- [x] Create consistent spacing and layout grid system using Tailwind utilities
- [x] Ensure WCAG accessibility compliance for color contrast and readability
- [x] Define semantic color tokens for different UI states (success, error, warning, info)
- [x] Implement responsive typography scaling for different screen sizes
- [x] Create typography utility classes for consistent text styling

### Task 2.6: Component Visual Design Enhancement ✅
**Priority: HIGH** | **Dependencies: Task 2.5** | **Estimated Time: 5-6 days**
- [x] Design professional chat message bubbles with proper visual distinction
- [x] Implement modern input field designs with focus states and validation styling
- [x] Design professional header and navigation components
- [x] Implement consistent button styles and interactive states
- [x] Add visual indicators for different message types (user, AI, system, error)
- [x] Create beautiful chart container designs with proper spacing and labels
- [x] Enhance component visual design with modern UI patterns (cards, shadows, borders)

### Task 2.7: Animation and Interaction Design ✅
**Priority: MEDIUM** | **Dependencies: Task 2.6** | **Estimated Time: 3-4 days**
- [x] Implement smooth animations and micro-interactions for better user experience
- [x] Create elegant loading states and skeleton screens
- [x] Implement modern scrollbar styling and smooth scrolling behavior
- [x] Add hover and focus animations for interactive elements
- [x] Create smooth transitions between different UI states
- [x] Implement progressive loading animations for charts and content

### Task 2.8: Layout and Visual Hierarchy Optimization ✅
**Priority: HIGH** | **Dependencies: Task 2.5** | **Estimated Time: 4-5 days**
- [x] Add subtle background patterns or gradients for visual depth
- [x] Implement proper visual hierarchy for different content sections
- [x] Create responsive breakpoint system for optimal viewing on all devices
- [x] Design elegant empty states and error message presentations
- [x] Add professional icons and visual elements throughout the interface
- [x] Optimize content spacing and alignment for better readability
- [x] Create cohesive design documentation and component style guide

---

## Phase 3: User Experience Functions
**Priority: MEDIUM** | **Dependencies: Phase 1-2 completion** | **Estimated Time: 3-4 weeks**

### Task 3.1: Preset Question Templates & Session Hydration ✅
- [ ] Create preset question library for investment analysis
- [ ] Implement question classification (technical, fundamental, market research)
- [ ] Add question search and filtering functions
- [ ] Implement user-defined question templates
- [ ] Create category-based question organization
- [ ] Add quick-select question buttons
- [x] Persist full message objects (including displayContent, status === complete) to LocalStorage/IndexedDB upon every state update
- [x] On component mount, hydrate conversation state: load stored messages and skip SSE initiation when messages are already complete
- [x] Add a `hydrated` boolean flag in `useChatStreaming` context to guarantee rendering before any new network calls
- [x] Provide a "Refresh from server" button that clears the hydration cache and replays the last conversation on demand
- [x] Unit-test: reload page and verify no network traffic while messages appear instantly

### Task 3.2: Conversation History Management
- [ ] Implement conversation session saving and loading
- [ ] Add historical conversation search and filtering
- [ ] Implement conversation export (JSON, Markdown, PDF)
- [ ] Add conversation sharing and collaboration features
- [ ] Create conversation metadata management
- [ ] Implement conversation pagination for performance

### Task 3.3: Intelligent Input & Interaction Enhancements
- [ ] Implement input auto-completion and suggestions
- [ ] Add intelligent stock code recognition and prompts
- [ ] Implement multi-line input and rich text editing
- [ ] Add voice input support (optional)
- [ ] Create smart suggestions based on context
- [ ] Implement command shortcuts for power users
- [x] In VirtualScrolling / `useAutoScroll` hook, detect user-initiated scroll events (wheel/touch/drag) and set `autoScrollPaused = true`
- [x] While `autoScrollPaused`, stop automatic `scrollToBottom` until one second after the last user scroll
- [x] Add a visible "Jump to Latest ↓" floating button when `autoScrollPaused` is true and streaming is active
- [x] Ensure the pause state persists across multiple streaming chunks and resumes only when the user clicks the button or reaches the bottom
- [x] E2E-test: drag scrollbar during streaming, ensure stream continues while viewport stays fixed; click "Jump to Latest" and verify auto-scroll resumes

### Task 3.4: Personalization Settings
- [ ] Implement theme switching (dark/light mode)
- [ ] Add font size and chat layout customization
- [ ] Implement AI response speed and detail settings
- [ ] Add notification and reminder functions
- [ ] Create user preference persistence
- [ ] Add accessibility options configuration

---

## Phase 4: Deep AI Workflow Integration
**Priority: MEDIUM** | **Dependencies: Backend AI workflow understanding** | **Estimated Time: 2-3 weeks**

### Task 4.1: Multi-Agent Collaboration Visualization
- [ ] Display current active AI agent status
- [ ] Implement workflow execution progress visualization
- [ ] Add avatars and logos for each agent role
- [ ] Display collaboration relationship diagram between agents
- [ ] Show agent handoff transitions
- [ ] Implement real-time agent activity indicators

### Task 4.2: Structured Display of Analysis Results
- [ ] Implement comparative display of bullish/bearish views
- [ ] Add highlighting of investment recommendations
- [ ] Implement embedded display of technical analysis charts
- [ ] Add eye-catching risk warning labels
- [ ] Create structured report card layouts
- [ ] Implement expandable/collapsible sections

### Task 4.3: Real-Time Analysis Status Feedback
- [ ] Display backend data acquisition and analysis progress
- [ ] Implement real-time status updates of analysis steps
- [ ] Add estimated completion time and current step prompts
- [ ] Implement cancel and pause functions for analysis process
- [ ] Show data source indicators
- [ ] Add progress bars for long-running analyses

### Task 4.4: AI Configuration and Tuning
- [ ] Implement frontend configuration interface for AI analysis parameters
- [ ] Add agent selection and priority settings
- [ ] Implement custom control of analysis depth and breadth
- [ ] Add AI model performance monitoring and statistics
- [ ] Create advanced user settings panel
- [ ] Implement configuration presets for different use cases

---

## Phase 5: Performance Optimization and Testing
**Priority: LOW** | **Dependencies: Core functionality completion** | **Estimated Time: 2-3 weeks**

### Task 5.1: Performance Optimization
- [ ] Implement virtual scrolling optimization for message lists
- [ ] Add lazy loading of images and charts
- [ ] Optimize rendering performance for large data volumes
- [ ] Implement memory usage monitoring and cleanup
- [ ] Add performance metrics collection
- [ ] Optimize bundle size and loading times

### Task 5.2: Error Monitoring and Logging
- [ ] Integrate frontend error monitoring and reporting
- [ ] Implement detailed user operation logging
- [ ] Add user feedback mechanism for AI response quality
- [ ] Implement A/B testing framework for function optimization
- [ ] Add analytics for feature usage
- [ ] Create debugging tools for development

### Task 5.3: Automated Testing
- [ ] Write unit tests for chat functions
- [ ] Implement integration tests for AI responses
- [ ] Add E2E automated tests for user interfaces
- [ ] Implement performance benchmarks and regression tests
- [ ] Create visual regression testing
- [ ] Add accessibility testing automation

### Task 5.4: Documentation and Deployment
- [ ] Improve user documentation for AI functions
- [ ] Create developer API documentation and integration guides
- [ ] Implement automated deployment of CI/CD pipelines
- [ ] Add monitoring and alerting systems
- [ ] Create troubleshooting guides
- [ ] Document configuration and customization options

---

## Phase 6: Progress Bar Enhancement
**Priority: MEDIUM** | **Dependencies: Phase 1 completion** | **Estimated Time: 1 week**

### Task 6.1: Implement Detailed Progress Indication ✅
- [x] Display real-time percentage progress for ongoing operations
- [x] Show current step or phase name within the progress bar
- [x] Implement estimated time remaining display
- [x] Add visual cues for completed sub-tasks

### Task 6.2: Enhance Progress Bar UI/UX ✅
- [x] Design modern and aesthetically pleasing progress bar styles (e.g., linear, circular)
- [x] Implement smooth animation for progress updates
- [x] Add different color schemes for various states (e.g., default, success, error)
- [x] Ensure responsiveness across different screen sizes

### Task 6.3: Improve Accessibility ✅
- [x] Add ARIA attributes to the progress bar for screen reader compatibility
- [x] Ensure sufficient color contrast for readability
- [x] Provide alternative text descriptions for visual cues

### Task 6.4: Integrate with Backend Status ✅
- [x] Map backend workflow steps to frontend progress bar stages
- [x] Handle backend errors or interruptions gracefully with appropriate progress bar states
- [x] Implement retry/cancel options visible on the progress bar for long-running tasks

---

## Phase 7: Multi-Agent Workflow Optimization
**Priority: HIGH** | **Dependencies: Phase 1-2 completion** | **Estimated Time: 3-4 weeks**

### Task 7.1: Workflow Analysis and Design
**Priority: HIGH** | **Dependencies: None** | **Estimated Time: 3-4 days**
- [ ] Analyze current multi-agent workflow redundancies and bottlenecks
- [ ] Design optimized workflow with dedicated specialized agents
- [ ] Create workflow flowchart and agent interaction diagrams
- [ ] Document data flow between new specialized agents
- [ ] Define clear responsibilities for each specialized agent
- [ ] Map existing tools to appropriate specialized agents

### Task 7.2: ENUM & MODEL CONSOLIDATION (Validation-Error Fix) ✅
**Priority: CRITICAL** | **Dependencies: None** | **Estimated Time: 1-2 days**
- [x] **7.2.1** Locate every duplicate definition of StepType and Plan across the repo
- [x] **7.2.2** Select backend/ai/prompts/planner_model.py as the single source of truth
- [x] **7.2.3** Add missing enum members (NEWS_ANALYSIS, FUNDAMENTAL_ANALYSIS) to ALL copies temporarily
- [x] **7.2.4** Refactor imports to point to the canonical model location
- [x] **7.2.5** Delete or deprecate redundant copies (src/, src copy/ directories)
- [x] **7.2.6** Update unit & integration tests with correct import paths
- [ ] **7.2.7** Regenerate pydantic JSON schema if needed for frontend typing
- [x] **7.2.8** Update documentation & prompts with consistent step types
- [x] **7.2.9** Run full test suite & manual sanity check for workflow validation

### Task 7.3: Create Technical Analysis Agent
**Priority: HIGH** | **Dependencies: Task 7.2** | **Estimated Time: 5-6 days**
- [ ] Create new `technical_analysis_specialist_node` function
- [ ] Design specialized prompt template `technical_analysis_specialist.md`
- [ ] Configure agent to use technical indicator tools (`technical_indicators.py`, `divergence_analysis.py`)
- [ ] Integrate chart data and stock price analysis capabilities
- [ ] Add support for multiple timeframe analysis (daily, weekly, monthly)
- [ ] Implement comprehensive technical report generation

### Task 7.4: Create News Analysis Agent
**Priority: HIGH** | **Dependencies: Task 7.2** | **Estimated Time: 5-6 days**
- [ ] Create new `news_analysis_specialist_node` function
- [ ] Design specialized prompt template `news_analysis_specialist.md`
- [ ] Configure agent to use news tools (`stock_news.py`, `yahoo_finance_news.py`)
- [ ] Implement news sentiment analysis and market impact assessment
- [ ] Add capability to correlate news events with price movements
- [ ] Create news-based market timing and trend analysis

### Task 7.5: Create Fundamental Analysis Agent
**Priority: MEDIUM** | **Dependencies: Task 7.2, fundamental data verification** | **Estimated Time: 6-7 days**
- [ ] **CONDITIONAL**: Verify availability of fundamental data tools (earnings, financial ratios, balance sheets)
- [ ] Create new `fundamental_analysis_specialist_node` function (if data available)
- [ ] Design specialized prompt template `fundamental_analysis_specialist.md`
- [ ] Integrate with `backend/data_sources.py` for financial data access
- [ ] Implement financial ratio analysis and company valuation
- [ ] Add earnings analysis and financial health assessment
- [ ] **ALTERNATIVE**: If no fundamental data tools, merge with news agent for financial news analysis

### Task 7.6: Update Planner Agent
**Priority: HIGH** | **Dependencies: Task 7.3-7.5** | **Estimated Time: 3-4 days**
- [ ] Modify `planner.md` prompt template to route to specialized agents
- [ ] Update step type classifications for new agent categories
- [ ] Add logic to distribute tasks based on analysis type (technical/news/fundamental)
- [ ] Ensure planner can handle multi-agent coordination
- [ ] Update planning strategy to leverage specialized agent strengths

### Task 7.7: Refactor Graph Workflow Structure
**Priority: HIGH** | **Dependencies: Task 7.3-7.6** | **Estimated Time: 4-5 days**
- [ ] Update `backend/ai/graph/builder.py` to include new specialized nodes
- [ ] Remove old research routing from workflow (keep nodes for future use)
- [ ] Create new edge connections: planner → specialized agents → financial reports
- [ ] Update `research_team_node` routing logic for new workflow
- [ ] Preserve existing `researcher`, `coder`, `technical_analyst` nodes (unused but available)
- [ ] Test new workflow connections and validate state transitions

### Task 7.8: Update Bullish/Bearish Reporter Integration
**Priority: HIGH** | **Dependencies: Task 7.7** | **Estimated Time: 3-4 days**
- [ ] Modify bullish reporter to receive data from all three specialized agents
- [ ] Modify bearish reporter to receive data from all three specialized agents
- [ ] Update reporter prompt templates to process multi-source analysis
- [ ] Ensure reporters can handle technical + news + fundamental data integration
- [ ] Add data source attribution in reports
- [ ] Test report quality with new multi-agent inputs

### Task 7.9: Create Comprehensive Unit Tests
**Priority: MEDIUM** | **Dependencies: Task 7.7-7.8** | **Estimated Time: 4-5 days**
- [ ] Write unit tests for new technical analysis specialist agent
- [ ] Write unit tests for new news analysis specialist agent
- [ ] Write unit tests for fundamental analysis specialist agent (if created)
- [ ] Create integration tests for new workflow routing
- [ ] Test specialized agent prompt templates with various inputs
- [ ] Validate state management across new agent transitions
- [ ] Create mock data for testing agent interactions

### Task 7.10: Update Documentation and Deployment
**Priority: MEDIUM** | **Dependencies: Task 7.9** | **Estimated Time: 3-4 days**
- [ ] Update `README.md` with new workflow architecture
- [ ] Create documentation for new specialized agents
- [ ] Document agent responsibilities and data flow
- [ ] Update API documentation for new workflow endpoints
- [ ] Create troubleshooting guide for new agent system
- [ ] Update deployment scripts to include new agent components

### Task 7.11: Performance Testing and Optimization
**Priority: LOW** | **Dependencies: Task 7.10** | **Estimated Time: 2-3 days**
- [ ] Benchmark new workflow performance vs. old system
- [ ] Optimize agent transition times and memory usage
- [ ] Test parallel agent execution capabilities
- [ ] Monitor resource consumption of specialized agents
- [ ] Create performance metrics dashboard
- [ ] Implement caching strategies for repeated analyses

---

## Phase 8: Complete Frontend Redesign with shadcn/ui
**Priority: HIGH** | **Dependencies: Modern UI/UX Requirements** | **Estimated Time: 8-10 weeks**

### Overview
Complete transformation of the frontend to use shadcn/ui components and adopt a modern design philosophy. This phase involves redesigning all UI components, implementing a consistent design system, and modernizing the user experience while maintaining all existing functionality.

### Task 8.1: Foundation Setup and Configuration
**Priority: CRITICAL** | **Dependencies: None** | **Estimated Time: 3-4 days**
- [x] **8.1.1** Install shadcn/ui with React 19 compatibility flags (`--legacy-peer-deps`)
- [x] **8.1.2** Configure `components.json` with custom theme tokens and component paths
- [x] **8.1.3** Set up Tailwind CSS configuration file (`tailwind.config.ts`)
- [x] **8.1.4** Install core shadcn/ui components: Button, Input, Card, Sheet, Dialog, Toast, Progress, Badge, Avatar, Separator, Skeleton
- [x] **8.1.5** Remove conflicting dependencies and custom CSS files
- [x] **8.1.6** Update `globals.css` with shadcn/ui theme tokens and design system variables
- [x] **8.1.7** Implement ThemeProvider for dark/light mode switching
- [x] **8.1.8** Configure proper font loading with Geist fonts

### Task 8.2: Core Layout Components ✅
- [x] **8.2.1** Create `AppSidebar.tsx` component with shadcn/ui Sidebar
  - Navigation structure (Home, AI Chat, Data Query, Factor Management, etc.)
  - Collapsible sidebar functionality
  - Active state indicators and proper routing
- [x] **8.2.2** Build `AppLayout.tsx` wrapper component
  - SidebarProvider integration
  - Content area with proper spacing and responsive design
  - Mobile-responsive sidebar overlay
- [x] **8.2.3** Implement `AppHeader.tsx` with modern design
  - Breadcrumb navigation using shadcn/ui Breadcrumb
  - User avatar with dropdown menu
  - Theme toggle button
  - Search functionality placeholder
- [x] **8.2.4** Create `Breadcrumbs.tsx` component
  - Dynamic breadcrumb generation based on route
  - Proper navigation state management
- [x] **8.2.5** Update `layout.tsx` to use new layout components

### Task 8.3: Chat Interface Complete Redesign
**Priority: HIGH** | **Dependencies: Task 8.2** | **Estimated Time: 6-7 days**
- [ ] **8.3.1** Create `ChatContainer.tsx` with shadcn/ui Card
  - Replace existing chat layout with modern design
  - Proper header, content, and input areas
  - ScrollArea component for message list
  - Responsive design for mobile/desktop
- [ ] **8.3.2** Build modern `MessageBubble.tsx` components
  - User vs AI message distinction with proper styling
  - Avatar, timestamp, and status indicators
  - Message actions (copy, retry, delete) with proper UI
- [ ] **8.3.3** Rewrite `StreamingMessage.tsx` component
  - shadcn/ui components integration
  - Typewriter effect with smooth animations
  - Skeleton loading states during streaming
  - Progress indicators for workflow steps
- [ ] **8.3.4** Implement `ChatInput.tsx` with shadcn/ui Textarea
  - Auto-resize functionality
  - Send button with loading states
  - Input validation and character limits
  - File upload capability (future enhancement)
- [ ] **8.3.5** Create `ChatControls.tsx` component
  - Stream control buttons (pause/resume/stop)
  - Clear conversation with confirmation dialog
  - Export conversation functionality
  - Settings dropdown menu

### Task 8.4: Data Visualization and Forms Redesign
**Priority: HIGH** | **Dependencies: Task 8.3** | **Estimated Time: 5-6 days**
- [ ] **8.4.1** Redesign `ChartContainer.tsx` with modern styling
  - shadcn/ui Card for chart containers
  - Proper chart headers with titles and controls
  - Export and fullscreen functionality
  - Responsive chart sizing
- [ ] **8.4.2** Update `ChartRenderer.tsx` with theming integration
  - Recharts integration with shadcn/ui theme
  - Chart type switching with Tabs component
  - Chart controls with Button and Select components
  - Error states and loading indicators
- [ ] **8.4.3** Create `StockSelector.tsx` with shadcn/ui Combobox
  - Search and autocomplete functionality
  - Recent selections and favorites
  - Validation and error states
- [ ] **8.4.4** Build `DateRangePicker.tsx` component
  - shadcn/ui Calendar component integration
  - Date range selection with presets (1W, 1M, 3M, 1Y)
  - Proper validation and error handling
- [ ] **8.4.5** Implement `DataTable.tsx` with modern design
  - shadcn/ui Table component
  - Sorting, filtering, and pagination
  - Row selection and bulk actions
  - Responsive table design

### Task 8.5: Mode-Specific Interface Redesign
**Priority: HIGH** | **Dependencies: Task 8.4** | **Estimated Time: 8-9 days**
- [ ] **8.5.1** Complete redesign of `AIAgentMode.tsx`
  - Agent selection with Select component
  - Workflow progress with Progress component
  - Agent status indicators with Badge
  - Modern form layouts and interactions
- [ ] **8.5.2** Rebuild `FactorManagementMode.tsx`
  - Factor panels with Card components
  - Factor creation forms with Dialog
  - Factor analysis charts with modern containers
  - Factor performance metrics display
- [ ] **8.5.3** Modernize `DataQueryMode.tsx`
  - Query interface with Form components
  - Technical indicators selection with Checkbox groups
  - Query results display with enhanced tables
  - Real-time data updates with loading states
- [ ] **8.5.4** Update `MLModelsMode.tsx`
  - Model selection and training interface
  - Progress indicators for training/prediction
  - Results visualization with modern charts
  - Model performance metrics display
- [ ] **8.5.5** Redesign `StockScoringMode.tsx`
  - Scoring interface with modern form components
  - Results display with Card and Badge components
  - Ranking visualization with enhanced tables
- [ ] **8.5.6** Modernize `DivergenceScannerMode.tsx`
  - Scanner interface with Select and Button components
  - Results display with modern card layouts
  - Historical data visualization
  - Scan progress indicators

### Task 8.6: Utility Components and Enhancements
**Priority: MEDIUM** | **Dependencies: Task 8.5** | **Estimated Time: 4-5 days**
- [ ] **8.6.1** Create comprehensive `LoadingStates.tsx`
  - Various Skeleton components for different content types
  - Spinner components for different contexts
  - Loading overlays with proper backdrop
  - Loading text and progress indicators
- [ ] **8.6.2** Build modern `ErrorBoundary.tsx`
  - Error display with Alert components
  - Error retry functionality with Button
  - User-friendly error messages
  - Error reporting capability
- [ ] **8.6.3** Implement `NotificationSystem.tsx`
  - shadcn/ui Toast integration
  - Different notification types (success, error, info, warning)
  - Action buttons in notifications
  - Notification queue management
- [ ] **8.6.4** Create enhanced `ProgressIndicators.tsx`
  - Multiple progress bar styles (linear, circular)
  - Step-based progress indicators
  - Workflow progress visualization
  - Time estimation displays

### Task 8.7: State Management and API Integration
**Priority: HIGH** | **Dependencies: Task 8.6** | **Estimated Time: 4-5 days**
- [ ] **8.7.1** Create centralized `AppContext.tsx`
  - Application state management
  - Theme context integration
  - Chat state context
  - API state management
- [ ] **8.7.2** Update all custom hooks in `hooks/` directory
  - Compatibility with new component structure
  - Proper loading and error states
  - Optimistic updates implementation
  - Cleanup and memory management
- [ ] **8.7.3** Implement enhanced error handling
  - Global error boundaries
  - API error handling with user feedback
  - Network status monitoring
  - Retry mechanisms with exponential backoff

### Task 8.8: Mobile Optimization and Accessibility
**Priority: MEDIUM** | **Dependencies: Task 8.7** | **Estimated Time: 3-4 days**
- [ ] **8.8.1** Ensure mobile-first responsive design
  - Proper breakpoints throughout the application
  - Mobile navigation with Sheet component
  - Touch-friendly interface elements
  - Mobile chart interactions
- [ ] **8.8.2** Implement comprehensive accessibility features
  - Proper ARIA labels and descriptions
  - Keyboard navigation support
  - Screen reader compatibility
  - High contrast mode support
  - Focus management and visual indicators

### Task 8.9: Performance Optimization
**Priority: MEDIUM** | **Dependencies: Task 8.8** | **Estimated Time: 3-4 days**
- [ ] **8.9.1** Implement code splitting and lazy loading
  - Dynamic imports for heavy components
  - Lazy load chart libraries and complex components
  - Bundle size optimization with tree shaking
  - Loading boundaries configuration
- [ ] **8.9.2** Memory and state optimization
  - Proper cleanup in useEffect hooks
  - Re-render optimization with useMemo and useCallback
  - State persistence configuration
  - Virtual scrolling for large datasets

### Task 8.10: Testing and Documentation
**Priority: LOW** | **Dependencies: Task 8.9** | **Estimated Time: 5-6 days**
- [ ] **8.10.1** Component testing suite
  - Unit tests for all new components
  - Integration tests for mode switching
  - E2E tests for critical user flows
  - Accessibility testing with axe-core
- [ ] **8.10.2** Comprehensive documentation
  - Component documentation with Storybook
  - Design system documentation
  - Migration guide from old components
  - Performance optimization guide
  - shadcn/ui customization guide

### Task 8.11: Migration and Cleanup
**Priority: HIGH** | **Dependencies: Task 8.10** | **Estimated Time: 2-3 days**
- [ ] **8.11.1** Remove old component files
  - Delete obsolete CSS files and components
  - Clean up unused dependencies
  - Update import statements throughout the codebase
- [ ] **8.11.2** Final integration testing
  - End-to-end functionality testing
  - Cross-browser compatibility testing
  - Performance regression testing
  - User acceptance testing

### Technical Implementation Notes
- **Design Philosophy**: Minimalist, accessible, responsive, performance-optimized
- **Component Library**: shadcn/ui with custom theming
- **Styling Approach**: Tailwind CSS v4 with design tokens
- **State Management**: React hooks with context providers
- **Accessibility**: WCAG 2.1 AA compliance
- **Performance**: Code splitting, lazy loading, virtual scrolling
- **Mobile Support**: Mobile-first responsive design
- **Testing**: Comprehensive unit, integration, and E2E testing

### Success Metrics
- **Design Consistency**: Unified shadcn/ui component usage throughout
- **Accessibility**: WCAG 2.1 AA compliance score >95%
- **Performance**: <3s initial load time, <200ms interaction responses
- **Mobile Experience**: Touch-friendly interface with proper responsive design
- **User Experience**: Intuitive navigation, clear visual hierarchy, smooth animations
- **Maintainability**: Reduced CSS complexity, reusable component library

---

## Technical Implementation Notes

### Key Dependencies and Architecture Decisions
- **Frontend Stack**: React 19 + TypeScript + Next.js 15 + Tailwind CSS v4 + shadcn/ui
- **Chart Library**: ECharts for visualization (integrating with shadcn/ui theming)
- **Markdown**: React Markdown for content rendering
- **State Management**: React Hooks + Context API + LocalStorage
- **API Integration**: SSE for streaming + Axios for HTTP requests

### Critical Integration Points
1. Backend `/chat/stream` endpoint for AI communication
2. Existing `MarkdownRenderer` component integration with shadcn/ui
3. Backend chart data format compatibility
4. Multi-agent workflow status synchronization
5. shadcn/ui theme system integration with existing design tokens

### New Workflow Architecture (Phase 7)
1. **Coordinator** → **Chart Generator** → **Planner**
2. **Planner** → **Specialized Agents** (Technical/News/Fundamental in parallel)
3. **Specialized Agents** → **Financial Report Coordinator**
4. **Financial Report Coordinator** → **Bullish Reporter** → **Bearish Reporter** → **Trading Advice Reporter**
5. **Trading Advice Reporter** → **Final Comprehensive Reporter** → **END**

### shadcn/ui Integration Strategy (Phase 8)
- **Component Migration**: Replace custom components with shadcn/ui equivalents
- **Theme System**: Integrate existing CSS variables with shadcn/ui design tokens
- **Accessibility**: Leverage shadcn/ui's built-in WCAG 2.1 AA compliance
- **Performance**: Utilize shadcn/ui's optimized component architecture
- **Customization**: Maintain brand consistency through custom theming

### Risk Mitigation Strategies
- Implement robust error handling and retry mechanisms
- Create fallback UIs for network failures
- Use progressive enhancement for advanced features
- Ensure graceful degradation on slower devices
- Maintain backward compatibility during migration
- Comprehensive testing at each migration step

---

## Implementation Schedule
- **Week 1-3**: Phase 1 (Core Chat Functions)
- **Week 4-6**: Phase 2 (Advanced Rendering)
- **Week 7-10**: Phase 3 (User Experience)
- **Week 11-13**: Phase 4 (AI Integration)
- **Week 14-16**: Phase 5 (Optimization & Testing)
- **Week 17**: Phase 6 (Progress Bar Enhancement)
- **Week 18-21**: Phase 7 (Multi-Agent Workflow Optimization)
- **Week 22-31**: Phase 8 (Complete Frontend Redesign with shadcn/ui)

**Total Estimated Duration: 31 weeks**
**Current Progress**: 
- ✅ Phase 1: Core Chat Functions (COMPLETED)
- ✅ Phase 2: Advanced Message Rendering System (COMPLETED)
- ✅ Phase 6: Progress Bar Enhancement (COMPLETED)
- ⚠️ Phase 3: User Experience Functions (IN PROGRESS - Task 3.1 & 3.3 partially complete)
- ⏳ Phase 4: Deep AI Workflow Integration (PENDING)
- ⏳ Phase 5: Performance Optimization and Testing (PENDING)
- ⏳ Phase 7: Multi-Agent Workflow Optimization (IN PROGRESS - 0% complete)
- 🆕 Phase 8: Complete Frontend Redesign with shadcn/ui (IN PROGRESS - 0% complete)

**Next Action**: Phase 2 - Data Isolation Implementation

# 用户认证和账户管理系统实现计划

## 项目技术架构分析

### 当前技术栈
**前端:**
- Next.js 15.3.3 (React 19.0.0)
- TypeScript
- Tailwind CSS 4.0
- Radix UI 组件库
- Axios (HTTP客户端)
- React Hook Form + Zod (表单验证)
- Lucide React (图标库)

**后端:**
- FastAPI (Python Web框架)
- Uvicorn (ASGI服务器)
- SQLite 数据库 (当前使用多个.db文件)
- Pandas + NumPy (数据处理)
- AkShare + Tushare (金融数据源)
- LangGraph + LangChain (AI工作流)

**数据存储:**
- SQLite数据库文件:
  - `data/financial_data.db` (股票数据)
  - `data/financial_news.db` (新闻数据)
  - `data/news_impact_analysis.db` (新闻影响分析)
  - `data/divergence_data.db` (背离数据)

## 详细实现任务清单

### 阶段1: 后端用户认证基础设施 (Backend Authentication Infrastructure) ✅

#### 1.1 数据库设计和迁移
- [x] 设计用户认证相关数据库表结构
  - [x] 创建 `users` 表 (用户基本信息)
  - [x] 创建 `user_sessions` 表 (会话管理)
  - [x] 创建 `user_preferences` 表 (用户偏好设置)
  - [x] 创建 `user_activity_logs` 表 (用户操作日志)
- [x] 修改现有数据表，添加 `user_id` 字段实现数据隔离
  - [x] 修改 `stock_info` 表添加用户关联
  - [x] 修改 `factor_data` 表添加用户关联
  - [x] 修改 `financial_news` 相关表添加用户关联
  - [x] 修改 `news_impact_analysis` 表添加用户关联
- [x] 创建数据库迁移脚本
  - [x] 编写迁移脚本处理现有数据
  - [x] 实现向后兼容的数据迁移策略

#### 1.2 认证依赖和工具
- [x] 安装和配置认证相关Python包
  - [x] 添加 `python-jose[cryptography]` (JWT处理)
  - [x] 添加 `passlib[bcrypt]` (密码哈希)
  - [x] 添加 `python-multipart` (表单数据处理)
- [x] 创建认证工具模块 `backend/auth/`
  - [x] 创建 `password_utils.py` (密码哈希和验证)
  - [x] 创建 `jwt_utils.py` (JWT令牌生成和验证)
  - [x] 创建 `auth_dependencies.py` (FastAPI依赖注入)

#### 1.3 用户模型和数据访问层
- [x] 创建用户相关Pydantic模型
  - [x] `UserCreate` (用户注册模型)
  - [x] `UserLogin` (用户登录模型)
  - [x] `UserResponse` (用户信息响应模型)
  - [x] `UserUpdate` (用户信息更新模型)
- [x] 创建用户数据访问层 `backend/auth/user_manager.py`
  - [x] 实现用户CRUD操作
  - [x] 实现用户认证逻辑
  - [x] 实现会话管理功能

#### 1.4 认证API端点
- [x] 创建认证路由 `backend/auth/auth_routes.py`
  - [x] `POST /auth/register` (用户注册)
  - [x] `POST /auth/login` (用户登录)
  - [x] `POST /auth/logout` (用户登出)
  - [x] `POST /auth/refresh` (刷新令牌)
  - [x] `GET /auth/me` (获取当前用户信息)
  - [x] `PUT /auth/me` (更新用户信息)
  - [x] `PUT /auth/password` (修改密码)

### 阶段2: 数据隔离机制实现 (Data Isolation Implementation) ✅

#### 2.1 修改现有数据管理器 ✅
- [x] 更新 `backend/data_manager.py`
  - [x] 添加用户上下文到所有数据操作
  - [x] 修改数据查询方法支持用户过滤
  - [x] 实现用户数据隔离逻辑
- [x] 更新 `backend/financial_news_manager.py`
  - [x] 添加用户关联到新闻数据
  - [x] 实现用户新闻数据隔离
- [x] 更新 `backend/news_impact_analyzer.py`
  - [x] 添加用户关联到分析数据
  - [x] 实现用户分析数据隔离

#### 2.2 修改API端点支持用户认证 ✅
- [x] 更新 `backend/server.py` 中的所有API端点
  - [x] 添加认证依赖到需要保护的端点
  - [x] 修改数据查询逻辑支持用户过滤
  - [x] 实现用户权限检查
- [x] 更新因子计算API (`/factors/*`)
  - [x] 添加用户上下文到因子计算
  - [x] 实现用户因子数据隔离
- [x] 更新股票数据API (`/stocks/*`)
  - [x] 添加用户上下文到股票查询
  - [x] 实现用户股票数据隔离
- [x] 更新新闻API (`/news/*`, `/financial-news/*`)
  - [x] 添加用户上下文到新闻查询
  - [x] 实现用户新闻数据隔离

#### 2.3 AI工作流用户上下文 ✅
- [x] 更新AI工作流管理器
  - [x] 修改 `backend/ai/workflow.py` 支持用户上下文
  - [x] 确保AI分析结果与用户关联
  - [x] 实现用户AI历史记录隔离

### 阶段3: 前端认证界面开发 (Frontend Authentication UI)

#### 3.1 认证相关组件 ✅
- [x] 创建登录组件 `frontend/src/components/auth/LoginForm.tsx`
  - [x] 实现用户名/邮箱和密码输入
  - [x] 添加"记住我"选项
  - [x] 添加表单验证和错误处理
- [x] 创建注册组件 `frontend/src/components/auth/RegisterForm.tsx`
  - [x] 实现用户注册表单
  - [x] 添加密码强度验证
  - [x] 添加邮箱验证
- [x] 创建认证布局 `frontend/src/components/auth/AuthLayout.tsx`
  - [x] 设计专业的认证页面布局
  - [x] 确保与现有设计风格一致
- [x] 创建用户菜单组件 `frontend/src/components/auth/UserMenu.tsx`
  - [x] 显示用户信息
  - [x] 提供登出功能
  - [x] 链接到用户设置页面

#### 3.2 认证页面 ✅
- [x] 创建登录页面 `frontend/src/app/login/page.tsx`
- [x] 创建注册页面 `frontend/src/app/register/page.tsx`
- [x] 创建忘记密码页面 `frontend/src/app/forgot-password/page.tsx`
- [x] 创建用户设置页面 `frontend/src/app/settings/page.tsx`
  - [x] 个人信息管理
  - [x] 密码修改
  - [x] 偏好设置

#### 3.3 认证状态管理 ✅
- [x] 创建认证上下文 `frontend/src/providers/AuthProvider.tsx`
  - [x] 管理用户登录状态
  - [x] 处理令牌存储和刷新
  - [x] 提供认证相关方法
- [x] 创建认证Hook (集成在AuthProvider中)
  - [x] 封装认证逻辑
  - [x] 提供登录、登出、注册方法
  - [x] 处理认证状态变化
- [x] 创建受保护路由组件 `frontend/src/components/auth/ProtectedRoute.tsx`
  - [x] 检查用户认证状态
  - [x] 重定向未认证用户到登录页
  - [x] 支持角色权限检查
  - [x] 提供加载状态和错误处理

### 阶段4: 前端集成和用户体验 (Frontend Integration & UX) ✅

#### 4.1 更新现有组件支持认证 ✅
- [x] 更新主应用组件 `frontend/src/app/page.tsx`
  - [x] 集成认证状态检查
  - [x] 添加ProtectedRoute保护
  - [x] 处理未认证用户的重定向
- [x] 更新应用布局 `frontend/src/app/layout.tsx`
  - [x] 添加AuthProvider到根布局
  - [x] 集成认证上下文
- [x] 更新登录和注册页面
  - [x] 集成AuthProvider的认证方法
  - [x] 支持returnUrl重定向功能

#### 4.2 API客户端更新 ✅
- [x] AuthProvider中实现API客户端
  - [x] 添加JWT令牌到请求头
  - [x] 实现令牌自动刷新
  - [x] 处理认证失败的响应
- [x] 认证状态管理
  - [x] 自动处理401未授权响应
  - [x] 实现自动重新认证机制

#### 4.3 用户体验优化 ✅
- [x] 实现登录状态持久化
  - [x] 使用localStorage/sessionStorage存储令牌
  - [x] 实现页面刷新后状态恢复
  - [x] 支持"记住我"功能
- [x] 添加加载状态和错误处理
  - [x] 登录/注册过程的加载指示器
  - [x] 友好的错误消息显示
  - [x] 认证加载骨架屏
- [x] 实现自动登出机制
  - [x] 令牌过期自动登出
  - [x] 定时刷新令牌机制

### 阶段5: 安全性和性能优化 (Security & Performance)

#### 5.1 安全性增强
- [ ] 实现密码安全策略
  - [ ] 密码复杂度要求
  - [ ] 密码历史记录防重复
  - [ ] 登录失败次数限制
- [ ] 添加安全中间件
  - [ ] CSRF保护
  - [ ] 请求频率限制
  - [ ] SQL注入防护
- [ ] 实现会话安全
  - [ ] 安全的令牌生成
  - [ ] 令牌过期和刷新机制
  - [ ] 设备/IP绑定验证

#### 5.2 性能优化
- [ ] 数据库查询优化
  - [ ] 添加必要的数据库索引
  - [ ] 优化用户数据查询性能
  - [ ] 实现查询结果缓存
- [ ] 前端性能优化
  - [ ] 实现组件懒加载
  - [ ] 优化认证状态检查
  - [ ] 减少不必要的API调用

### 阶段6: 测试和部署 (Testing & Deployment)

#### 6.1 后端测试
- [ ] 创建认证API测试
  - [ ] 单元测试认证逻辑
  - [ ] 集成测试API端点
  - [ ] 测试数据隔离功能
- [ ] 创建安全性测试
  - [ ] 测试认证绕过尝试
  - [ ] 测试数据泄露防护
  - [ ] 测试令牌安全性

#### 6.2 前端测试
- [ ] 创建认证组件测试
  - [ ] 测试登录/注册表单
  - [ ] 测试认证状态管理
  - [ ] 测试受保护路由
- [ ] 创建端到端测试
  - [ ] 测试完整认证流程
  - [ ] 测试用户数据隔离
  - [ ] 测试用户体验流程

#### 6.3 数据迁移和部署
- [ ] 准备生产环境迁移
  - [ ] 备份现有数据
  - [ ] 执行数据库迁移
  - [ ] 验证数据完整性
- [ ] 部署和监控
  - [ ] 部署认证系统
  - [ ] 监控系统性能
  - [ ] 收集用户反馈

### 阶段7: 文档和维护 (Documentation & Maintenance)

#### 7.1 技术文档
- [ ] 编写认证系统文档
  - [ ] API文档更新
  - [ ] 数据库架构文档
  - [ ] 安全策略文档
- [ ] 创建开发者指南
  - [ ] 认证集成指南
  - [ ] 数据隔离最佳实践
  - [ ] 故障排除指南

#### 7.2 用户文档
- [ ] 创建用户使用指南
  - [ ] 注册和登录指南
  - [ ] 账户管理指南
  - [ ] 隐私和安全说明

## 技术实现要点

### 数据库设计要点
1. **用户表结构**:
   ```sql
   CREATE TABLE users (
       id INTEGER PRIMARY KEY AUTOINCREMENT,
       username VARCHAR(50) UNIQUE NOT NULL,
       email VARCHAR(100) UNIQUE NOT NULL,
       password_hash VARCHAR(255) NOT NULL,
       full_name VARCHAR(100),
       is_active BOOLEAN DEFAULT TRUE,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

2. **数据隔离策略**:
   - 所有业务数据表添加 `user_id` 外键
   - 查询时自动添加用户过滤条件
   - 使用数据库级别的行级安全策略

### 认证流程设计
1. **JWT令牌策略**:
   - Access Token (短期，15分钟)
   - Refresh Token (长期，7天)
   - 自动刷新机制

2. **密码安全**:
   - bcrypt哈希算法
   - 盐值随机生成
   - 密码复杂度验证

### 前端状态管理
1. **认证上下文**:
   - 全局用户状态管理
   - 令牌自动管理
   - 认证状态同步

2. **路由保护**:
   - 高阶组件包装
   - 自动重定向
   - 权限级别检查

## 预期时间安排
- 阶段1-2: 2-3周 (后端基础设施)
- 阶段3-4: 2-3周 (前端界面和集成)
- 阶段5-6: 1-2周 (安全性和测试)
- 阶段7: 1周 (文档和部署)

**总计: 6-9周**

## 风险评估和缓解策略
1. **数据迁移风险**: 充分测试迁移脚本，准备回滚方案
2. **性能影响**: 逐步部署，监控系统性能
3. **用户体验**: 提供平滑的过渡期，保持向后兼容
4. **安全漏洞**: 进行安全审计，实施最佳实践

## 成功标准
1. 用户可以安全注册、登录、登出
2. 用户数据完全隔离，无数据泄露
3. 系统性能不受显著影响
4. 用户界面友好，操作流畅
5. 通过安全性测试和代码审查 