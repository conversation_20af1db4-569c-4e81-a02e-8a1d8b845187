# Docker部署指南

## 概述

本指南提供了数据查询中心项目的完整Docker化部署方案。项目采用单容器架构，包含前端React应用、后端FastAPI服务和Nginx反向代理。

## 系统架构

```
┌─────────────────────────────────────────┐
│              Docker容器                  │
├─────────────────────────────────────────┤
│  Nginx (端口80)                         │
│  ├── 静态文件服务 (前端)                 │
│  └── 反向代理 → 后端API                  │
├─────────────────────────────────────────┤
│  FastAPI后端 (端口8000)                 │
│  ├── AI工作流引擎                        │
│  ├── 金融数据分析                        │
│  └── 用户认证系统                        │
├─────────────────────────────────────────┤
│  SQLite数据库 (卷挂载)                   │
│  ├── 用户数据                            │
│  ├── 金融数据                            │
│  └── 新闻数据                            │
└─────────────────────────────────────────┘
```

## 环境要求

### 服务器要求
- **操作系统**: Linux (推荐Ubuntu 20.04+/CentOS 8+)
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **内存**: 最低2GB，推荐4GB+
- **磁盘**: 最低10GB可用空间
- **CPU**: 最低2核，推荐4核+

### 网络要求
- 确保80端口可访问
- 服务器可访问外部API (获取金融数据和AI服务)

## 快速部署

### 1. 获取项目代码

```bash
git clone <your-repository-url>
cd cash-flow
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp env.example .env

# 编辑环境变量配置
vim .env
```

**必须配置的关键API密钥:**

```bash
# 智谱GLM API (用于新闻影响分析)
GLM_API_KEY=your_actual_glm_api_key

# Tushare Pro API (用于股票数据)
TUSHARE_TOKEN=your_actual_tushare_token

# JWT密钥 (用于用户认证)
SECRET_KEY=your-random-secret-key-here
```

### 3. 构建和启动

```bash
# 构建并启动服务
docker-compose up -d

# 查看启动日志
docker-compose logs -f
```

### 4. 验证部署

```bash
# 检查容器状态
docker-compose ps

# 检查健康状态
curl http://localhost/health

# 访问应用
# 浏览器打开: http://your-server-ip
```

## 详细配置说明

### API密钥配置

#### 1. 智谱GLM API
```bash
GLM_API_KEY=your_glm_api_key_here
```
- **获取地址**: https://open.bigmodel.cn/
- **用途**: 新闻影响分析、AI对话
- **重要性**: 高 (影响核心AI功能)

#### 2. Tushare Pro API
```bash
TUSHARE_TOKEN=your_tushare_token_here
```
- **获取地址**: https://tushare.pro/
- **用途**: 股票数据获取
- **重要性**: 高 (影响数据分析功能)

#### 3. 可选API密钥
```bash
# OpenAI API (可选AI模型)
OPENAI_API_KEY=your_openai_api_key_here

# Alpha Vantage API (美股数据)
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here

# Tavily搜索API (智能搜索)
TAVILY_API_KEY=your_tavily_api_key_here
```

### 数据库配置

项目使用SQLite数据库，数据文件通过Docker卷挂载实现持久化：

```yaml
volumes:
  - ./data:/app/data  # 数据库文件目录
```

**数据库文件列表:**
- `financial_data.db` - 金融数据
- `financial_news.db` - 新闻数据
- `users.db` - 用户数据
- `news_impact_analysis.db` - 新闻影响分析
- `divergence_data.db` - 背离数据

### 性能调优

#### 1. 资源限制
在 `docker-compose.yml` 中添加资源限制：

```yaml
services:
  cash-flow:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '1.0'
```

#### 2. 后端并发配置
修改 `docker/supervisord.conf`:

```ini
[program:backend]
command=uvicorn backend.server:app --host 0.0.0.0 --port 8000 --workers 2
```

## 生产环境部署

### 1. HTTPS配置

使用Nginx SSL配置（需要SSL证书）：

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/private.key;
    
    # 其他配置...
}
```

### 2. 域名配置

更新 `docker/nginx.conf` 中的 `server_name`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    # ...
}
```

### 3. 防火墙配置

```bash
# Ubuntu/Debian
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=80/tcp
sudo firewall-cmd --permanent --add-port=443/tcp
sudo firewall-cmd --reload
```

## 管理操作

### 启动和停止

```bash
# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 查看状态
docker-compose ps
```

### 日志管理

```bash
# 查看所有日志
docker-compose logs

# 查看特定服务日志
docker-compose logs cash-flow

# 实时查看日志
docker-compose logs -f

# 查看容器内日志
docker exec -it cash-flow-app tail -f /var/log/supervisor/backend.log
```

### 数据备份

```bash
# 备份数据目录
tar -czf backup-$(date +%Y%m%d_%H%M%S).tar.gz data/

# 恢复数据
tar -xzf backup-YYYYMMDD_HHMMSS.tar.gz
```

### 更新应用

```bash
# 拉取最新代码
git pull

# 重新构建并启动
docker-compose up -d --build

# 清理旧镜像
docker image prune -f
```

## 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 查看详细错误信息
docker-compose logs cash-flow

# 检查配置文件语法
docker-compose config
```

#### 2. API密钥错误
- 检查 `.env` 文件中的API密钥格式
- 确认API密钥有效性和权限
- 查看应用日志中的具体错误信息

#### 3. 数据库权限问题
```bash
# 修复数据目录权限
sudo chown -R 1000:1000 data/
sudo chmod -R 755 data/
```

#### 4. 端口冲突
```bash
# 检查端口占用
sudo netstat -tlnp | grep :80

# 修改端口映射
# 在 docker-compose.yml 中修改: "8080:80"
```

### 健康检查

```bash
# 应用健康检查
curl http://localhost/health

# 后端API检查
curl http://localhost/api/health

# 容器健康状态
docker inspect cash-flow-app | grep Health -A 10
```

### 性能监控

```bash
# 查看容器资源使用
docker stats cash-flow-app

# 查看系统资源
htop
df -h
```

## 安全考虑

### 1. 环境变量安全
- 确保 `.env` 文件权限设置为 600
- 不要将 `.env` 文件提交到版本控制
- 定期更新API密钥

### 2. 网络安全
- 配置防火墙限制不必要的端口访问
- 使用HTTPS加密传输
- 考虑使用VPN或内网部署

### 3. 数据安全
- 定期备份数据库
- 设置强密码策略
- 启用日志记录和监控

## 扩展部署

### 负载均衡部署

如需高可用部署，可以配置多实例负载均衡：

```yaml
version: '3.8'
services:
  nginx-lb:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx-lb.conf:/etc/nginx/nginx.conf
    depends_on:
      - cash-flow-1
      - cash-flow-2

  cash-flow-1:
    build: .
    ports:
      - "8001:80"
    
  cash-flow-2:
    build: .
    ports:
      - "8002:80"
```

## 支持和反馈

如遇到部署问题，请提供以下信息：
1. 服务器环境信息 (OS、Docker版本)
2. 错误日志 (`docker-compose logs`)
3. 配置文件内容 (隐去敏感信息)
4. 具体的错误现象描述

---

**注意**: 首次部署可能需要5-10分钟完成所有初始化工作，请耐心等待。 