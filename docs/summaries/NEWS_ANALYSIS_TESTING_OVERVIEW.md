# 🤖 新闻分析AI测试框架 - 项目总览

## 📁 项目结构

```
news_analysis_testing/                    # 测试框架主目录
├── __init__.py                          # 包初始化文件
├── main.py                              # 主入口文件 (命令行工具)
├── config.py                            # 配置文件
├── test_utils.py                        # 测试工具类
├── test_cases.py                        # 测试用例生成器
├── test_runner.py                       # 测试执行器
├── report_generator.py                  # 报告生成器
├── examples.py                          # 使用示例
├── quick_start.py                       # 快速启动演示
├── README.md                            # 详细说明文档
├── results/                             # 测试结果目录 (自动创建)
├── reports/                             # 测试报告目录 (自动创建)
└── logs/                               # 日志目录 (自动创建)

run_news_analysis_test.py                # 便捷启动脚本
NEWS_ANALYSIS_TESTING_OVERVIEW.md       # 本文档
```

## 🎯 核心功能

### 1. 测试分类 (6大类)

| 分类 | 描述 | 测试内容 |
|------|------|----------|
| **basic** | 基础功能测试 | API响应、字段完整性、基本分析能力 |
| **market_impact** | 市场影响分析测试 | 美股/A股/港股影响评估准确性 |
| **stock_recommendation** | 股票推荐测试 | 推荐股票相关性、推荐理由合理性 |
| **performance** | 性能测试 | 响应时间、不同长度文本处理能力 |
| **batch_analysis** | 批量分析测试 | 并发处理、批量结果一致性 |
| **edge_cases** | 边界情况测试 | 异常输入、特殊字符、多语言支持 |

### 2. 评估维度

- **响应时间评估** (20%权重)
  - 优秀: ≤10秒
  - 良好: ≤20秒  
  - 可接受: ≤30秒
  - 较差: >30秒

- **分析完整性评估** (40%权重)
  - 必需字段检查
  - 可选字段检查
  - 数据结构验证

- **内容质量评估** (40%权重)
  - 概述长度充足性
  - 分析深度合理性
  - 股票推荐质量

### 3. 报告格式

- **HTML报告**: 可视化图表、交互式界面
- **JSON报告**: 结构化数据、程序化处理
- **CSV摘要**: 表格格式、便于Excel分析

## 🚀 快速开始

### 方式1: 使用便捷启动脚本 (推荐)

```bash
python run_news_analysis_test.py
```

### 方式2: 直接使用命令行工具

```bash
# 快速测试
python -m news_analysis_testing.main --quick

# 完整测试
python -m news_analysis_testing.main --all

# 特定分类测试
python -m news_analysis_testing.main --category basic,performance

# 手动输入新闻
python -m news_analysis_testing.main --manual
```

### 方式3: Python代码调用

```python
from news_analysis_testing import run_news_analysis_tests

# 执行快速测试
result = run_news_analysis_tests(quick=True)
print(f"成功率: {result['overall_summary']['success_rate']:.1%}")
```

## 📊 预定义测试新闻样本

框架内置了4类典型新闻样本：

1. **正面科技新闻**: 苹果发布AI芯片
2. **负面金融新闻**: 美联储意外加息
3. **中性政策新闻**: 央行维持利率不变
4. **行业特定新闻**: 新能源汽车销量创新高

## 🔧 配置说明

### API配置
```python
'api': {
    'base_url': 'http://127.0.0.1:8000',  # 后端API地址
    'timeout': 60,                        # 请求超时时间
    'max_retries': 3                      # 最大重试次数
}
```

### 测试配置
```python
'testing': {
    'max_concurrent_requests': 3,         # 最大并发数
    'save_detailed_results': True,        # 保存详细结果
    'generate_reports': True              # 生成测试报告
}
```

## 📈 使用场景

### 1. 开发阶段
- **功能验证**: 确保新闻分析API正常工作
- **性能监控**: 检查响应时间是否符合要求
- **回归测试**: 代码更新后验证功能完整性

### 2. 部署阶段
- **环境验证**: 确保生产环境配置正确
- **压力测试**: 验证系统在负载下的表现
- **健康检查**: 定期监控系统状态

### 3. 运维阶段
- **定期测试**: 自动化定期执行测试套件
- **问题诊断**: 快速定位系统问题
- **性能分析**: 长期跟踪系统性能趋势

## 🛠️ 扩展指南

### 添加新的测试分类

1. 在 `config.py` 中添加新分类定义
2. 在 `test_cases.py` 中实现测试用例生成方法
3. 在 `test_runner.py` 中添加执行逻辑
4. 更新 `report_generator.py` 支持新分类报告

### 自定义评估标准

修改 `config.py` 中的 `EVALUATION_CRITERIA` 配置：

```python
EVALUATION_CRITERIA = {
    'response_time': {
        'excellent': 5,   # 自定义时间阈值
        'good': 15,
        'acceptable': 25,
        'poor': 40
    }
}
```

### 添加新的报告格式

在 `report_generator.py` 中添加新的报告生成方法：

```python
def generate_pdf_report(self, test_results):
    # 实现PDF报告生成逻辑
    pass
```

## 🔍 故障排除

### 常见问题

1. **API连接失败**
   ```
   ❌ API健康检查失败: API不可访问
   ```
   **解决方案**: 确保后端服务运行在 http://127.0.0.1:8000

2. **GLM API不可用**
   ```
   ❌ GLM API不可用，请检查GLM_API_KEY环境变量设置
   ```
   **解决方案**: 检查GLM API密钥配置

3. **测试超时**
   ```
   ❌ 请求超时
   ```
   **解决方案**: 增加 `timeout` 配置值或检查网络连接

4. **权限错误**
   ```
   ❌ 无法创建测试结果目录
   ```
   **解决方案**: 确保有写入权限或更改输出目录

### 调试模式

启用详细日志输出：

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 然后运行测试
from news_analysis_testing import run_news_analysis_tests
result = run_news_analysis_tests()
```

## 📋 检查清单

在使用测试框架前，请确认：

- [ ] 后端API服务正常运行
- [ ] GLM API密钥配置正确
- [ ] Python环境满足要求 (3.7+)
- [ ] 必要的依赖包已安装
- [ ] 有足够的磁盘空间存储测试结果
- [ ] 网络连接正常

## 🎯 最佳实践

1. **定期执行**: 建议每日执行快速测试，每周执行完整测试
2. **版本控制**: 将测试结果纳入版本控制，跟踪性能变化
3. **阈值设置**: 根据业务需求调整评估阈值
4. **报告分析**: 定期分析测试报告，识别性能瓶颈
5. **自动化集成**: 将测试框架集成到CI/CD流程中

## 📞 支持

如果在使用过程中遇到问题：

1. 查看 `news_analysis_testing/README.md` 详细文档
2. 运行 `python -m news_analysis_testing.main --help-full` 查看帮助
3. 检查 `logs/` 目录下的日志文件
4. 运行 `python news_analysis_testing/examples.py` 查看示例

---

**版本**: 1.0.0  
**更新时间**: 2024-12-16  
**兼容性**: Python 3.7+
