# 🤖 智能新闻影响分析功能

## 功能概述

基于GLM-4-Flash大语言模型的智能新闻影响分析功能，能够自动分析财经新闻对金融市场的潜在影响，提供多维度的专业分析和投资建议。

## 核心特性

### 🔍 智能分析引擎
- **AI驱动**: 使用GLM-4-Flash API进行专业的金融分析
- **多维度评估**: 覆盖美股、A股、港股三大市场
- **结构化输出**: 标准化的JSON格式分析结果
- **缓存机制**: 避免重复分析相同新闻，提高效率

### 📊 分析维度

#### 1. 整体影响评估
- 影响程度评级（高/中/低）
- 影响概述和关键要点

#### 2. 市场分析
- **美股市场**: 影响程度、方向、相关板块、推荐股票
- **A股市场**: 影响程度、方向、相关行业、推荐股票  
- **港股市场**: 影响程度、方向、相关板块、推荐股票

#### 3. 指数影响
- S&P 500、纳斯达克指数
- 上证综指、深证成指
- 恒生指数

#### 4. 个股分析
- 识别可能受影响的具体个股
- 提供利多/利空判断和详细原因

#### 5. 风险评估
- 短期和中期风险评级
- 关键风险因素识别

#### 6. 投资建议
- 投资策略建议
- 注意事项和时间范围

## 技术架构

### 后端组件

#### 1. GLM客户端 (`backend/ai/llms/glm_client.py`)
```python
class GLMClient:
    - analyze_news_impact(): 调用GLM-4-Flash API进行新闻分析
    - is_available(): 检查API可用性
    - _build_analysis_prompt(): 构建专业分析提示词
```

#### 2. 新闻影响分析器 (`backend/news_impact_analyzer.py`)
```python
class NewsImpactAnalyzer:
    - analyze_news(): 单条新闻分析
    - batch_analyze_news(): 批量新闻分析
    - get_analysis_by_id(): 获取分析结果
    - get_recent_analyses(): 获取最近分析
    - get_analysis_statistics(): 获取统计信息
```

#### 3. 财经新闻管理器 (`backend/financial_news_manager.py`)
```python
class FinancialNewsManager:
    - get_latest_news(): 获取最新新闻
    - get_news_by_id(): 根据ID获取新闻
    - search_news(): 搜索新闻
    - get_statistics(): 获取统计信息
```

### 前端组件

#### 1. 智能财经新闻组件 (`SmartFinancialNews.tsx`)
- 新闻列表展示
- AI分析按钮集成
- 实时数据刷新
- 错误处理和加载状态

#### 2. 新闻影响分析组件 (`NewsImpactAnalysis.tsx`)
- 完整的分析结果可视化
- 多维度影响展示
- 颜色编码的影响程度显示
- 股票推荐和投资建议展示

### API接口

#### 新闻相关接口
```
GET  /financial-news/latest          # 获取最新新闻
POST /financial-news/query           # 查询新闻
GET  /financial-news/sources         # 获取数据源状态
GET  /financial-news/statistics      # 获取统计信息
```

#### 影响分析接口
```
POST /news/impact-analysis                    # 分析单条新闻
POST /news/batch-impact-analysis             # 批量分析
GET  /news/impact-analysis/{id}              # 获取分析结果
GET  /news/impact-analysis/recent            # 获取最近分析
GET  /news/impact-analysis/statistics        # 获取分析统计
POST /news/impact-analysis/auto-analyze-latest # 自动分析最新重要新闻
```

## 数据库设计

### 新闻影响分析表 (`news_impact_analysis`)
```sql
CREATE TABLE news_impact_analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    news_hash VARCHAR(64) UNIQUE NOT NULL,
    news_title TEXT NOT NULL,
    news_content TEXT,
    news_source VARCHAR(100),
    news_publish_time DATETIME,
    analysis_result TEXT NOT NULL,
    overall_impact_level VARCHAR(10),
    us_market_impact VARCHAR(10),
    a_share_impact VARCHAR(10),
    hk_market_impact VARCHAR(10),
    analysis_status VARCHAR(20) DEFAULT 'completed',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 分析缓存表 (`analysis_cache`)
```sql
CREATE TABLE analysis_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    cache_key VARCHAR(64) UNIQUE NOT NULL,
    cache_data TEXT NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 使用方法

### 1. 环境配置
```bash
# 设置GLM API密钥
export GLM_API_KEY="your-glm-api-key"

# 或在.env文件中添加
echo "GLM_API_KEY=your-glm-api-key" >> .env
```

### 2. 启动服务
```bash
# 启动后端服务
cd backend && python server.py

# 启动前端服务
cd frontend && npm run dev
```

### 3. 访问界面
- 前端界面: http://localhost:3000
- 后端API: http://127.0.0.1:8000

### 4. 功能测试
```bash
# 运行完整功能测试
python test_smart_news_integration.py

# 运行新闻影响分析测试
python test_news_impact_analysis.py
```

## 使用流程

### 1. 查看智能新闻
1. 访问主页面，查看"🤖 智能财经资讯"模块
2. 浏览最新的财经新闻列表
3. 点击"刷新"按钮获取最新新闻

### 2. 进行AI分析
1. 选择感兴趣的新闻
2. 点击"🤖 AI 分析"按钮
3. 等待GLM-4-Flash分析完成（通常15-30秒）

### 3. 查看分析结果
1. **整体影响**: 查看影响级别和概述
2. **市场分析**: 了解对各市场的具体影响
3. **指数影响**: 查看对主要指数的影响预测
4. **个股推荐**: 获取具体的股票投资建议
5. **风险评估**: 了解潜在风险和注意事项
6. **投资建议**: 获取专业的投资策略建议

## 分析结果示例

```json
{
  "overall_impact": {
    "level": "高",
    "summary": "美联储加息将对全球金融市场产生显著影响..."
  },
  "us_market": {
    "impact_level": "高",
    "direction": "利空",
    "affected_sectors": ["金融", "房地产", "消费"],
    "recommended_stocks": [
      {
        "symbol": "JPM",
        "name": "JPMorgan Chase & Co.",
        "impact": "利空",
        "reason": "加息可能导致银行利润率下降"
      }
    ],
    "analysis": "加息将提高借款成本，对依赖借贷的行业产生负面影响"
  },
  "major_indices": {
    "sp500": {
      "impact": "利空",
      "reason": "加息预期通常会导致股市下跌"
    }
  },
  "investment_advice": {
    "strategy": "考虑增加固定收益类资产配置",
    "attention_points": ["关注市场波动性增加的风险"],
    "time_horizon": "短期"
  }
}
```

## 性能特点

### 1. 智能缓存
- 相同新闻内容自动使用缓存结果
- 避免重复调用GLM API，节省成本
- 提高响应速度

### 2. 并发处理
- 支持批量新闻的并发分析
- 可配置并发数量，平衡速度和资源使用

### 3. 错误处理
- 完善的错误处理机制
- 网络超时和重试机制
- 用户友好的错误提示

## 扩展功能

### 1. 自动分析
- 定时分析最新重要新闻
- 基于关键词过滤重要新闻
- 支持自定义分析触发条件

### 2. 历史分析
- 查看历史分析记录
- 分析结果统计和趋势
- 分析准确性跟踪

### 3. 个性化设置
- 自定义关注的市场和板块
- 个性化的风险偏好设置
- 定制化的分析报告格式

## 注意事项

1. **API限制**: GLM-4-Flash API有调用频率限制，请合理使用
2. **分析时间**: AI分析通常需要15-30秒，请耐心等待
3. **结果参考**: 分析结果仅供参考，投资决策请结合多方信息
4. **网络要求**: 需要稳定的网络连接访问GLM API

## 故障排除

### 1. GLM API不可用
```bash
# 检查环境变量
echo $GLM_API_KEY

# 测试API连接
python -c "from backend.ai.llms.glm_client import get_glm_client; print(get_glm_client().is_available())"
```

### 2. 新闻数据为空
```bash
# 检查新闻数据库
python -c "from backend.financial_news_manager import get_financial_news_manager; print(len(get_financial_news_manager().get_latest_news(5)))"

# 手动同步新闻
curl -X POST "http://127.0.0.1:8000/financial-news/sync" -H "Content-Type: application/json" -d '{"sync_type": "breakfast"}'
```

### 3. 前端无法访问后端
```bash
# 检查后端服务状态
curl http://127.0.0.1:8000/health

# 检查端口占用
lsof -i :8000
```

## 更新日志

### v1.0.0 (2025-06-13)
- ✅ 完成GLM-4-Flash API集成
- ✅ 实现新闻影响分析核心功能
- ✅ 完成前端智能新闻组件
- ✅ 实现分析结果可视化
- ✅ 添加缓存机制和错误处理
- ✅ 完成完整的测试覆盖

---

**开发团队**: AI Assistant  
**技术支持**: 如有问题请查看日志文件或运行测试脚本进行诊断 