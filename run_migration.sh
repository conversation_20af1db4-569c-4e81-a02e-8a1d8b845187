#!/bin/bash
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

# 数据库迁移执行脚本

echo "=========================================="
echo "开始执行数据库迁移"
echo "=========================================="

# 检查是否在项目根目录
if [ ! -f "backend/scripts/migrations/add_impact_score_column.py" ]; then
    echo "错误：请在项目根目录运行此脚本"
    exit 1
fi

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "错误：未找到python3命令"
    exit 1
fi

# 执行迁移脚本
echo "执行迁移：添加impact_score列..."
python3 backend/scripts/migrations/add_impact_score_column.py

migration_exit_code=$?

if [ $migration_exit_code -eq 0 ]; then
    echo "=========================================="
    echo "数据库迁移成功完成"
    echo "=========================================="
    exit 0
else
    echo "=========================================="
    echo "数据库迁移失败"
    echo "=========================================="
    exit 1
fi 