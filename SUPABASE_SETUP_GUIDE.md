# 📊 Supabase数据库设置指南

## 🎉 连接测试结果

您的Supabase连接配置**完全正确**！✅

- ✅ **环境变量配置**: 所有必要的配置都已正确设置
- ✅ **网络连接**: 成功连接到 `https://yvosnnboqjoktnircdnw.supabase.co`
- ✅ **客户端创建**: Supabase Python客户端正常工作
- ✅ **延迟测试**: 连接质量良好

## 📋 下一步：创建数据库表

现在只需要在Supabase控制台创建`news`表即可开始使用。

### 步骤1：访问Supabase控制台

1. 打开浏览器，访问 [Supabase控制台](https://app.supabase.com)
2. 登录您的账户
3. 选择您的项目（URL: `https://yvosnnboqjoktnircdnw.supabase.co`）

### 步骤2：打开SQL编辑器

1. 在左侧导航栏中点击 **"SQL Editor"** 
2. 点击 **"New query"** 创建新查询

### 步骤3：执行表创建脚本

复制以下SQL脚本并粘贴到编辑器中：

```sql
-- ================================
-- Supabase新闻表创建脚本
-- ================================

-- 创建news表
CREATE TABLE IF NOT EXISTS public.news (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    title TEXT NOT NULL,
    content TEXT,
    published_at TIMESTAMPTZ NOT NULL,
    url TEXT UNIQUE,
    source TEXT NOT NULL,
    related_stocks JSONB DEFAULT '[]'::jsonb,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_news_url ON public.news(url);
CREATE INDEX IF NOT EXISTS idx_news_published_at ON public.news(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_news_source ON public.news(source);
CREATE INDEX IF NOT EXISTS idx_news_created_at ON public.news(created_at DESC);

-- 创建复合索引
CREATE INDEX IF NOT EXISTS idx_news_source_published ON public.news(source, published_at DESC);

-- 为related_stocks JSONB字段创建GIN索引以支持高效的JSON查询
CREATE INDEX IF NOT EXISTS idx_news_related_stocks ON public.news USING gin(related_stocks);

-- 添加表注释
COMMENT ON TABLE public.news IS '新闻数据表，存储从各个数据源采集的金融新闻';
COMMENT ON COLUMN public.news.id IS '唯一标识符';
COMMENT ON COLUMN public.news.created_at IS '记录创建时间';
COMMENT ON COLUMN public.news.title IS '新闻标题';
COMMENT ON COLUMN public.news.content IS '新闻内容摘要';
COMMENT ON COLUMN public.news.published_at IS '新闻发布时间';
COMMENT ON COLUMN public.news.url IS '新闻原始链接，用于去重';
COMMENT ON COLUMN public.news.source IS '新闻来源（如：东方财富、财新网）';
COMMENT ON COLUMN public.news.related_stocks IS '相关股票代码，JSON数组格式';
COMMENT ON COLUMN public.news.updated_at IS '记录最后更新时间';

-- 创建自动更新updated_at字段的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
DROP TRIGGER IF EXISTS update_news_updated_at ON public.news;
CREATE TRIGGER update_news_updated_at
    BEFORE UPDATE ON public.news
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

### 步骤4：运行脚本

1. 点击 **"Run"** 按钮（或按 Ctrl+Enter）
2. 确认执行成功，应该看到类似 `Success. No rows returned` 的消息
3. 在左侧导航栏点击 **"Table Editor"** 验证`news`表已创建

## ✅ 验证设置

表创建完成后，运行以下命令验证一切正常：

```bash
# 重新测试连接
python test_supabase_connection.py

# 如果测试通过，开始新闻同步
./run_supabase_sync.sh
```

## 🚀 开始使用

设置完成后，您可以：

### 一次性同步
```bash
./run_supabase_sync.sh --sync
```

### 查看帮助
```bash
./run_supabase_sync.sh --help
```

### 检查系统状态
```bash
./run_supabase_sync.sh --check
```

## 📊 表结构说明

`news`表包含以下字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `id` | BIGSERIAL | 自增主键 |
| `created_at` | TIMESTAMPTZ | 记录创建时间 |
| `title` | TEXT | 新闻标题 |
| `content` | TEXT | 新闻内容摘要 |
| `published_at` | TIMESTAMPTZ | 新闻发布时间 |
| `url` | TEXT | 新闻链接（唯一约束，用于去重） |
| `source` | TEXT | 数据源（东方财富、财新网等） |
| `related_stocks` | JSONB | 相关股票代码数组 |
| `updated_at` | TIMESTAMPTZ | 记录更新时间（自动维护） |

## 🔧 性能优化

创建的索引包括：
- URL索引（去重查询）
- 发布时间索引（时间范围查询）
- 数据源索引（按源筛选）
- 复合索引（多条件查询）
- JSONB GIN索引（股票代码搜索）

## 🛡️ 安全建议

1. **密钥管理**: 确保使用服务角色密钥(service_role key)
2. **访问控制**: 考虑配置行级安全(RLS)策略
3. **定期轮换**: 定期更新API密钥
4. **监控使用**: 关注项目配额和使用情况

## ❓ 故障排除

如果遇到问题：

1. **权限错误**: 确认使用的是服务角色密钥
2. **网络问题**: 检查防火墙和网络连接
3. **配额限制**: 查看Supabase项目使用情况
4. **SQL错误**: 确保完整复制并执行了所有SQL语句

---

完成以上步骤后，您的Supabase新闻同步系统就完全配置好了！🎉 