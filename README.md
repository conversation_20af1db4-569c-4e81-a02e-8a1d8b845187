# Cash Flow: AI-Powered Financial Analysis System

Cash Flow is a comprehensive financial analysis system powered by AI. It provides advanced capabilities for stock analysis, market research, investment decision support, and real-time data acquisition. The system is designed with a modular architecture, consisting of a robust backend for data processing and AI computations, and a dynamic frontend for interactive user experience.

## Backend

The backend is the core of the Cash Flow system, responsible for all data processing, AI computations, and API services. It is built with Python and utilizes various libraries for financial data acquisition, machine learning, and multi-agent systems.

### Key Components:

*   **AI Agents (`backend/ai/agents`)**: Manages specialized AI agents for various analysis tasks, including research, coding, and technical analysis.
*   **API Layer (`backend/apis/`)**: RESTful API endpoints for data queries, factor calculations, and metadata management.
*   **Core Business Logic (`backend/core/`)**:
    *   **Data Management (`backend/core/data/`)**: Handles data acquisition, storage, and management from various sources (e.g., AkShare, Tushare).
    *   **Analysis Engine (`backend/core/analysis/`)**: Comprehensive analysis capabilities including:
        *   **Factor Analysis (`backend/core/analysis/factors/`)**: Technical, fundamental, and quantitative factor calculations.
        *   **Machine Learning (`backend/core/analysis/ml/`)**: Predictive analytics and model training.
        *   **Scoring System (`backend/core/analysis/scoring/`)**: Stock ranking and evaluation algorithms.
        *   **Risk Management (`backend/core/analysis/risk/`)**: VaR, CVaR, and drawdown analysis.
        *   **Technical Analysis (`backend/core/analysis/technical/`)**: Pattern detection and market scanning.
        *   **Backtesting (`backend/core/analysis/backtesting/`)**: Strategy testing and performance evaluation.
*   **Services Layer (`backend/services/`)**: Business services for news analysis, market data, and background tasks.
*   **Authentication (`backend/auth/`)**: User management and authentication services.
*   **Utilities (`backend/utils/`)**: Common utility functions and helpers.
*   **Scripts (`backend/scripts/`)**: Management and initialization scripts.

## Frontend

The frontend provides a user-friendly interface for interacting with the Cash Flow system. It is a React application built with TypeScript, designed to visualize financial data, display analysis reports, and allow users to configure and run various AI-powered tools.

### Key Components:

*   **User Interface (`frontend/src/App.tsx`, `frontend/src/App.css`)**: The main application component and its styling, handling navigation, state management, and rendering of different modules.
*   **Components (`frontend/src/components`)**: Reusable UI components, such as `MarkdownRenderer` for displaying formatted text.
*   **Public Assets (`frontend/public`)**: Static assets like `index.html`, favicons, and logos.

## Setup and Running

Follow these steps to set up and run the Cash Flow system on your local machine.

### Backend Setup

1.  **Navigate to the Backend Directory:**
    ```bash
    cd backend
    ```

2.  **Create a Virtual Environment (Recommended):**
    ```bash
    python3 -m venv venv
    ```

3.  **Activate the Virtual Environment:**
    *   On macOS/Linux:
        ```bash
        source venv/bin/activate
        ```
    *   On Windows:
        ```bash
        .\venv\Scripts\activate
        ```

4.  **Install Python Dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

5.  **Configure Environment Variables:**
    The system requires `GEMINI_API_KEY` to be set for the LLM. You can create a `.env` file in the `backend` directory or set it in your shell environment.
    ```
    GEMINI_API_KEY=your_gemini_api_key_here
    GEMINI_MODEL=gemini-2.5-flash-lite-preview-06-17 # Or gemini-1.5-flash
    ```

6.  **Run the Backend Server:**
    ```bash
    python -m uvicorn backend.server:app --reload
    ```
    The backend API will typically run on `http://127.0.0.1:8000`.

### Frontend Setup

1.  **Navigate to the Frontend Directory:**
    ```bash
    cd frontend
    ```

2.  **Install Node.js Dependencies:**
    Make sure you have Node.js and npm installed.
    ```bash
    npm install
    ```

3.  **Run the Frontend Development Server:**
    ```bash
    npm start
    ```
    The frontend application will open in your browser, typically at `http://localhost:3000`.

### Running the Stock Chart Demo

The `demo_stock_chart_feature.py` script demonstrates the stock chart detection and generation pipeline.

1.  **Ensure Backend is Running:** Make sure you have started the backend server as described above.

2.  **Run the Demo Script:**
    From the project root directory:
    ```bash
    python demo_stock_chart_feature.py
    ```
    This script will execute a series of demo queries and print the results of stock symbol detection, data fetching, chart configuration generation, and LangGraph integration.
