#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
新闻分析测试框架启动脚本
直接运行此脚本开始测试
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """主函数"""
    print("""
╔══════════════════════════════════════════════════════════════╗
║                    🤖 新闻分析AI测试框架                      ║
║                                                              ║
║  专门用于测试AI分析新闻结果和相关股票影响的综合测试工具        ║
╚══════════════════════════════════════════════════════════════╝
    """)
    
    print("🚀 选择运行模式:")
    print("1. 快速演示 (推荐新用户)")
    print("2. 快速测试 (仅基础功能)")
    print("3. 完整测试套件")
    print("4. 手动输入新闻测试")
    print("5. 查看测试用例列表")
    print("6. API健康检查")
    print("7. 运行示例脚本")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (0-7): ").strip()
            
            if choice == '0':
                print("👋 再见!")
                break
            elif choice == '1':
                print("🎬 启动快速演示...")
                os.system(f"python {project_root}/news_analysis_testing/quick_start.py")
            elif choice == '2':
                print("⚡ 执行快速测试...")
                os.system(f"python {project_root}/news_analysis_testing/main.py --quick")
            elif choice == '3':
                print("🔄 执行完整测试套件...")
                os.system(f"python {project_root}/news_analysis_testing/main.py --all")
            elif choice == '4':
                print("✍️ 手动输入新闻测试...")
                os.system(f"python {project_root}/news_analysis_testing/main.py --manual")
            elif choice == '5':
                print("📋 查看测试用例列表...")
                os.system(f"python {project_root}/news_analysis_testing/main.py --list-cases")
            elif choice == '6':
                print("🏥 API健康检查...")
                os.system(f"python {project_root}/news_analysis_testing/main.py --health-check")
            elif choice == '7':
                print("🧪 运行示例脚本...")
                os.system(f"python {project_root}/news_analysis_testing/examples.py")
            else:
                print("❌ 无效选择，请输入0-7之间的数字")
                continue
            
            # 询问是否继续
            if choice != '0':
                continue_choice = input("\n是否继续使用? (y/n, 默认y): ").strip().lower()
                if continue_choice in ['n', 'no']:
                    print("👋 再见!")
                    break
                    
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，再见!")
            break
        except Exception as e:
            print(f"\n❌ 发生错误: {e}")
            continue

if __name__ == "__main__":
    main()
