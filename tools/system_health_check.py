#!/usr/bin/env python3
"""
系统健康检查脚本
验证所有服务和功能是否正常运行
"""

import requests
import sys
import os
from datetime import datetime

def check_backend_health():
    """检查后端服务健康状态"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 后端服务正常: {data.get('service', 'Unknown')}")
            return True
        else:
            print(f"❌ 后端服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端服务连接失败: {e}")
        return False

def check_frontend_health():
    """检查前端服务健康状态"""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200 and "金融投资助手" in response.text:
            print("✅ 前端服务正常")
            return True
        else:
            print(f"❌ 前端服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端服务连接失败: {e}")
        return False

def check_api_endpoints():
    """检查主要API接口"""
    endpoints = [
        ("/factors", "因子列表接口"),
        ("/factors/list", "增强型因子列表接口"),
        ("/factors/health", "因子服务健康检查"),
        ("/divergence/markets", "市场信息接口"),
    ]
    
    all_ok = True
    for endpoint, name in endpoints:
        try:
            response = requests.get(f"http://localhost:8000{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name}正常")
            else:
                print(f"❌ {name}异常: HTTP {response.status_code}")
                all_ok = False
        except Exception as e:
            print(f"❌ {name}连接失败: {e}")
            all_ok = False
    
    return all_ok

def check_data_functionality():
    """检查数据功能"""
    try:
        # 测试股票数据查询
        payload = {
            "symbol": "AAPL",
            "tushare_token": "test_token",
            "period": "1m"
        }
        response = requests.post(
            "http://localhost:8000/stocks/query",
            json=payload,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            if data.get("success") and data.get("kline_data"):
                print(f"✅ 数据查询功能正常 (获取到 {len(data['kline_data'])} 条记录)")
                return True
            else:
                print("❌ 数据查询功能异常: 返回数据格式错误")
                return False
        else:
            print(f"❌ 数据查询功能异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 数据查询功能测试失败: {e}")
        return False

def check_akshare_integration():
    """检查AkShare集成"""
    try:
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        from backend.data_manager import init_data_manager
        
        # 简单测试数据管理器初始化
        data_manager = init_data_manager("test_token")
        print("✅ AkShare集成正常")
        return True
    except Exception as e:
        print(f"❌ AkShare集成异常: {e}")
        return False

def check_enhanced_factors():
    """检查增强型因子计算功能"""
    try:
        # 测试增强型因子计算器
        from backend.enhanced_factors import get_enhanced_factor_calculator, list_all_available_factors
        
        calculator = get_enhanced_factor_calculator()
        categories = calculator.get_factor_categories()
        total_factors = sum(len(factors) for factors in categories.values())
        
        print(f"✅ 增强型因子计算功能正常")
        print(f"   支持 {len(categories)} 个分类，共 {total_factors} 个因子")
        return True
    except Exception as e:
        print(f"❌ 增强型因子计算功能异常: {e}")
        return False

def main():
    """主检查函数"""
    print("=" * 60)
    print("🔍 系统健康检查")
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    checks = [
        ("后端服务", check_backend_health),
        ("前端服务", check_frontend_health),
        ("API接口", check_api_endpoints),
        ("数据功能", check_data_functionality),
        ("AkShare集成", check_akshare_integration),
        ("增强型因子计算", check_enhanced_factors),
    ]
    
    results = []
    for name, check_func in checks:
        print(f"\n🔍 检查 {name}...")
        result = check_func()
        results.append((name, result))
    
    print("\n" + "=" * 60)
    print("📊 检查结果汇总:")
    print("=" * 60)
    
    all_passed = True
    for name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 所有检查通过！系统运行正常，可以提交到GitHub。")
        print("💡 建议:")
        print("   - 确保所有重要文件都已提交")
        print("   - 检查.gitignore文件是否正确配置")
        print("   - 更新README文档")
    else:
        print("⚠️  部分检查未通过，请修复问题后再提交。")
    print("=" * 60)
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 