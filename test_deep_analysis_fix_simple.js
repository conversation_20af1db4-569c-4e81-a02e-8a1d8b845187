// 简单的深度分析功能测试脚本
// 在浏览器控制台中运行

console.log('🚀 开始测试AI深度分析功能修复效果');

// 测试直接流式分析API
async function testDirectStreamAnalysis() {
  console.log('📡 测试直接流式分析API...');
  
  try {
    const response = await fetch('http://localhost:8000/news/deep-analysis', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        news_title: "测试新闻：AI深度分析功能修复验证",
        news_content: "这是一条用于测试AI深度分析功能修复效果的测试新闻。主要验证EventSource连接、数据格式兼容性、流式响应处理和Markdown渲染等功能是否正常工作。",
        news_source: "测试来源",
        news_publish_time: new Date().toISOString(),
        analysis_type: "deep",
        max_research_loops: 1,
        priority: "medium",
        use_four_layer_analysis: true
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    console.log('✅ API请求成功，开始接收流式数据...');
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let messageCount = 0;
    let hasTaskId = false;
    let hasCompletion = false;

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const data = JSON.parse(line.slice(6));
            messageCount++;
            
            console.log(`📨 消息 ${messageCount}:`, {
              type: data.type,
              message: data.message?.substring(0, 50) + '...',
              hasTaskId: !!data.task_id
            });

            if (data.task_id && !hasTaskId) {
              hasTaskId = true;
              console.log('🆔 获取到任务ID:', data.task_id);
            }

            if (data.type === 'analysis_completed') {
              hasCompletion = true;
              console.log('🎉 分析完成!');
              
              if (data.result?.final_analysis) {
                console.log('✅ 分析结果格式正确');
              } else {
                console.log('⚠️ 分析结果格式可能有问题');
              }
              break;
            }

            if (data.type === 'error') {
              console.error('❌ 分析过程中出现错误:', data.message);
              return false;
            }
          } catch (err) {
            console.warn('⚠️ JSON解析失败:', err);
          }
        }
      }
    }

    console.log('📊 测试结果:');
    console.log(`  - 接收消息数: ${messageCount}`);
    console.log(`  - 获取任务ID: ${hasTaskId ? '✅' : '❌'}`);
    console.log(`  - 完成状态: ${hasCompletion ? '✅' : '❌'}`);

    return hasCompletion;

  } catch (error) {
    console.error('❌ 测试失败:', error);
    return false;
  }
}

// 测试EventSource连接（如果有任务ID）
async function testEventSourceConnection(taskId) {
  console.log('📡 测试EventSource连接...');
  
  return new Promise((resolve) => {
    const eventSource = new EventSource(`http://localhost:8000/news/deep-analysis/${taskId}/stream`);
    let messageCount = 0;
    let hasError = false;

    const timeout = setTimeout(() => {
      eventSource.close();
      console.log('⏰ EventSource测试超时');
      resolve(false);
    }, 10000);

    eventSource.onopen = () => {
      console.log('✅ EventSource连接成功');
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        messageCount++;
        console.log(`📨 EventSource消息 ${messageCount}:`, data.type);
      } catch (err) {
        console.error('❌ EventSource消息解析失败:', err);
        hasError = true;
      }
    };

    eventSource.onerror = (err) => {
      console.error('❌ EventSource连接错误:', err);
      hasError = true;
      clearTimeout(timeout);
      eventSource.close();
      resolve(false);
    };

    // 简单测试，5秒后关闭
    setTimeout(() => {
      clearTimeout(timeout);
      eventSource.close();
      console.log(`📊 EventSource测试结果: 消息数=${messageCount}, 错误=${hasError}`);
      resolve(!hasError && messageCount >= 0);
    }, 5000);
  });
}

// 运行测试
async function runTests() {
  console.log('🧪 开始运行测试套件...');
  
  const results = {
    directStream: false,
    eventSource: false
  };

  // 测试直接流式分析
  results.directStream = await testDirectStreamAnalysis();
  
  console.log('\n📋 测试结果总结:');
  console.log(`直接流式分析: ${results.directStream ? '✅ 通过' : '❌ 失败'}`);
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log(`\n🎯 总体结果: ${passedTests}/${totalTests} 测试通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！AI深度分析功能修复成功！');
  } else {
    console.log('⚠️ 部分测试失败，需要进一步检查。');
  }
  
  return results;
}

// 自动运行测试
runTests().catch(console.error);

// 导出测试函数供手动调用
window.testDeepAnalysis = {
  runTests,
  testDirectStreamAnalysis,
  testEventSourceConnection
};
