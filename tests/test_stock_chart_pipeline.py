#!/usr/bin/env python3
"""
Stock Chart Pipeline Test Script
Test the end-to-end functionality of stock symbol detection, chart generation, and workflow integration
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

async def test_stock_symbol_detector():
    """Test the stock symbol detector"""
    print("🔍 Testing Stock Symbol Detector...")
    
    try:
        from backend.ai.tools.stock_symbol_detector import StockSymbolDetector
        
        detector = StockSymbolDetector()
        
        # Test queries
        test_queries = [
            "分析苹果公司AAPL的股票表现",
            "给我看看特斯拉的K线图",
            "TSLA和META的比较分析",
            "今天苹果股价怎么样",
            "Show me Apple stock chart",
            "Tesla performance analysis",
            "What's the price of MSFT today?",
            "不是股票相关的查询"
        ]
        
        for query in test_queries:
            is_stock = detector.is_stock_query(query)
            symbols = detector.detect_symbols(query)
            primary_symbol = detector.get_primary_symbol(query)
            
            print(f"Query: {query}")
            print(f"  Is Stock Related: {is_stock}")
            print(f"  Symbols Found: {[f'{s.symbol}({s.company_name})' for s in symbols]}")
            print(f"  Primary Symbol: {primary_symbol if primary_symbol else 'None'}")
            print()
        
        print("✅ Stock Symbol Detector test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Stock Symbol Detector test failed: {e}")
        return False

async def test_chart_data_provider():
    """Test the chart data provider"""
    print("📊 Testing Chart Data Provider...")
    
    try:
        from backend.ai.tools.chart_data_provider import ChartDataProvider
        
        provider = ChartDataProvider()
        
        # Test with Apple stock
        chart_data = await provider.get_chart_data("分析苹果公司AAPL", max_days=120)
        
        if chart_data and chart_data.dates:
            print(f"✅ Retrieved {len(chart_data.dates)} data points for AAPL")
            print(f"   Date range: {chart_data.dates[0]} to {chart_data.dates[-1]}")
            print(f"   Current price: ${chart_data.current_price}")
            print(f"   Price change: {chart_data.price_change:+.2f} ({chart_data.price_change_percent:+.2f}%)")
            print("✅ Chart Data Provider test passed!")
            return True
        else:
            print("❌ No chart data retrieved")
            return False
            
    except Exception as e:
        print(f"❌ Chart Data Provider test failed: {e}")
        return False

async def test_chart_renderer():
    """Test the chart renderer"""
    print("🎨 Testing Chart Renderer...")
    
    try:
        from backend.ai.tools.chart_renderer import ChartRenderer
        from backend.ai.tools.chart_data_provider import ChartDataProvider
        
        provider = ChartDataProvider()
        renderer = ChartRenderer()
        
        # Get some test data
        chart_data = await provider.get_chart_data("分析苹果公司AAPL", max_days=30)
        
        if chart_data:
            # Generate chart config
            chart_config = renderer.generate_echarts_config(chart_data)
            
            # Check if config has required elements
            required_keys = ['title', 'xAxis', 'yAxis', 'series', 'tooltip', 'dataZoom']
            if all(key in chart_config for key in required_keys):
                print("✅ Chart configuration generated successfully")
                print(f"   Series count: {len(chart_config['series'])}")
                print(f"   Data points: {len(chart_config['series'][0]['data']) if chart_config['series'] else 0}")
                
                # Test HTML generation
                html_content = renderer.generate_chart_html(chart_config, "Test AAPL Chart")
                if "<html>" in html_content and "echarts" in html_content:
                    print("✅ HTML chart page generated successfully")
                    print("✅ Chart Renderer test passed!")
                    return True
                else:
                    print("❌ HTML generation failed")
                    return False
            else:
                print(f"❌ Chart config missing required keys: {required_keys}")
                return False
        else:
            print("❌ No chart data available for testing")
            return False
            
    except Exception as e:
        print(f"❌ Chart Renderer test failed: {e}")
        return False

async def test_chart_generation_node():
    """Test the chart generation node"""
    print("⚙️ Testing Chart Generation Node...")
    
    try:
        from backend.ai.graph.chart_node import chart_generation_node
        from langchain_core.messages import HumanMessage
        
        # Test state
        test_state = {
            "messages": [HumanMessage(content="分析苹果公司AAPL的股票表现")],
            "chart_data": None,
            "chart_config": None,
            "has_chart_data": False
        }
        
        # Run the node
        result = await chart_generation_node(test_state)
        
        # Check if result is a Command object with update data
        if hasattr(result, 'update') and result.update:
            update_data = result.update
            if update_data.get("has_chart_data") and update_data.get("chart_config"):
                print("✅ Chart generation node executed successfully")
                print(f"   Has chart data: {update_data.get('has_chart_data')}")
                print(f"   Chart config keys: {list(update_data.get('chart_config', {}).keys())}")
                print(f"   Next node: {result.goto}")
                print("✅ Chart Generation Node test passed!")
                return True
            else:
                print("❌ Chart generation node failed to generate chart")
                print(f"   Update data: {update_data}")
                return False
        else:
            print("❌ Chart generation node returned unexpected result type")
            print(f"   Result type: {type(result)}")
            return False
            
    except Exception as e:
        print(f"❌ Chart Generation Node test failed: {e}")
        return False

async def test_workflow_integration():
    """Test workflow integration"""
    print("🔄 Testing Workflow Integration...")
    
    try:
        from backend.ai.workflow import AIWorkflowManager
        
        # Initialize workflow manager
        workflow_manager = AIWorkflowManager()
        
        # Test with stock query
        test_query = "分析苹果公司AAPL的股票走势"
        
        print(f"Testing query: {test_query}")
        
        has_chart_data = False
        has_final_report = False
        
        async for result in workflow_manager.process_user_query(test_query):
            content = result.get('content', '')
            content_preview = content[:100] if isinstance(content, str) else str(content)[:100]
            print(f"  {result.get('type')}: {result.get('agent', '')} - {content_preview}...")
            
            if result.get('type') == 'chart_data':
                has_chart_data = True
                print("    ✅ Chart data received!")
                
            if result.get('type') == 'final_report':
                has_final_report = True
                print("    ✅ Final report received!")
                
            # Break early for testing
            if has_chart_data and has_final_report:
                break
        
        if has_chart_data:
            print("✅ Workflow Integration test passed!")
            return True
        else:
            print("❌ No chart data received from workflow")
            return False
            
    except Exception as e:
        print(f"❌ Workflow Integration test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🚀 Starting Stock Chart Pipeline Tests...")
    print("=" * 60)
    
    tests = [
        test_stock_symbol_detector,
        test_chart_data_provider, 
        test_chart_renderer,
        test_chart_generation_node,
        test_workflow_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
            print("-" * 60)
        except Exception as e:
            print(f"❌ Test {test_func.__name__} crashed: {e}")
            print("-" * 60)
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Stock chart pipeline is working correctly.")
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    asyncio.run(main()) 