#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重复响应问题修复
"""

import asyncio
import json
import time
import requests
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_simple_greeting_http():
    """通过HTTP API测试简单问候"""
    print("🧪 测试HTTP API - 简单问候")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        "你好",
        "hello", 
        "Hi",
        "how are you",
        "谢谢"
    ]
    
    for i, message in enumerate(test_cases, 1):
        print(f"\n🔍 测试 {i}: '{message}'")
        print("-" * 30)
        
        try:
            response = requests.post(
                'http://127.0.0.1:8000/chat/stream',
                headers={'Content-Type': 'application/json'},
                json={
                    'message': message,
                    'max_plan_iterations': 1,
                    'max_step_num': 3,
                    'enable_background_investigation': False,
                    'debug': False,
                },
                stream=True
            )
            
            if response.status_code == 200:
                print("✅ 后端响应成功")
                
                # 统计响应数据
                message_count = 0
                final_report_count = 0
                response_contents = []
                
                for line in response.iter_lines():
                    if line:
                        try:
                            line_str = line.decode('utf-8')
                            if line_str.startswith('data: '):
                                data_str = line_str[6:]
                                if data_str.strip() == '[DONE]':
                                    break
                                    
                                data = json.loads(data_str)
                                response_type = data.get('type', 'unknown')
                                content = data.get('content', '')
                                agent = data.get('agent', 'unknown')
                                
                                print(f"   📡 收到响应: {response_type} | {agent} | {content[:50]}...")
                                
                                if response_type == 'message':
                                    message_count += 1
                                    response_contents.append(f"MESSAGE: {content}")
                                elif response_type == 'final_report':
                                    final_report_count += 1
                                    response_contents.append(f"FINAL_REPORT: {content}")
                                elif response_type == 'end':
                                    print(f"   🏁 工作流结束: {content}")
                                    break
                                    
                        except json.JSONDecodeError:
                            continue
                        except Exception as e:
                            print(f"   ⚠️  处理响应行时出错: {e}")
                
                # 分析结果
                print(f"\n📊 响应统计:")
                print(f"   - Message 类型响应: {message_count}")
                print(f"   - Final Report 类型响应: {final_report_count}")
                print(f"   - 总响应数量: {len(response_contents)}")
                
                # 检查是否有重复
                unique_contents = set(response_contents)
                if len(unique_contents) < len(response_contents):
                    print("❌ 发现重复响应！")
                    print("   重复的内容:")
                    for content in response_contents:
                        if response_contents.count(content) > 1:
                            print(f"     • {content[:100]}...")
                else:
                    print("✅ 无重复响应")
                
                # 期望：对于简单问候，应该只有1个final_report响应
                if final_report_count == 1 and message_count == 0:
                    print("🎉 测试通过：响应正确，无重复")
                else:
                    print(f"⚠️  响应异常：期望1个final_report，0个message，实际：{final_report_count}个final_report，{message_count}个message")
                    
            else:
                print(f"❌ 后端响应失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
        except Exception as e:
            print(f"❌ 处理响应时出错: {e}")
        
        time.sleep(1)  # 避免请求过快

async def test_graph_direct():
    """直接测试图节点逻辑"""
    print("\n🧪 测试图节点 - 直接调用")
    print("=" * 50)
    
    try:
        from backend.ai.graph.builder import build_graph
        from backend.ai.graph.types import State
        from langchain_core.messages import HumanMessage
        
        # 构建图
        graph = build_graph()
        print("✅ 图构建成功")
        
        # 测试简单问候
        initial_state = State(
            messages=[HumanMessage(content="你好")],
            locale="zh-CN",
            auto_accepted_plan=True,
        )
        
        print("🔄 执行工作流...")
        
        config = {"configurable": {"thread_id": "test_duplicate_fix"}}
        
        # 收集所有状态变化
        states = []
        async for state in graph.astream(initial_state, config=config, stream_mode="values"):
            states.append(state)
            print(f"   📊 状态更新: messages={len(state.get('messages', []))}, final_report={'有' if state.get('final_report') else '无'}")
        
        # 分析最终状态
        final_state = states[-1] if states else {}
        messages = final_state.get('messages', [])
        final_report = final_state.get('final_report', '')
        
        print(f"\n📋 最终状态分析:")
        print(f"   - 消息数量: {len(messages)}")
        print(f"   - 最终报告: {'有' if final_report else '无'}")
        
        if len(messages) <= 1 and final_report:  # 用户消息 + 可能的系统消息
            print("✅ 图节点测试通过：无重复响应")
        else:
            print("❌ 图节点测试失败：可能存在重复")
            print("   消息列表:")
            for i, msg in enumerate(messages):
                print(f"     {i+1}. {getattr(msg, 'name', 'unknown')}: {getattr(msg, 'content', str(msg))[:100]}...")
                
    except Exception as e:
        print(f"❌ 图节点测试失败: {e}")
        import traceback
        print(f"错误详情: {traceback.format_exc()}")

async def main():
    """主测试函数"""
    print("🔧 重复响应问题修复测试")
    print("=" * 50)
    
    # 测试1: HTTP API
    test_simple_greeting_http()
    
    # 测试2: 直接图节点
    await test_graph_direct()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main()) 