#!/usr/bin/env python3
"""
AI市场影响分析模型选择功能测试

测试GLM和Gemini模型的切换功能以及API端点的响应
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from backend.services.news.news_impact_analyzer import get_news_impact_analyzer

async def test_glm_analysis():
    """测试GLM模型分析"""
    print("🔍 测试GLM模型分析...")
    
    analyzer = get_news_impact_analyzer()
    
    # 测试新闻数据
    test_news = {
        'title': '苹果公司发布最新iPhone，预计销量创历史新高',
        'content': '苹果公司今日正式发布了最新款iPhone，搭载最新A17芯片，相机性能大幅提升。分析师预计该产品将推动苹果第四季度销售额显著增长，股价有望继续上涨。',
        'source': 'test_source',
        'publish_time': datetime.now().isoformat()
    }
    
    try:
        result = await analyzer.analyze_news(test_news, model='glm')
        
        if result.get('success'):
            print("✅ GLM分析成功")
            analysis = result.get('analysis', {})
            print(f"   整体影响: {analysis.get('overall_impact', {}).get('level', 'N/A')}")
            print(f"   美股影响: {analysis.get('us_market', {}).get('impact_level', 'N/A')}")
            print(f"   A股影响: {analysis.get('a_share_market', {}).get('impact_level', 'N/A')}")
            return True
        else:
            print(f"❌ GLM分析失败: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ GLM分析异常: {e}")
        return False

async def test_gemini_analysis():
    """测试Gemini模型分析"""
    print("🔍 测试Gemini模型分析...")
    
    analyzer = get_news_impact_analyzer()
    
    # 测试新闻数据
    test_news = {
        'title': '特斯拉发布新型电池技术，续航里程大幅提升',
        'content': '特斯拉公司宣布开发出新型电池技术，新电池续航里程相比现有技术提升50%，充电时间缩短30%。这一突破性技术预计将在明年投入商用，对整个新能源汽车行业产生重大影响。',
        'source': 'test_source',
        'publish_time': datetime.now().isoformat()
    }
    
    try:
        result = await analyzer.analyze_news(test_news, model='gemini')
        
        if result.get('success'):
            print("✅ Gemini分析成功")
            analysis = result.get('analysis', {})
            print(f"   整体影响: {analysis.get('overall_impact', {}).get('level', 'N/A')}")
            print(f"   美股影响: {analysis.get('us_market', {}).get('impact_level', 'N/A')}")
            print(f"   A股影响: {analysis.get('a_share_market', {}).get('impact_level', 'N/A')}")
            return True
        else:
            print(f"❌ Gemini分析失败: {result.get('error', 'Unknown error')}")
            return False
            
    except Exception as e:
        print(f"❌ Gemini分析异常: {e}")
        return False

def test_api_availability():
    """测试API可用性"""
    print("🔍 测试API可用性...")
    
    analyzer = get_news_impact_analyzer()
    
    # 测试GLM客户端
    glm_available = analyzer.glm_client.is_available()
    print(f"   GLM API: {'✅ 可用' if glm_available else '❌ 不可用'}")
    
    # 测试Gemini客户端
    gemini_available = analyzer.llm_manager.is_available()
    print(f"   Gemini API: {'✅ 可用' if gemini_available else '❌ 不可用'}")
    
    return glm_available or gemini_available

async def test_model_switching():
    """测试模型切换功能"""
    print("🔍 测试模型切换功能...")
    
    analyzer = get_news_impact_analyzer()
    
    test_news = {
        'title': '美联储宣布加息决定',
        'content': '美联储宣布将基准利率上调0.25个百分点，这是今年第三次加息。此举旨在控制通胀，但可能对股市产生负面影响。',
        'source': 'test_source',
        'publish_time': datetime.now().isoformat()
    }
    
    results = {}
    
    # 测试GLM模型
    try:
        glm_result = await analyzer.analyze_news(test_news, model='glm')
        results['glm'] = glm_result.get('success', False)
        print(f"   GLM切换测试: {'✅ 成功' if results['glm'] else '❌ 失败'}")
    except Exception as e:
        results['glm'] = False
        print(f"   GLM切换测试: ❌ 异常 - {e}")
    
    # 测试Gemini模型
    try:
        gemini_result = await analyzer.analyze_news(test_news, model='gemini')
        results['gemini'] = gemini_result.get('success', False)
        print(f"   Gemini切换测试: {'✅ 成功' if results['gemini'] else '❌ 失败'}")
    except Exception as e:
        results['gemini'] = False
        print(f"   Gemini切换测试: ❌ 异常 - {e}")
    
    return any(results.values())

def test_environment_setup():
    """测试环境配置"""
    print("🔍 检查环境配置...")
    
    env_vars = {
        'GLM_API_KEY': os.getenv('GLM_API_KEY'),
        'GEMINI_API_KEY': os.getenv('GEMINI_API_KEY'),
        'GEMINI_MODEL': os.getenv('GEMINI_MODEL')
    }
    
    for var, value in env_vars.items():
        status = '✅ 已配置' if value else '❌ 未配置'
        print(f"   {var}: {status}")
    
    return bool(env_vars['GLM_API_KEY'] or env_vars['GEMINI_API_KEY'])

async def main():
    """主测试函数"""
    print("🚀 开始AI市场影响分析模型选择功能测试\n")
    
    tests = [
        ("环境配置检查", test_environment_setup),
        ("API可用性测试", test_api_availability),
        ("GLM模型测试", test_glm_analysis),
        ("Gemini模型测试", test_gemini_analysis),
        ("模型切换测试", test_model_switching)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试汇总
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print(f"\n通过: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！AI市场影响分析模型选择功能正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查配置和服务状态。")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️  测试中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试失败: {e}")
        sys.exit(1) 