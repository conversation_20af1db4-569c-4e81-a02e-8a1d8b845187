#!/usr/bin/env python3
"""
调试AAPL数据问题
检查数据库中AAPL的数据情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.data_manager import init_data_manager
from datetime import datetime, timedelta

def debug_aapl_data():
    """调试AAPL数据问题"""
    
    print("=" * 60)
    print("🔍 调试AAPL数据问题")
    print("=" * 60)
    
    # 初始化数据管理器
    try:
        data_manager = init_data_manager("test_token")
        print("✅ 数据管理器初始化成功")
    except Exception as e:
        print(f"❌ 数据管理器初始化失败: {e}")
        return
    
    symbol = "AAPL"
    
    # 检查不同时间范围的数据
    timeframes = {
        "1个月": 30,
        "3个月": 90,
        "6个月": 180,
        "1年": 365
    }
    
    end_date = datetime.now()
    
    for name, days in timeframes.items():
        start_date = end_date - timedelta(days=days)
        start_date_str = start_date.strftime("%Y%m%d")
        end_date_str = end_date.strftime("%Y%m%d")
        
        print(f"\n🔍 检查 {name} 数据 ({start_date_str} 到 {end_date_str}):")
        
        try:
            # 直接查询数据库
            df = data_manager.get_stock_data(symbol, start_date_str, end_date_str)
            
            if df.empty:
                print(f"   ❌ 数据库中无 {name} 数据")
            else:
                print(f"   ✅ 找到 {len(df)} 条记录")
                print(f"   📅 日期范围: {df['trade_date'].min()} 到 {df['trade_date'].max()}")
                
                # 检查数据是否有差异
                if len(df) > 0:
                    sample_data = df.tail(3)[['trade_date', 'close', 'volume']].to_dict('records')
                    for record in sample_data:
                        print(f"   📊 {record['trade_date']}: 收盘价={record['close']:.2f}, 成交量={record['volume']}")
                        
        except Exception as e:
            print(f"   ❌ 查询错误: {e}")
    
    # 检查数据库中所有AAPL数据
    print(f"\n🔍 检查数据库中所有 {symbol} 数据:")
    try:
        all_data = data_manager.get_stock_data(symbol)
        if all_data.empty:
            print("   ❌ 数据库中完全没有AAPL数据")
            print("   💡 这就是问题所在！没有数据，所以不同时间范围返回相同结果")
        else:
            print(f"   ✅ 数据库中共有 {len(all_data)} 条AAPL记录")
            print(f"   📅 完整日期范围: {all_data['trade_date'].min()} 到 {all_data['trade_date'].max()}")
            
            # 检查是否是模拟数据（模拟数据通常有固定模式）
            unique_closes = all_data['close'].nunique()
            total_records = len(all_data)
            if unique_closes < total_records * 0.8:  # 如果80%以上的收盘价都不同，可能是真实数据
                print("   ⚠️  数据可能是模拟数据（收盘价变化较少）")
            else:
                print("   ✅ 数据看起来是真实数据")
                
    except Exception as e:
        print(f"   ❌ 查询所有数据错误: {e}")
    
    # 检查智能数据获取
    print(f"\n🔍 测试智能数据获取:")
    try:
        start_date_str = (end_date - timedelta(days=30)).strftime("%Y%m%d")
        end_date_str = end_date.strftime("%Y%m%d")
        
        print(f"   尝试智能获取 {symbol} 数据 ({start_date_str} 到 {end_date_str})")
        smart_data = data_manager.get_stock_data_intelligent(
            symbol, start_date_str, end_date_str, force_download=False
        )
        
        if smart_data.empty:
            print("   ❌ 智能获取也没有数据")
        else:
            print(f"   ✅ 智能获取到 {len(smart_data)} 条数据")
            
    except Exception as e:
        print(f"   ❌ 智能获取错误: {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print("🎯 问题分析:")
    print("=" * 60)
    print("1. 如果数据库中没有AAPL数据，这解释了为什么不同时间范围返回相同结果")
    print("2. 需要确保因子计算API能正确触发数据下载")
    print("3. 如果是模拟数据，需要使用真实数据或改进模拟算法")
    print("=" * 60)

if __name__ == "__main__":
    debug_aapl_data() 