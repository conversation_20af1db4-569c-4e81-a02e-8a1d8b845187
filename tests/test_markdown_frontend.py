#!/usr/bin/env python3
"""
测试前端 Markdown 渲染功能
"""

import requests
import json
import time

def test_ai_agent_with_markdown():
    """测试 AI Agent 返回 markdown 格式的响应"""
    
    # 后端 API 地址
    base_url = "http://localhost:8000"
    
    # 测试数据
    test_query = "苹果公司的投资建议"
    
    print("🧪 测试前端 Markdown 渲染功能")
    print("=" * 50)
    
    try:
        # 发送请求到 AI Agent
        print(f"📤 发送请求: {test_query}")
        
        response = requests.post(
            f"{base_url}/chat/stream",
            json={"message": test_query},
            headers={"Content-Type": "application/json"},
            stream=True,
            timeout=60
        )
        
        if response.status_code == 200:
            print("✅ 后端响应成功")
            print("📝 AI 分析结果 (Markdown 格式):")
            print("-" * 40)
            
            # 处理流式响应
            full_content = ""
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        if data.get('type') == 'message' and data.get('content'):
                            content = data['content']
                            full_content += content
                            print(content, end='', flush=True)
                        elif data.get('type') == 'done':
                            break
                    except json.JSONDecodeError:
                        continue
            
            print("\n" + "-" * 40)
            print("✅ 测试完成！")
            print("\n📋 测试说明:")
            print("1. 后端已成功返回 Markdown 格式的分析结果")
            print("2. 前端应该能够自动渲染以下 Markdown 元素:")
            print("   - 标题 (# ## ###)")
            print("   - 列表 (- 1.)")
            print("   - 粗体 (**文本**)")
            print("   - 代码块 (```)")
            print("   - 表格 (| | |)")
            print("   - 链接 ([文本](URL))")
            print("   - 引用 (> 文本)")
            print("   - 分割线 (---)")
            
            print("\n🌐 前端测试步骤:")
            print("1. 打开浏览器访问 http://localhost:3000")
            print("2. 点击 'AI智能分析' 模块")
            print("3. 输入查询: '苹果公司的投资建议'")
            print("4. 观察 AI 回复是否正确渲染 Markdown 格式")
            print("5. 或者点击 'Markdown 测试' 模块查看测试页面")
            
        else:
            print(f"❌ 后端响应失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到后端服务")
        print("请确保后端服务正在运行: python -m uvicorn backend.server:app --host 0.0.0.0 --port 8000 --reload")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")

def check_frontend_status():
    """检查前端服务状态"""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正在运行 (http://localhost:3000)")
            return True
        else:
            print(f"⚠️ 前端服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 前端服务未运行")
        print("请启动前端服务: cd frontend && npm start")
        return False
    except Exception as e:
        print(f"❌ 检查前端服务时出错: {str(e)}")
        return False

def main():
    print("🚀 开始测试 Markdown 渲染功能")
    print("=" * 50)
    
    # 检查前端服务
    print("1. 检查前端服务状态...")
    frontend_ok = check_frontend_status()
    
    print("\n2. 检查后端服务状态...")
    # 测试后端 AI Agent
    test_ai_agent_with_markdown()
    
    print("\n" + "=" * 50)
    if frontend_ok:
        print("🎉 测试完成！请在浏览器中验证 Markdown 渲染效果")
        print("🔗 前端地址: http://localhost:3000")
    else:
        print("⚠️ 请先启动前端服务再进行测试")

if __name__ == "__main__":
    main() 