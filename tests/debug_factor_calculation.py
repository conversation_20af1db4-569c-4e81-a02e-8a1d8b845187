#!/usr/bin/env python3
"""
调试因子计算过程
验证数据格式和因子计算的详细过程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.data_manager import init_data_manager
from backend.enhanced_factors import FactorManager

def debug_factor_calculation():
    """调试因子计算的详细过程"""
    
    print("=" * 80)
    print("🔬 调试因子计算过程")
    print("=" * 80)
    
    # 初始化
    data_manager = init_data_manager("test_token")
    factor_manager = FactorManager(data_manager)
    
    symbol = "AAPL"
    factor_names = ["rsi", "macd", "sma20"]
    
    print(f"📊 测试股票: {symbol}")
    print(f"📊 测试因子: {factor_names}")
    print()
    
    # 第1步：获取原始数据
    print("1️⃣ 获取原始数据...")
    data = data_manager.get_stock_data(symbol)
    print(f"   数据形状: {data.shape}")
    print(f"   列名: {list(data.columns)}")
    print(f"   数据类型:\n{data.dtypes}")
    
    if not data.empty:
        print(f"   前3行数据:\n{data.head(3)}")
        print(f"   最后3行数据:\n{data.tail(3)}")
    
    # 第2步：检查数据验证
    print("\n2️⃣ 验证数据...")
    is_valid = factor_manager.validate_factor_data(data)
    print(f"   数据验证结果: {is_valid}")
    
    # 第3步：逐个检查必需列
    print("\n3️⃣ 检查必需列...")
    required_columns = ['close', 'high', 'low', 'volume']
    for col in required_columns:
        exists = col in data.columns
        print(f"   {col}: {'✅' if exists else '❌'}")
        if exists:
            data_values = data[col].values
            print(f"      数据长度: {len(data_values)}")
            print(f"      数据范围: {data_values.min():.4f} ~ {data_values.max():.4f}")
            print(f"      NaN数量: {sum(pd.isna(data_values))}")
    
    # 第4步：尝试因子计算
    print("\n4️⃣ 尝试因子计算...")
    try:
        factors = factor_manager.calculate_factors_for_stock(symbol, factor_names)
        print(f"   计算结果: {factors}")
        if factors:
            print("   ✅ 因子计算成功!")
            for factor_name, value in factors.items():
                print(f"      {factor_name}: {value}")
        else:
            print("   ❌ 因子计算返回空结果")
    except Exception as e:
        print(f"   ❌ 因子计算出错: {e}")
        import traceback
        print(f"   详细错误:\n{traceback.format_exc()}")
    
    # 第5步：尝试直接调用计算器
    print("\n5️⃣ 直接调用因子计算器...")
    try:
        calculator = factor_manager.calculator
        all_factors = calculator.calculate_all_enhanced_factors(data)
        print(f"   计算结果数量: {len(all_factors)}")
        if all_factors:
            print("   ✅ 直接计算成功!")
            # 只显示前5个因子
            for i, (factor_name, value) in enumerate(all_factors.items()):
                if i < 5:
                    print(f"      {factor_name}: {value}")
                else:
                    break
            if len(all_factors) > 5:
                print(f"      ... 还有 {len(all_factors) - 5} 个因子")
        else:
            print("   ❌ 直接计算返回空结果")
    except Exception as e:
        print(f"   ❌ 直接计算出错: {e}")
        import traceback
        print(f"   详细错误:\n{traceback.format_exc()}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    import pandas as pd
    debug_factor_calculation() 