#!/usr/bin/env python3
"""
测试新的capabilities工具功能
"""

import asyncio
import json
import logging
from backend.server import stream_workflow_response

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_capabilities_tool():
    """测试capabilities工具是否正常工作"""
    
    test_cases = [
        "你能做什么？",
        "你有什么功能？",
        "介绍一下你的功能",
        "你能帮我什么？",
        "What can you help me with?",
        "What are your capabilities?",
        "Tell me about your features",
        "help"
    ]
    
    print("🧪 测试Capabilities工具功能")
    print("=" * 50)
    
    for user_input in test_cases:
        print(f"\n📝 测试输入: '{user_input}'")
        print("-" * 30)
        
        responses = []
        response_types = []
        
        try:
            async for response_chunk in stream_workflow_response(
                user_input=user_input,
                max_plan_iterations=1,
                max_step_num=3,
                enable_background_investigation=True,
                debug=False
            ):
                # 解析SSE格式的响应
                if response_chunk.startswith("data: "):
                    data_str = response_chunk[6:].strip()
                    if data_str:
                        try:
                            data = json.loads(data_str)
                            response_type = data.get("type", "unknown")
                            content = data.get("content", "")
                            
                            responses.append(content)
                            response_types.append(response_type)
                            
                            if response_type == "final_report":
                                print(f"   📨 {response_type}: {content[:200]}...")
                            else:
                                print(f"   📨 {response_type}: {content[:100]}...")
                            
                        except json.JSONDecodeError:
                            print(f"   ⚠️  无法解析JSON: {data_str}")
            
            # 分析响应
            final_report_count = response_types.count("final_report")
            
            print(f"\n   📊 响应统计:")
            print(f"   - Final Report 类型响应: {final_report_count}")
            print(f"   - 总响应数: {len(responses)}")
            
            # 检查是否包含能力介绍内容
            if final_report_count == 1:
                final_report = responses[response_types.index("final_report")]
                if "DeerFlow" in final_report and ("核心能力" in final_report or "Core Capabilities" in final_report):
                    print(f"   ✅ 成功展示了系统能力")
                else:
                    print(f"   ❌ 响应内容不包含能力介绍")
            else:
                print(f"   ❌ 响应异常：期望1个final_report，实际{final_report_count}个")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_capabilities_tool()) 