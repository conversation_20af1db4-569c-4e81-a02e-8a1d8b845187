#!/usr/bin/env python3
"""
调试API调用过程
模拟前端的API调用，找出问题所在
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json
from datetime import datetime, timedelta

def debug_api_call():
    """调试API调用过程"""
    
    print("=" * 80)
    print("🌐 调试API调用过程")
    print("=" * 80)
    
    # 准备API调用参数
    url = "http://localhost:8000/factors/calculate/batch"
    
    # 计算时间范围（模拟前端逻辑）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=180)  # 6个月
    
    payload = {
        "symbols": ["AAPL"],
        "factor_names": ["rsi", "macd", "sma20"],
        "start_date": start_date.strftime("%Y%m%d"),
        "end_date": end_date.strftime("%Y%m%d")
    }
    
    print(f"📊 API端点: {url}")
    print(f"📊 请求参数:")
    for key, value in payload.items():
        print(f"   {key}: {value}")
    print()
    
    # 第1步：发送API请求
    print("1️⃣ 发送API请求...")
    try:
        response = requests.post(url, json=payload, timeout=10)
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("   ✅ API请求成功!")
            try:
                data = response.json()
                print(f"   响应数据类型: {type(data)}")
                print(f"   响应数据内容: {data}")
                
                # 检查响应数据结构
                if isinstance(data, dict):
                    if 'success' in data:
                        print(f"   success字段: {data.get('success')}")
                    if 'data' in data:
                        factor_data = data.get('data')
                        print(f"   data字段类型: {type(factor_data)}")
                        print(f"   data字段内容: {factor_data}")
                    if 'error' in data:
                        print(f"   error字段: {data.get('error')}")
                
            except json.JSONDecodeError as e:
                print(f"   ❌ JSON解析失败: {e}")
                print(f"   原始响应: {response.text}")
        else:
            print(f"   ❌ API请求失败: {response.status_code}")
            print(f"   错误信息: {response.text}")
            
    except Exception as e:
        print(f"   ❌ API请求异常: {e}")
    
    # 第2步：测试不同的参数组合
    print("\n2️⃣ 测试不同参数组合...")
    
    test_cases = [
        {
            "name": "无时间范围",
            "payload": {
                "symbols": ["AAPL"],
                "factor_names": ["rsi", "macd"]
            }
        },
        {
            "name": "单个因子",
            "payload": {
                "symbols": ["AAPL"],
                "factor_names": ["rsi"],
                "start_date": start_date.strftime("%Y%m%d"),
                "end_date": end_date.strftime("%Y%m%d")
            }
        },
        {
            "name": "多个股票",
            "payload": {
                "symbols": ["AAPL", "MSFT"],
                "factor_names": ["rsi", "macd"],
                "start_date": start_date.strftime("%Y%m%d"),
                "end_date": end_date.strftime("%Y%m%d")
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n   🧪 测试: {test_case['name']}")
        try:
            response = requests.post(url, json=test_case['payload'], timeout=10)
            print(f"      状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"      响应: {data}")
            else:
                print(f"      错误: {response.text}")
        except Exception as e:
            print(f"      异常: {e}")
    
    # 第3步：检查健康状态
    print("\n3️⃣ 检查后端健康状态...")
    try:
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"   健康检查状态码: {health_response.status_code}")
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"   健康数据: {health_data}")
    except Exception as e:
        print(f"   健康检查失败: {e}")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    debug_api_call() 