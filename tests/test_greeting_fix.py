#!/usr/bin/env python3
"""
测试问候语重复问题的修复
"""

import asyncio
import json
import logging
from backend.server import stream_workflow_response

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_greeting_response():
    """测试问候语响应是否正确"""
    
    test_cases = [
        "你好",
        "你是谁",
        "hello",
        "hi"
    ]
    
    print("🧪 测试问候语重复问题修复")
    print("=" * 50)
    
    for user_input in test_cases:
        print(f"\n📝 测试输入: '{user_input}'")
        print("-" * 30)
        
        responses = []
        response_types = []
        
        try:
            async for response_chunk in stream_workflow_response(
                user_input=user_input,
                max_plan_iterations=1,
                max_step_num=3,
                enable_background_investigation=True,
                debug=False
            ):
                # 解析SSE格式的响应
                if response_chunk.startswith("data: "):
                    data_str = response_chunk[6:].strip()
                    if data_str:
                        try:
                            data = json.loads(data_str)
                            response_type = data.get("type", "unknown")
                            content = data.get("content", "")
                            
                            responses.append(content)
                            response_types.append(response_type)
                            
                            print(f"   📨 {response_type}: {content[:100]}...")
                            
                        except json.JSONDecodeError:
                            print(f"   ⚠️  无法解析JSON: {data_str}")
            
            # 分析响应
            message_count = response_types.count("message")
            final_report_count = response_types.count("final_report")
            
            print(f"\n   📊 响应统计:")
            print(f"   - Message 类型响应: {message_count}")
            print(f"   - Final Report 类型响应: {final_report_count}")
            print(f"   - 总响应数: {len(responses)}")
            
            # 检查是否有重复
            if message_count == 0 and final_report_count == 1:
                print(f"   ✅ 响应正确：只有1个final_report，无重复")
            elif message_count > 0 and final_report_count > 0:
                print(f"   ❌ 响应重复：同时有{message_count}个message和{final_report_count}个final_report")
            elif final_report_count > 1:
                print(f"   ❌ 响应重复：有{final_report_count}个final_report")
            else:
                print(f"   ⚠️  响应异常：{message_count}个message，{final_report_count}个final_report")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_greeting_response()) 