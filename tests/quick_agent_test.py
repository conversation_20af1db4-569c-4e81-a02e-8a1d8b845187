#!/usr/bin/env python3
"""
快速多智能体测试脚本
专注于核心功能的快速验证
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

# 简单的日志函数
def log(msg):
    print(f"[{time.strftime('%H:%M:%S')}] {msg}")

async def test_basic_components():
    """测试基础组件"""
    log("测试基础组件导入...")
    
    try:
        from backend.ai import AIWorkflowManager, AgentManager, LLMManager
        log("✓ 基础组件导入成功")
        return True
    except ImportError as e:
        log(f"✗ 基础组件导入失败: {e}")
        return False

async def test_llm_basic():
    """测试 LLM 基础功能"""
    log("测试 LLM 基础功能...")
    
    try:
        from backend.ai import LLMManager
        
        llm = LLMManager()
        response = await llm.get_completion("你好，请简单回复", temperature=0.3)
        
        if response and len(response.strip()) > 0:
            log(f"✓ LLM 响应正常 (长度: {len(response)})")
            return True
        else:
            log("✗ LLM 响应为空")
            return False
            
    except Exception as e:
        log(f"✗ LLM 测试失败: {e}")
        return False

async def test_agent_tools():
    """测试智能体工具"""
    log("测试智能体工具...")
    
    try:
        from backend.ai import AgentManager
        
        agent_manager = AgentManager()
        tools = agent_manager.available_tools
        
        log(f"✓ 可用工具数量: {len(tools)}")
        
        # 列出关键工具
        key_tools = ['web_search', 'python_repl', 'crawl']
        available_key_tools = [tool for tool in key_tools if tool in tools]
        log(f"✓ 关键工具可用: {available_key_tools}")
        
        return len(tools) > 0
        
    except Exception as e:
        log(f"✗ 工具测试失败: {e}")
        return False

async def test_graph_structure():
    """测试图结构"""
    log("测试图结构...")
    
    try:
        from backend.ai.graph import build_graph
        
        graph = build_graph()
        
        # 尝试不同的方法获取图信息
        try:
            # 方法1: 尝试 get_graph()
            graph_info = graph.get_graph()
            if hasattr(graph_info, 'nodes'):
                nodes = list(graph_info.nodes.keys())
                log(f"✓ 图节点数: {len(nodes)}")
            else:
                nodes = []
        except:
            # 方法2: 直接访问节点（备用方案）
            nodes = []
            log("✓ 图构建成功（无法获取详细节点信息）")
        
        # 检查关键节点（如果能获取到节点信息）
        if nodes:
            key_nodes = ['coordinator', 'planner', 'researcher', 'technical_analyst', 'reporter']
            missing_nodes = [node for node in key_nodes if node not in nodes]
            
            if not missing_nodes:
                log("✓ 所有关键节点都存在")
            else:
                log(f"! 部分关键节点缺失: {missing_nodes}")
                log(f"  实际节点: {nodes}")
        
        log("✓ 图结构测试通过")
        return True
            
    except Exception as e:
        log(f"✗ 图结构测试失败: {e}")
        return False

async def test_simple_workflow():
    """测试简单工作流"""
    log("测试简单工作流...")
    
    try:
        from backend.ai import AIWorkflowManager
        
        workflow = AIWorkflowManager()
        
        # 测试简单查询
        query = "你好"
        response_count = 0
        
        async for response in workflow.process_user_query(query):
            response_count += 1
            log(f"收到响应 {response_count}: {response.get('type', 'unknown')}")
            
            if response_count >= 2:  # 限制响应数量
                break
        
        if response_count > 0:
            log(f"✓ 工作流响应正常 (响应数: {response_count})")
            return True
        else:
            log("✗ 工作流无响应")
            return False
            
    except Exception as e:
        log(f"✗ 工作流测试失败: {e}")
        return False

async def test_technical_analysis():
    """测试技术分析功能"""
    log("测试技术分析功能...")
    
    try:
        from backend.ai import AgentManager
        import pandas as pd
        import numpy as np
        
        agent_manager = AgentManager()
        
        # 创建模拟数据
        dates = pd.date_range('2024-01-01', periods=20, freq='D')
        mock_data = pd.DataFrame({
            'close': np.random.uniform(100, 200, 20),
            'high': np.random.uniform(110, 220, 20),
            'low': np.random.uniform(90, 190, 20),
            'volume': np.random.randint(1000000, 10000000, 20)
        }, index=dates)
        
        mock_factors = {'rsi': 65.5, 'macd': 0.5}
        
        result = await agent_manager.get_technical_analysis("AAPL", mock_data, mock_factors)
        
        if isinstance(result, dict) and "agent_type" in result:
            log("✓ 技术分析功能正常")
            return True
        else:
            log("✗ 技术分析结果格式错误")
            return False
            
    except Exception as e:
        log(f"✗ 技术分析测试失败: {e}")
        return False

async def quick_test():
    """快速测试主函数"""
    log("="*50)
    log("开始快速多智能体系统测试")
    log("="*50)
    
    tests = [
        ("基础组件", test_basic_components),
        ("LLM基础功能", test_llm_basic),
        ("智能体工具", test_agent_tools),
        ("图结构", test_graph_structure),
        ("简单工作流", test_simple_workflow),
        ("技术分析", test_technical_analysis),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        log(f"\n--- {test_name} ---")
        try:
            success = await test_func()
            if success:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            log(f"✗ {test_name} 异常: {e}")
            failed += 1
    
    log("\n" + "="*50)
    log("测试结果汇总")
    log("="*50)
    log(f"总测试数: {passed + failed}")
    log(f"通过: {passed}")
    log(f"失败: {failed}")
    
    if failed == 0:
        log("🎉 所有测试通过！")
        return True
    else:
        log(f"❌ {failed} 个测试失败")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(quick_test())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试运行错误: {e}")
        sys.exit(1) 