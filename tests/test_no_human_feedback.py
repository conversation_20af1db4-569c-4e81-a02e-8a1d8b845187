#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试删除人工反馈节点后的Agent系统工作流
"""

import asyncio
import logging
from backend.ai.graph.builder import build_graph
from backend.ai.graph.types import State
from langchain_core.messages import HumanMessage

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_workflow_without_human_feedback():
    """测试无人工反馈的工作流程"""
    
    logger.info("🚀 开始测试无人工反馈的Agent工作流")
    
    # 构建图
    try:
        graph = build_graph()
        logger.info("✅ Agent工作流图构建成功")
    except Exception as e:
        logger.error(f"❌ 工作流图构建失败: {e}")
        return False
    
    # 准备测试输入
    test_query = "分析一下苹果公司(AAPL)的股票投资价值"
    
    initial_state = State(
        messages=[HumanMessage(content=test_query)],
        locale="zh-CN",
        enable_background_investigation=False,  # 为了快速测试，关闭背景调查
        auto_accepted_plan=True,  # 自动接受计划
    )
    
    logger.info(f"📝 测试查询: {test_query}")
    
    try:
        # 运行工作流
        logger.info("🔄 开始运行工作流...")
        
        config = {"configurable": {"thread_id": "test_thread_001"}}
        
        # 运行单个步骤进行测试
        result = await graph.ainvoke(
            initial_state,
            config=config
        )
        
        logger.info("✅ 工作流执行完成")
        
        # 检查结果
        if result:
            logger.info("📊 工作流执行结果:")
            logger.info(f"  - 消息数量: {len(result.get('messages', []))}")
            logger.info(f"  - 语言环境: {result.get('locale', 'N/A')}")
            logger.info(f"  - 计划迭代次数: {result.get('plan_iterations', 0)}")
            logger.info(f"  - 自动接受计划: {result.get('auto_accepted_plan', False)}")
            
            if result.get('current_plan'):
                plan = result['current_plan']
                logger.info(f"  - 计划标题: {getattr(plan, 'title', 'N/A')}")
                if hasattr(plan, 'steps'):
                    logger.info(f"  - 计划步骤数量: {len(plan.steps)}")
            
            # 检查报告生成
            reports = {
                'bullish_report': result.get('bullish_report', ''),
                'bearish_report': result.get('bearish_report', ''),
                'trading_advice_report': result.get('trading_advice_report', ''),
                'comprehensive_final_report': result.get('comprehensive_final_report', ''),
                'final_report': result.get('final_report', '')
            }
            
            for report_name, report_content in reports.items():
                if report_content:
                    logger.info(f"  - {report_name}: 已生成 ({len(report_content)} 字符)")
                else:
                    logger.info(f"  - {report_name}: 未生成")
            
            return True
        else:
            logger.warning("⚠️ 工作流返回空结果")
            return False
            
    except Exception as e:
        logger.error(f"❌ 工作流执行失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

def test_graph_structure():
    """测试图结构的完整性"""
    logger.info("🔍 测试工作流图结构")
    
    try:
        graph = build_graph()
        
        # 检查图的基本属性
        logger.info("📋 工作流图节点信息:")
        
        # 获取所有节点 - 修复方法调用
        try:
            # 尝试获取节点信息
            graph_def = graph.get_graph()
            nodes = list(graph_def.nodes())
        except Exception as e:
            logger.warning(f"无法通过get_graph()获取节点: {e}")
            # 尝试其他方式获取节点信息
            if hasattr(graph, 'nodes'):
                nodes = list(graph.nodes.keys()) if hasattr(graph.nodes, 'keys') else []
            else:
                logger.warning("无法获取节点列表，跳过详细检查")
                nodes = []
        
        logger.info(f"  - 检测到的节点数量: {len(nodes)}")
        
        if nodes:
            for node in nodes:
                logger.info(f"    * {node}")
            
            # 检查是否不包含human_feedback节点
            if 'human_feedback' in nodes:
                logger.error("❌ 发现human_feedback节点，删除未成功")
                return False
            else:
                logger.info("✅ 确认human_feedback节点已删除")
            
            # 检查关键节点是否存在
            required_nodes = [
                'coordinator', 'planner', 'research_team',
                'financial_report_coordinator', 'final_comprehensive_reporter'
            ]
            
            missing_nodes = [node for node in required_nodes if node not in nodes]
            if missing_nodes:
                logger.error(f"❌ 缺少关键节点: {missing_nodes}")
                return False
            else:
                logger.info("✅ 所有关键节点都存在")
        else:
            logger.info("ℹ️ 无法获取详细节点信息，但图构建成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 图结构测试失败: {e}")
        import traceback
        logger.error(f"错误详情: {traceback.format_exc()}")
        return False

async def main():
    """主测试函数"""
    logger.info("=" * 50)
    logger.info("🧪 Agent系统无人工反馈测试")
    logger.info("=" * 50)
    
    # 测试1: 图结构完整性
    logger.info("\n📋 测试1: 图结构完整性")
    structure_ok = test_graph_structure()
    
    if not structure_ok:
        logger.error("❌ 图结构测试失败，停止后续测试")
        return
    
    # 测试2: 工作流执行
    logger.info("\n🔄 测试2: 工作流执行")
    workflow_ok = await test_workflow_without_human_feedback()
    
    # 总结
    logger.info("\n" + "=" * 50)
    logger.info("📊 测试总结")
    logger.info("=" * 50)
    logger.info(f"图结构测试: {'✅ 通过' if structure_ok else '❌ 失败'}")
    logger.info(f"工作流测试: {'✅ 通过' if workflow_ok else '❌ 失败'}")
    
    if structure_ok and workflow_ok:
        logger.info("🎉 所有测试通过！无人工反馈的Agent系统工作正常")
    else:
        logger.error("⚠️ 存在测试失败，请检查系统配置")

if __name__ == "__main__":
    asyncio.run(main()) 