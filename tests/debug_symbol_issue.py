#!/usr/bin/env python3
"""
调试symbol和ts_code转换问题
检查数据库中AAPL数据的实际存储格式
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.data_manager import init_data_manager
import sqlite3

def debug_symbol_issue():
    """调试symbol和ts_code的转换问题"""
    
    print("=" * 60)
    print("🔍 调试Symbol和TS_Code转换问题")
    print("=" * 60)
    
    # 初始化数据管理器
    data_manager = init_data_manager("test_token")
    db_path = "/Users/<USER>/Code/cash-flow/backend/data/financial_data.db"
    
    print("1. 检查数据库中的AAPL相关数据...")
    
    with sqlite3.connect(db_path) as conn:
        conn.row_factory = sqlite3.Row
        
        # 检查stock_info表中的AAPL信息
        print("\n📊 检查stock_info表中的AAPL...")
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM stock_info WHERE symbol LIKE '%AAPL%' OR ts_code LIKE '%AAPL%'")
        stock_info_records = cursor.fetchall()
        
        if stock_info_records:
            for record in stock_info_records:
                print(f"   ts_code: {record['ts_code']}, symbol: {record['symbol']}, name: {record['name']}")
        else:
            print("   ❌ 未找到AAPL的stock_info记录")
        
        # 检查各个数据表中的AAPL数据
        tables = ['cn_stocks', 'us_stocks', 'hk_stocks']
        
        for table in tables:
            print(f"\n📊 检查{table}表中的AAPL...")
            try:
                cursor.execute(f"SELECT DISTINCT ts_code FROM {table} WHERE ts_code LIKE '%AAPL%' LIMIT 5")
                records = cursor.fetchall()
                if records:
                    for record in records:
                        print(f"   找到ts_code: {record['ts_code']}")
                    
                    # 获取第一个ts_code的数据量
                    first_ts_code = records[0]['ts_code']
                    cursor.execute(f"SELECT COUNT(*) as count FROM {table} WHERE ts_code = ?", (first_ts_code,))
                    count_result = cursor.fetchone()
                    print(f"   数据量: {count_result['count']} 条")
                else:
                    print(f"   ❌ {table}表中未找到AAPL数据")
            except Exception as e:
                print(f"   ❌ 查询{table}表失败: {e}")
    
    print("\n2. 测试不同的symbol格式...")
    
    # 测试不同的symbol格式
    test_symbols = ["AAPL", "AAPL.US", "AAPL.O", "AAPL.NASDAQ"]
    
    for symbol in test_symbols:
        print(f"\n🧪 测试symbol: {symbol}")
        try:
            data = data_manager.get_stock_data(symbol)
            print(f"   结果: 获取到 {len(data)} 条记录")
            if not data.empty:
                print(f"   列名: {list(data.columns)}")
                print(f"   日期范围: {data['trade_date'].min()} 到 {data['trade_date'].max()}")
                break
        except Exception as e:
            print(f"   错误: {e}")
    
    print("\n3. 测试数据管理器的智能获取方法...")
    
    try:
        data = data_manager.get_stock_data_intelligent("AAPL")
        print(f"智能获取结果: {len(data)} 条记录")
        if not data.empty:
            print(f"列名: {list(data.columns)}")
    except Exception as e:
        print(f"智能获取失败: {e}")

if __name__ == "__main__":
    debug_symbol_issue() 