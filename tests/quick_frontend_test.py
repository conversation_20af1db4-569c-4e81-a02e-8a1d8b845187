#!/usr/bin/env python3
"""
快速前端兼容性测试
专门测试新的金融多报告系统是否能与前端正常工作
"""

import asyncio
import json
import aiohttp
import time

async def test_financial_reports_compatibility():
    """测试金融多报告系统的前端兼容性"""
    print("🚀 测试金融多报告系统前端兼容性...")
    
    # 1. 检查后端状态结构
    print("\n1️⃣ 检查后端状态结构...")
    try:
        from backend.ai.graph.types import State
        
        # 检查新增的报告字段
        required_fields = [
            'bullish_report',
            'bearish_report', 
            'trading_advice_report',
            'comprehensive_final_report',
            'final_report'
        ]
        
        state_annotations = getattr(State, '__annotations__', {})
        missing_fields = [field for field in required_fields if field not in state_annotations]
        
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields}")
            return False
        else:
            print("✅ 所有必需的报告字段都存在")
    
    except ImportError as e:
        print(f"❌ 无法导入后端模块: {e}")
        return False
    
    # 2. 检查前端StreamData接口
    print("\n2️⃣ 检查前端接口定义...")
    try:
        with open("frontend/src/App.tsx", "r", encoding="utf-8") as f:
            frontend_code = f.read()
        
        # 检查StreamData接口是否包含final_report
        if "'final_report'" in frontend_code and "data.type === 'final_report'" in frontend_code:
            print("✅ 前端已支持final_report处理")
        else:
            print("⚠️ 前端可能需要更新final_report处理逻辑")
    
    except FileNotFoundError:
        print("❌ 无法找到前端App.tsx文件")
        return False
    
    # 3. 测试API响应
    print("\n3️⃣ 测试API响应...")
    url = "http://127.0.0.1:8000/chat/stream"
    headers = {"Content-Type": "application/json"}
    payload = {
        "message": "简单测试金融报告系统",
        "max_plan_iterations": 1,
        "max_step_num": 2,
        "enable_background_investigation": False,
        "debug": True
    }
    
    try:
        timeout = aiohttp.ClientTimeout(total=30)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status != 200:
                    print(f"❌ API响应错误: {response.status}")
                    return False
                
                print("✅ API连接成功")
                
                # 读取前几行响应
                line_count = 0
                async for line in response.content:
                    if line_count >= 5:  # 只读取前5行
                        break
                    
                    line_str = line.decode('utf-8').strip()
                    if line_str.startswith('data: '):
                        try:
                            data = json.loads(line_str[6:])
                            event_type = data.get('type', 'unknown')
                            print(f"   📡 收到事件: {event_type}")
                            line_count += 1
                        except json.JSONDecodeError:
                            continue
                
                print("✅ 流式响应正常")
    
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False
    
    # 4. 检查图结构
    print("\n4️⃣ 检查图结构...")
    try:
        from backend.ai.graph.builder import build_graph
        
        graph = build_graph()
        
        # 检查是否包含新的金融报告节点
        financial_nodes = [
            'financial_report_coordinator',
            'bullish_reporter',
            'bearish_reporter', 
            'trading_advice_reporter',
            'final_comprehensive_reporter'
        ]
        
        graph_nodes = list(graph.nodes.keys()) if hasattr(graph, 'nodes') else []
        missing_nodes = [node for node in financial_nodes if node not in graph_nodes]
        
        if missing_nodes:
            print(f"❌ 缺少图节点: {missing_nodes}")
            return False
        else:
            print("✅ 所有金融报告节点都存在")
            print(f"   📊 图节点总数: {len(graph_nodes)}")
    
    except Exception as e:
        print(f"❌ 图结构检查失败: {e}")
        return False
    
    return True

async def test_frontend_data_processing():
    """测试前端数据处理逻辑"""
    print("\n5️⃣ 模拟前端数据处理...")
    
    # 模拟新的金融报告系统产生的数据流
    mock_financial_stream = [
        {"type": "agent_message", "content": "开始金融分析...", "agent": "financial_report_coordinator"},
        {"type": "agent_message", "content": "生成看多报告...", "agent": "bullish_reporter"},
        {"type": "agent_message", "content": "生成看空报告...", "agent": "bearish_reporter"},
        {"type": "agent_message", "content": "制定交易建议...", "agent": "trading_advice_reporter"},
        {"type": "final_report", "content": "# 综合金融分析报告\n\n## 看多观点\n积极因素分析...\n\n## 看空观点\n风险因素分析...\n\n## 交易建议\n具体操作建议...", "agent": "final_comprehensive_reporter"},
        {"type": "workflow_complete", "content": "金融分析完成"}
    ]
    
    # 模拟前端处理逻辑
    messages = []
    final_report = ""
    agents_seen = set()
    
    for data in mock_financial_stream:
        event_type = data.get('type')
        content = data.get('content', '')
        agent = data.get('agent', 'unknown')
        
        if agent:
            agents_seen.add(agent)
        
        if event_type in ['message', 'agent_message']:
            messages.append({
                'type': event_type,
                'agent': agent,
                'content': content
            })
            print(f"   💬 [{agent}]: {content[:30]}...")
        
        elif event_type == 'final_report':
            final_report = content
            print(f"   📊 收到最终报告 ({len(content)} 字符)")
        
        elif event_type == 'workflow_complete':
            print(f"   ✅ 工作流完成")
            break
    
    # 验证处理结果
    expected_agents = {
        'financial_report_coordinator',
        'bullish_reporter', 
        'bearish_reporter',
        'trading_advice_reporter',
        'final_comprehensive_reporter'
    }
    
    if expected_agents.issubset(agents_seen):
        print("✅ 所有预期的金融报告智能体都参与了")
    else:
        missing_agents = expected_agents - agents_seen
        print(f"⚠️ 缺少智能体: {missing_agents}")
    
    if final_report and "看多观点" in final_report and "看空观点" in final_report and "交易建议" in final_report:
        print("✅ 最终报告包含所有必需部分")
    else:
        print("⚠️ 最终报告可能不完整")
    
    return len(messages) > 0 and bool(final_report)

async def main():
    """主测试函数"""
    print("🧪 金融多报告系统前端兼容性测试")
    print("="*50)
    
    # 运行兼容性测试
    compatibility_ok = await test_financial_reports_compatibility()
    
    # 运行数据处理测试
    data_processing_ok = await test_frontend_data_processing()
    
    print("\n" + "="*50)
    print("📋 测试结果总结")
    print("="*50)
    
    if compatibility_ok and data_processing_ok:
        print("🎉 测试通过！新的金融多报告系统与前端完全兼容！")
        print("\n✅ 兼容性确认:")
        print("   - 后端状态结构包含所有必需字段")
        print("   - 前端接口支持final_report处理")
        print("   - API响应正常")
        print("   - 图结构包含所有金融报告节点")
        print("   - 数据流处理逻辑正确")
        
        print("\n💡 使用建议:")
        print("   1. 前端可以直接使用现有的聊天界面")
        print("   2. 新的多报告数据会通过final_report字段传递")
        print("   3. 支持实时流式显示分析过程")
        print("   4. 兼容原有的所有功能")
        
        return True
    else:
        print("❌ 测试失败！发现兼容性问题")
        
        if not compatibility_ok:
            print("   - 后端兼容性问题")
        if not data_processing_ok:
            print("   - 前端数据处理问题")
        
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1) 