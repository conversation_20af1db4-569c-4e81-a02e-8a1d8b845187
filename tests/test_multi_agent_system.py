#!/usr/bin/env python3
"""
多智能体系统测试脚本
测试 AI 智能分析系统的各个组件和工作流
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any, List
import traceback

# 添加项目根目录到 Python 路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_multi_agent.log')
    ]
)
logger = logging.getLogger(__name__)

# 导入系统组件
try:
    from backend.ai import AIWorkflowManager, AgentManager, LLMManager
    from backend.ai.graph import build_graph, build_graph_with_memory
    from backend.ai.config.configuration import Configuration
    COMPONENTS_AVAILABLE = True
except ImportError as e:
    logger.error(f"导入系统组件失败: {e}")
    COMPONENTS_AVAILABLE = False


class MultiAgentTester:
    """多智能体系统测试器"""
    
    def __init__(self):
        self.workflow_manager = None
        self.agent_manager = None
        self.llm_manager = None
        self.graph = None
        self.test_results = []
        
    async def initialize(self):
        """初始化测试环境"""
        logger.info("正在初始化测试环境...")
        
        try:
            # 初始化各个管理器
            self.workflow_manager = AIWorkflowManager()
            self.agent_manager = AgentManager()
            self.llm_manager = LLMManager()
            
            # 初始化图结构
            self.graph = build_graph()
            
            logger.info("测试环境初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            logger.error(traceback.format_exc())
            return False
    
    async def test_llm_manager(self):
        """测试 LLM 管理器"""
        test_name = "LLM管理器测试"
        logger.info(f"开始 {test_name}")
        
        try:
            # 测试基本完成功能
            response = await self.llm_manager.get_completion(
                "请简单介绍一下你自己",
                temperature=0.5
            )
            
            assert response and len(response.strip()) > 0, "LLM响应为空"
            
            # 测试用户意图分析
            intent_result = await self.llm_manager.analyze_user_intent(
                "我想分析苹果公司的股票技术面"
            )
            
            assert isinstance(intent_result, dict), "意图分析结果格式错误"
            assert "analysis_type" in intent_result, "缺少分析类型"
            
            self.test_results.append({
                "test": test_name,
                "status": "PASS",
                "details": f"LLM响应长度: {len(response)}, 意图分析: {intent_result.get('analysis_type', 'N/A')}"
            })
            
            logger.info(f"{test_name} - 通过")
            return True
            
        except Exception as e:
            self.test_results.append({
                "test": test_name,
                "status": "FAIL",
                "error": str(e)
            })
            logger.error(f"{test_name} - 失败: {e}")
            return False
    
    async def test_agent_manager(self):
        """测试智能体管理器"""
        test_name = "智能体管理器测试"
        logger.info(f"开始 {test_name}")
        
        try:
            # 测试工具初始化
            tools = self.agent_manager.available_tools
            assert len(tools) > 0, "没有可用工具"
            
            # 测试技术分析智能体
            import pandas as pd
            import numpy as np
            
            # 创建模拟股票数据
            dates = pd.date_range('2024-01-01', periods=30, freq='D')
            mock_data = pd.DataFrame({
                'close': np.random.uniform(100, 200, 30),
                'high': np.random.uniform(110, 220, 30),
                'low': np.random.uniform(90, 190, 30),
                'volume': np.random.randint(1000000, 10000000, 30)
            }, index=dates)
            
            mock_factors = {
                'rsi': 65.5,
                'macd': 0.5,
                'ma20': 150.0
            }
            
            tech_result = await self.agent_manager.get_technical_analysis(
                "AAPL", mock_data, mock_factors
            )
            
            assert isinstance(tech_result, dict), "技术分析结果格式错误"
            assert "agent_type" in tech_result, "缺少智能体类型"
            assert tech_result["agent_type"] == "技术分析师", "智能体类型错误"
            
            self.test_results.append({
                "test": test_name,
                "status": "PASS",
                "details": f"可用工具数: {len(tools)}, 技术分析结果: {bool(tech_result.get('analysis_content'))}"
            })
            
            logger.info(f"{test_name} - 通过")
            return True
            
        except Exception as e:
            self.test_results.append({
                "test": test_name,
                "status": "FAIL",
                "error": str(e)
            })
            logger.error(f"{test_name} - 失败: {e}")
            return False
    
    async def test_workflow_manager(self):
        """测试工作流管理器"""
        test_name = "工作流管理器测试"
        logger.info(f"开始 {test_name}")
        
        try:
            # 测试简单查询处理
            test_queries = [
                "你好，请介绍一下你的功能",
                "分析苹果公司的股票表现",
                "TSLA的技术分析报告"
            ]
            
            results = []
            for query in test_queries:
                logger.info(f"测试查询: {query}")
                
                response_count = 0
                async for response in self.workflow_manager.process_user_query(query):
                    response_count += 1
                    if response_count >= 3:  # 限制响应数量以避免过长测试
                        break
                
                results.append({
                    "query": query,
                    "response_count": response_count
                })
            
            assert all(r["response_count"] > 0 for r in results), "某些查询没有响应"
            
            self.test_results.append({
                "test": test_name,
                "status": "PASS",
                "details": f"测试查询数: {len(test_queries)}, 所有查询都有响应"
            })
            
            logger.info(f"{test_name} - 通过")
            return True
            
        except Exception as e:
            self.test_results.append({
                "test": test_name,
                "status": "FAIL",
                "error": str(e)
            })
            logger.error(f"{test_name} - 失败: {e}")
            return False
    
    async def test_graph_structure(self):
        """测试图结构"""
        test_name = "图结构测试"
        logger.info(f"开始 {test_name}")
        
        try:
            # 检查图是否正确构建
            assert self.graph is not None, "图未正确构建"
            
            # 检查图的节点
            graph_dict = self.graph.get_graph().to_dict()
            nodes = list(graph_dict.get('nodes', {}).keys())
            
            expected_nodes = [
                'coordinator', 'background_investigator', 'planner', 
                'reporter', 'research_team', 'researcher', 
                'coder', 'technical_analyst', 'human_feedback'
            ]
            
            missing_nodes = [node for node in expected_nodes if node not in nodes]
            assert len(missing_nodes) == 0, f"缺少节点: {missing_nodes}"
            
            # 检查边连接
            edges = graph_dict.get('edges', [])
            assert len(edges) > 0, "图中没有边连接"
            
            self.test_results.append({
                "test": test_name,
                "status": "PASS",
                "details": f"节点数: {len(nodes)}, 边数: {len(edges)}"
            })
            
            logger.info(f"{test_name} - 通过")
            return True
            
        except Exception as e:
            self.test_results.append({
                "test": test_name,
                "status": "FAIL",
                "error": str(e)
            })
            logger.error(f"{test_name} - 失败: {e}")
            return False
    
    async def test_tools_integration(self):
        """测试工具集成"""
        test_name = "工具集成测试"
        logger.info(f"开始 {test_name}")
        
        try:
            tools = self.agent_manager.available_tools
            
            # 测试基础工具
            basic_tools = ['web_search', 'python_repl', 'crawl']
            for tool_name in basic_tools:
                if tool_name in tools:
                    logger.info(f"✓ {tool_name} 工具可用")
                else:
                    logger.warning(f"✗ {tool_name} 工具不可用")
            
            # 测试 AkShare 工具
            akshare_tools = ['stock_news', 'us_stock_spot', 'famous_stock_data']
            akshare_available = 0
            for tool_name in akshare_tools:
                if tool_name in tools:
                    akshare_available += 1
                    logger.info(f"✓ AkShare {tool_name} 工具可用")
            
            # 测试技术分析工具
            tech_tools = ['technical_indicators', 'divergence_analysis']
            tech_available = 0
            for tool_name in tech_tools:
                if tool_name in tools:
                    tech_available += 1
                    logger.info(f"✓ 技术分析 {tool_name} 工具可用")
            
            self.test_results.append({
                "test": test_name,
                "status": "PASS",
                "details": f"总工具数: {len(tools)}, AkShare工具: {akshare_available}, 技术分析工具: {tech_available}"
            })
            
            logger.info(f"{test_name} - 通过")
            return True
            
        except Exception as e:
            self.test_results.append({
                "test": test_name,
                "status": "FAIL",
                "error": str(e)
            })
            logger.error(f"{test_name} - 失败: {e}")
            return False
    
    async def test_end_to_end_workflow(self):
        """测试端到端工作流"""
        test_name = "端到端工作流测试"
        logger.info(f"开始 {test_name}")
        
        try:
            # 测试一个完整的股票分析查询
            query = "请分析苹果公司AAPL的股票，包括技术面和基本面分析"
            
            logger.info(f"执行端到端测试查询: {query}")
            
            responses = []
            response_count = 0
            max_responses = 10  # 限制最大响应数量
            
            start_time = time.time()
            
            async for response in self.workflow_manager.process_user_query(
                query, 
                max_plan_iterations=1,
                max_step_num=3,
                enable_background_investigation=False
            ):
                responses.append(response)
                response_count += 1
                
                logger.info(f"收到响应 {response_count}: {response.get('type', 'unknown')}")
                
                if response_count >= max_responses:
                    logger.info("达到最大响应数量限制，停止测试")
                    break
                
                # 如果收到最终报告，停止测试
                if response.get('type') == 'final_report' or 'final_report' in response:
                    logger.info("收到最终报告，测试完成")
                    break
            
            end_time = time.time()
            duration = end_time - start_time
            
            assert len(responses) > 0, "没有收到任何响应"
            
            # 检查是否有最终结果
            has_final_result = any(
                'final_report' in r or r.get('type') == 'final_report' 
                for r in responses
            )
            
            self.test_results.append({
                "test": test_name,
                "status": "PASS",
                "details": f"响应数: {len(responses)}, 耗时: {duration:.2f}s, 有最终结果: {has_final_result}"
            })
            
            logger.info(f"{test_name} - 通过")
            return True
            
        except Exception as e:
            self.test_results.append({
                "test": test_name,
                "status": "FAIL",
                "error": str(e)
            })
            logger.error(f"{test_name} - 失败: {e}")
            logger.error(traceback.format_exc())
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("=" * 60)
        logger.info("开始多智能体系统全面测试")
        logger.info("=" * 60)
        
        # 初始化
        if not await self.initialize():
            logger.error("初始化失败，停止测试")
            return False
        
        # 定义测试套件
        test_suite = [
            ("LLM管理器", self.test_llm_manager),
            ("智能体管理器", self.test_agent_manager),
            ("图结构", self.test_graph_structure),
            ("工具集成", self.test_tools_integration),
            ("工作流管理器", self.test_workflow_manager),
            ("端到端工作流", self.test_end_to_end_workflow),
        ]
        
        # 执行测试
        passed = 0
        failed = 0
        
        for test_name, test_func in test_suite:
            try:
                logger.info(f"\n{'='*40}")
                logger.info(f"执行测试: {test_name}")
                logger.info(f"{'='*40}")
                
                success = await test_func()
                if success:
                    passed += 1
                else:
                    failed += 1
                    
            except Exception as e:
                logger.error(f"测试 {test_name} 发生异常: {e}")
                failed += 1
        
        # 生成测试报告
        self.generate_test_report(passed, failed)
        
        return failed == 0
    
    def generate_test_report(self, passed: int, failed: int):
        """生成测试报告"""
        logger.info("\n" + "=" * 60)
        logger.info("测试报告")
        logger.info("=" * 60)
        
        logger.info(f"总测试数: {passed + failed}")
        logger.info(f"通过: {passed}")
        logger.info(f"失败: {failed}")
        logger.info(f"成功率: {(passed / (passed + failed) * 100):.1f}%")
        
        logger.info("\n详细结果:")
        for result in self.test_results:
            status_symbol = "✓" if result["status"] == "PASS" else "✗"
            logger.info(f"{status_symbol} {result['test']}: {result['status']}")
            
            if "details" in result:
                logger.info(f"  详情: {result['details']}")
            if "error" in result:
                logger.info(f"  错误: {result['error']}")
        
        # 保存 JSON 格式的报告
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": {
                "total": passed + failed,
                "passed": passed,
                "failed": failed,
                "success_rate": passed / (passed + failed) * 100 if (passed + failed) > 0 else 0
            },
            "results": self.test_results
        }
        
        with open("multi_agent_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"\n测试报告已保存到: multi_agent_test_report.json")


async def main():
    """主函数"""
    if not COMPONENTS_AVAILABLE:
        logger.error("系统组件导入失败，无法运行测试")
        return False
    
    tester = MultiAgentTester()
    success = await tester.run_all_tests()
    
    if success:
        logger.info("\n🎉 所有测试通过！")
        return True
    else:
        logger.error("\n❌ 部分测试失败，请查看详细日志")
        return False


if __name__ == "__main__":
    # 检查是否有必要的组件
    if not COMPONENTS_AVAILABLE:
        print("错误: 无法导入必要的系统组件")
        print("请确保您在正确的项目目录中运行此脚本")
        sys.exit(1)
    
    # 运行测试
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试运行时发生错误: {e}")
        traceback.print_exc()
        sys.exit(1) 