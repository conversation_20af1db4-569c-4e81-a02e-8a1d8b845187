#!/usr/bin/env python3
"""
AI模块修复验证测试脚本
测试背离检测器和新闻影响分析器的导入和功能
"""

import asyncio
import sys
import os
import traceback
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_divergence_detector_import():
    """测试背离检测器导入"""
    print("🔍 测试1: 背离检测器导入...")
    try:
        from backend.core.analysis.technical.divergence_detector import (
            get_divergence_detector, 
            get_divergence_database,
            MACDDivergenceDetector,
            DivergenceDatabase
        )
        print("✅ 背离检测器模块导入成功")
        
        # 测试实例化
        detector = get_divergence_detector()
        database = get_divergence_database()
        
        print(f"✅ 背离检测器实例化成功: {type(detector).__name__}")
        print(f"✅ 背离数据库实例化成功: {type(database).__name__}")
        
        return True
    except Exception as e:
        print(f"❌ 背离检测器导入失败: {e}")
        traceback.print_exc()
        return False

def test_news_impact_analyzer_import():
    """测试新闻影响分析器导入"""
    print("\n📊 测试2: 新闻影响分析器导入...")
    try:
        from backend.services.news.news_impact_analyzer import (
            get_news_impact_analyzer,
            NewsImpactAnalyzer
        )
        print("✅ 新闻影响分析器模块导入成功")
        
        # 测试实例化
        analyzer = get_news_impact_analyzer()
        print(f"✅ 新闻影响分析器实例化成功: {type(analyzer).__name__}")
        
        return True
    except Exception as e:
        print(f"❌ 新闻影响分析器导入失败: {e}")
        traceback.print_exc()
        return False

def test_technical_analysis_module():
    """测试技术分析模块的整体导入"""
    print("\n🔧 测试3: 技术分析模块整体导入...")
    try:
        from backend.core.analysis.technical import DivergenceDetector, MarketScanner
        print("✅ 技术分析模块from导入成功")
        
        # 测试实例化
        detector = DivergenceDetector()
        print(f"✅ DivergenceDetector实例化成功: {type(detector).__name__}")
        
        return True
    except Exception as e:
        print(f"❌ 技术分析模块导入失败: {e}")
        traceback.print_exc()
        return False

async def test_news_analyzer_functionality():
    """测试新闻分析器功能"""
    print("\n🧪 测试4: 新闻分析器功能测试...")
    try:
        from backend.services.news.news_impact_analyzer import get_news_impact_analyzer
        
        analyzer = get_news_impact_analyzer()
        
        # 模拟测试新闻数据
        test_news = {
            'title': '苹果公司发布新iPhone，预计将推动股价上涨',
            'content': '苹果公司今日发布了最新的iPhone系列产品，市场分析师预计这将对苹果股价产生积极影响。新产品具有多项创新功能，预计将刺激消费者需求。',
            'source': 'test_source',
            'publish_time': datetime.now().isoformat()
        }
        
        print("🔄 开始分析测试新闻...")
        result = await analyzer.analyze_news(test_news)
        
        if result.get('success'):
            analysis = result.get('analysis', {})
            overall_impact = analysis.get('overall_impact', {})
            print(f"✅ 新闻分析成功:")
            print(f"   影响级别: {overall_impact.get('level', '未知')}")
            print(f"   分析概述: {overall_impact.get('summary', '无')[:100]}...")
            print(f"   是否使用缓存: {result.get('cached', False)}")
            print(f"   分析ID: {result.get('analysis_id', '无')}")
            return True
        else:
            print(f"❌ 新闻分析失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 新闻分析器功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_divergence_database_functionality():
    """测试背离数据库功能"""
    print("\n💾 测试5: 背离数据库功能测试...")
    try:
        from backend.core.analysis.technical.divergence_detector import get_divergence_database
        
        db = get_divergence_database()
        
        # 测试获取最近的背离信号
        recent_divergences = db.get_recent_divergences(market='US', hours=24)
        print(f"✅ 获取最近背离信号成功，数量: {len(recent_divergences)}")
        
        # 测试数据库连接
        with db.get_connection() as conn:
            cursor = conn.execute("SELECT COUNT(*) as count FROM divergence_signals")
            total_signals = cursor.fetchone()['count']
            print(f"✅ 数据库连接正常，总背离信号数: {total_signals}")
        
        return True
    except Exception as e:
        print(f"❌ 背离数据库功能测试失败: {e}")
        traceback.print_exc()
        return False

def test_services_module_init():
    """测试服务模块的__init__.py导入"""
    print("\n📦 测试6: 服务模块导入测试...")
    try:
        from backend.services import (
            NewsImpactAnalyzer,
            get_index_stocks,
            get_all_index_names
        )
        print("✅ 服务模块导入成功")
        
        # 测试index组件功能
        index_names = get_all_index_names()
        print(f"✅ 获取指数名称成功，数量: {len(index_names)}")
        
        return True
    except Exception as e:
        print(f"❌ 服务模块导入失败: {e}")
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 AI市场影响分析功能修复验证测试")
    print("=" * 60)
    
    tests = [
        ("背离检测器导入", test_divergence_detector_import()),
        ("新闻影响分析器导入", test_news_impact_analyzer_import()),
        ("技术分析模块导入", test_technical_analysis_module()),
        ("新闻分析器功能", await test_news_analyzer_functionality()),
        ("背离数据库功能", test_divergence_database_functionality()),
        ("服务模块导入", test_services_module_init())
    ]
    
    results = []
    for test_name, result in tests:
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！AI市场影响分析功能修复成功！")
        return True
    else:
        print(f"⚠️  还有 {total - passed} 项测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试过程中发生意外错误: {e}")
        traceback.print_exc()
        sys.exit(1) 