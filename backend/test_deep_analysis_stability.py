#!/usr/bin/env python3
"""
深度分析功能稳定性测试

用于验证修复后的深度分析功能是否稳定运行
"""

import asyncio
import time
import sys
import os
import json
from datetime import datetime
from typing import Dict, Any

# 添加backend路径到sys.path
backend_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_path)

from ai.deep_research.core.deep_research_engine import DeepResearchEngine

async def test_deep_analysis_stability():
    """测试深度分析功能的稳定性"""
    print("🧪 开始深度分析功能稳定性测试")
    print("=" * 60)
    
    # 创建深度研究引擎
    engine = DeepResearchEngine()
    
    # 测试新闻数据
    test_news = {
        "id": 99999,
        "title": "比尔·盖茨创办的核电初创公司TerraPower融资6.50亿美元，英伟达参投",
        "content": "比尔·盖茨创办的核电初创公司TerraPower LLC.融资6.50亿美元，英伟达参投。该公司将在美国怀俄明州继续开发高级核反应堆项目。",
        "source": "测试",
        "publish_time": datetime.now().isoformat()
    }
    
    print(f"📋 测试新闻: {test_news['title']}")
    print()
    
    # 测试配置
    analysis_config = {
        "analysis_type": "deep",
        "max_research_loops": 1,
        "priority": "medium"
    }
    
    start_time = time.time()
    progress_count = 0
    error_count = 0
    last_message_time = start_time
    
    print("🚀 开始深度分析...")
    print("-" * 40)
    
    try:
        async for progress in engine.analyze_news_deep(test_news, analysis_config):
            progress_count += 1
            current_time = time.time()
            elapsed = current_time - start_time
            since_last = current_time - last_message_time
            last_message_time = current_time
            
            # 记录每个进度消息
            print(f"[{elapsed:6.1f}s] ({since_last:4.1f}s) {progress.get('type', 'unknown')}: {progress.get('message', '')}")
            
            # 检查特定类型的消息
            if progress.get('type') == 'rate_limit_info':
                print(f"    📡 速率限制信息: {progress.get('details', '')}")
            
            elif progress.get('type') == 'queries_generated':
                queries = progress.get('queries', [])
                print(f"    📝 生成查询数量: {len(queries)}")
                for i, query in enumerate(queries):
                    print(f"       {i+1}. {query.get('query', '')}")
            
            elif progress.get('type') == 'search_completed':
                result_preview = progress.get('result_preview', '')
                print(f"    🔍 搜索结果预览: {result_preview[:100]}...")
            
            elif progress.get('type') == 'analysis_completed':
                result = progress.get('result', {})
                final_analysis = result.get('final_analysis', {})
                sources = result.get('sources', [])
                queries_used = result.get('queries_used', [])
                
                print(f"    ✅ 分析完成!")
                print(f"       - 任务ID: {result.get('task_id', 'N/A')}")
                print(f"       - 研究查询数量: {len(queries_used)}")
                print(f"       - 来源数量: {len(sources)}")
                print(f"       - 分析文本长度: {len(final_analysis.get('analysis', ''))}")
                
                # 验证结果质量
                if len(queries_used) == 0:
                    print("    ⚠️  警告: 研究查询数量为0")
                    error_count += 1
                
                if len(sources) == 0:
                    print("    ⚠️  警告: 来源数量为0")
                    error_count += 1
                
                if len(final_analysis.get('analysis', '')) < 100:
                    print("    ⚠️  警告: 分析内容过短")
                    error_count += 1
                
                break
            
            elif progress.get('type') == 'error':
                error_count += 1
                print(f"    ❌ 错误: {progress.get('message', '')}")
                break
    
    except Exception as e:
        error_count += 1
        print(f"❌ 测试过程中发生异常: {e}")
    
    # 测试结果汇总
    total_time = time.time() - start_time
    print()
    print("=" * 60)
    print("📊 测试结果汇总")
    print(f"   - 总耗时: {total_time:.1f} 秒")
    print(f"   - 进度消息数量: {progress_count}")
    print(f"   - 错误数量: {error_count}")
    
    if error_count == 0:
        print("   ✅ 测试通过 - 深度分析功能稳定运行")
        return True
    else:
        print("   ❌ 测试失败 - 发现稳定性问题")
        return False

async def test_llm_rate_limiting():
    """测试LLM速率限制功能"""
    print("\n🔄 测试LLM速率限制功能")
    print("-" * 40)
    
    from ai.llm import LLMManager
    
    llm_manager = LLMManager()
    
    if llm_manager.mock_mode:
        print("⚠️  当前处于模拟模式，无法测试真实API速率限制")
        return True
    
    print(f"📋 配置信息:")
    print(f"   - 模型: {llm_manager.model}")
    print(f"   - 每分钟请求限制: {llm_manager.requests_per_minute}")
    print(f"   - 最小请求间隔: {llm_manager.min_request_interval}秒")
    
    # 测试连续API调用
    start_time = time.time()
    
    try:
        for i in range(3):
            call_start = time.time()
            print(f"\n🔗 API调用 {i+1}/3...")
            
            response = llm_manager.get_gemini_response(
                "请简单回答：今天是星期几？",
                temperature=0.1
            )
            
            call_end = time.time()
            call_duration = call_end - call_start
            
            print(f"   ✅ 调用完成，耗时: {call_duration:.1f}秒")
            print(f"   📝 响应长度: {len(response)}字符")
            
            if i < 2:  # 不是最后一次调用
                print(f"   ⏱️  等待下次调用...")
    
    except Exception as e:
        print(f"   ❌ API调用失败: {e}")
        return False
    
    total_time = time.time() - start_time
    print(f"\n📊 速率限制测试完成，总耗时: {total_time:.1f}秒")
    
    # 验证间隔是否符合预期（至少应该等待两次10秒间隔）
    expected_min_time = 2 * llm_manager.min_request_interval
    if total_time >= expected_min_time:
        print(f"   ✅ 速率限制工作正常 (实际: {total_time:.1f}s >= 预期: {expected_min_time:.1f}s)")
        return True
    else:
        print(f"   ⚠️  速率限制可能有问题 (实际: {total_time:.1f}s < 预期: {expected_min_time:.1f}s)")
        return False

async def main():
    """主测试函数"""
    print("🧪 深度分析功能稳定性测试套件")
    print("=" * 60)
    
    # 测试1: LLM速率限制
    rate_limit_ok = await test_llm_rate_limiting()
    
    # 测试2: 深度分析稳定性
    stability_ok = await test_deep_analysis_stability()
    
    # 总结
    print("\n" + "=" * 60)
    print("🏁 测试套件完成")
    
    if rate_limit_ok and stability_ok:
        print("✅ 所有测试通过 - 深度分析功能稳定性良好")
        return 0
    else:
        print("❌ 部分测试失败 - 需要进一步调试")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code) 