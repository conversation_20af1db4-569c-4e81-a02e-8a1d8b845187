#!/usr/bin/env python3
"""
四层思维链分析功能快速测试
用于日常验证系统功能是否正常
"""

import asyncio
import os
import sys
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def quick_test():
    """快速测试四层分析功能"""
    
    print("🚀 四层思维链分析功能快速测试")
    print("="*50)
    
    try:
        # 1. 导入测试
        print("🔍 测试模块导入...")
        from backend.ai.deep_research.core.deep_research_engine import DeepResearchEngine
        from backend.ai.deep_research.analyzers.four_layer_analyzer import FourLayerThinkingAnalyzer
        print("✅ 模块导入成功")
        
        # 2. 引擎初始化测试
        print("🔍 测试引擎初始化...")
        engine = DeepResearchEngine()
        print("✅ 引擎初始化成功")
        
        # 3. 简单功能测试
        print("🔍 执行简单功能测试...")
        
        test_news = {
            "title": "测试新闻：某科技公司股价上涨10%",
            "content": "某知名科技公司今日发布财报后股价上涨10%，超出市场预期。",
            "source": "测试源",
            "publish_time": datetime.now().isoformat(),
            "category": "科技"
        }
        
        start_time = time.time()
        step_count = 0
        
        async for result in engine.analyze_news_deep(
            news_data=test_news,
            analysis_config={
                "use_four_layer_analysis": True,
                "max_research_loops": 1,
                "initial_search_query_count": 1  # 最小配置
            }
        ):
            result_type = result.get("type", "unknown")
            step_count += 1
            
            # 只显示关键步骤
            if result_type in ["task_started", "queries_generated", "four_layer_completed", "analysis_completed"]:
                message = result.get("message", "")
                print(f"   ✅ {message}")
                
                if result_type == "analysis_completed":
                    break
            
            # 超时保护
            if time.time() - start_time > 120:  # 2分钟超时
                print("   ⏰ 测试超时，终止")
                break
        
        end_time = time.time()
        print(f"\n🎉 测试完成！")
        print(f"   耗时: {end_time - start_time:.1f}秒")
        print(f"   步骤数: {step_count}")
        print(f"   状态: ✅ 功能正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        result = asyncio.run(quick_test())
        if result:
            print(f"\n🎯 结论: 四层思维链分析功能运行正常")
            sys.exit(0)
        else:
            print(f"\n⚠️  结论: 四层思维链分析功能存在问题")
            sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 