#!/usr/bin/env python3
"""
深度分析修复验证测试
验证EventSource错误修复和分析质量改进
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_deep_analysis_with_quality_issues():
    """测试带有质量问题的新闻数据处理"""
    
    print("🔍 测试深度分析修复效果")
    print("=" * 60)
    
    try:
        from backend.ai.deep_research.core.deep_research_engine import DeepResearchEngine
        
        # 创建引擎
        engine = DeepResearchEngine()
        print("✅ 深度研究引擎创建成功")
        
        # 测试用例1：内容质量问题的新闻
        test_news_problematic = {
            "title": "短标题",  # 过短的标题
            "content": "内容过短",  # 过短的内容
            "source": "未知来源",  # 不明来源
            "publish_time": datetime.now().isoformat()
        }
        
        print("\n📰 测试用例1：质量问题新闻")
        print(f"标题: {test_news_problematic['title']}")
        print(f"内容: {test_news_problematic['content']}")
        print(f"来源: {test_news_problematic['source']}")
        
        analysis_config = {
            "use_four_layer_analysis": True,
            "max_research_loops": 1,
            "enable_detailed_status": True
        }
        
        print("\n🔄 开始分析...")
        messages = []
        
        async for progress in engine.analyze_news_deep(test_news_problematic, analysis_config):
            messages.append(progress)
            message_type = progress.get('type', 'unknown')
            message_text = progress.get('message', '')
            print(f"[{message_type}] {message_text}")
            
            # 检查是否有质量警告
            if message_type == 'content_quality_warning':
                print(f"✅ 成功检测到内容质量问题: {progress.get('details', [])}")
            
            # 限制测试时间
            if message_type == 'analysis_completed' or len(messages) > 10:
                break
        
        print(f"\n📊 测试结果: 共收到 {len(messages)} 条消息")
        
        # 检查是否正确处理了质量问题
        quality_warnings = [m for m in messages if m.get('type') == 'content_quality_warning']
        if quality_warnings:
            print("✅ 质量控制功能正常工作")
            print(f"   检测到的问题: {quality_warnings[0].get('details', [])}")
        else:
            print("⚠️  未检测到预期的质量警告")
        
        # 测试用例2：正常质量的新闻
        print("\n" + "="*60)
        print("📰 测试用例2：正常质量新闻")
        
        test_news_normal = {
            "title": "巴基斯坦军方：穆尼尔（Munir）与特朗普讨论了贸易、经济发展和加密货币",
            "content": "据报道，巴基斯坦军方高层近日表示，军方领导人穆尼尔与美国前总统特朗普就双边贸易、经济发展以及加密货币监管等议题进行了深入讨论。这一消息引发了市场对中美巴三方经贸关系的关注。",
            "source": "新浪财经全球财经快讯",
            "publish_time": "2025-06-19 12:47:31"
        }
        
        print(f"标题: {test_news_normal['title'][:50]}...")
        print(f"来源: {test_news_normal['source']}")
        
        messages_normal = []
        print("\n🔄 开始正常新闻分析...")
        
        async for progress in engine.analyze_news_deep(test_news_normal, analysis_config):
            messages_normal.append(progress)
            message_type = progress.get('type', 'unknown')
            message_text = progress.get('message', '')
            print(f"[{message_type}] {message_text}")
            
            # 限制测试时间，但允许更多消息
            if message_type == 'analysis_completed' or len(messages_normal) > 15:
                break
        
        print(f"\n📊 正常新闻测试结果: 共收到 {len(messages_normal)} 条消息")
        
        # 检查正常新闻是否有质量警告
        quality_warnings_normal = [m for m in messages_normal if m.get('type') == 'content_quality_warning']
        if not quality_warnings_normal:
            print("✅ 正常新闻未触发质量警告，功能正常")
        else:
            print("⚠️  正常新闻意外触发了质量警告")
        
        print("\n" + "="*60)
        print("🎉 深度分析修复验证完成")
        print("\n主要改进:")
        print("1. ✅ 增强了EventSource错误处理和日志输出")
        print("2. ✅ 添加了新闻内容质量验证")
        print("3. ✅ 改进了四层分析的可信度检查")
        print("4. ✅ 优化了默认分析报告的生成")
        print("5. ✅ 增加了详细的风险提示和免责声明")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_deep_analysis_with_quality_issues()) 