# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import sqlite3
import json
import logging
import hashlib
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from contextlib import contextmanager

from backend.ai.llms.glm_client import get_glm_client
from backend.ai.llm import LLMManager

logger = logging.getLogger(__name__)

class NewsImpactScorer:
    """新闻影响评分器"""

    @staticmethod
    def calculate_impact_score(analysis: Dict[str, Any]) -> float:
        """
        根据AI分析结果计算综合影响评分 (0-100)

        Args:
            analysis: AI分析结果

        Returns:
            影响评分 (0-100)
        """
        try:
            # 基础权重配置
            weights = {
                'overall_impact': 0.4,
                'market_breadth': 0.3,
                'time_sensitivity': 0.2,
                'confidence': 0.1
            }

            score = 0.0

            # 1. 整体影响评分
            overall_impact = analysis.get('overall_impact', {})
            impact_level = overall_impact.get('level', '中').lower()
            impact_scores = {'低': 20, '中': 50, '高': 80, '极高': 100}
            score += weights['overall_impact'] * impact_scores.get(impact_level, 50)

            # 2. 市场广度评分（影响的市场数量）
            markets = ['us_market', 'a_share_market', 'hk_market']
            affected_markets = 0
            total_market_impact = 0

            for market in markets:
                market_data = analysis.get(market, {})
                if market_data.get('impact_level', '中').lower() in ['高', '极高']:
                    affected_markets += 1
                    total_market_impact += impact_scores.get(market_data.get('impact_level', '中').lower(), 50)

            market_breadth_score = (affected_markets / len(markets)) * 100
            score += weights['market_breadth'] * market_breadth_score

            # 3. 时间敏感性评分
            time_horizon = overall_impact.get('time_horizon', '中期').lower()
            time_scores = {'短期': 90, '中期': 60, '长期': 30}
            score += weights['time_sensitivity'] * time_scores.get(time_horizon, 60)

            # 4. 分析置信度评分
            confidence = overall_impact.get('confidence', 0.7)
            score += weights['confidence'] * (confidence * 100)

            return min(100, max(0, score))

        except Exception as e:
            logger.error(f"计算影响评分失败: {e}")
            return 50.0  # 默认中等评分

    @staticmethod
    def generate_impact_tags(analysis: Dict[str, Any]) -> List[str]:
        """
        根据分析结果生成影响标签

        Args:
            analysis: AI分析结果

        Returns:
            影响标签列表
        """
        tags = []

        try:
            # 整体影响标签
            overall_impact = analysis.get('overall_impact', {})
            impact_level = overall_impact.get('level', '中').lower()

            if impact_level in ['高', '极高']:
                tags.append('高影响')
            elif impact_level == '低':
                tags.append('低影响')
            else:
                tags.append('中等影响')

            # 时间敏感性标签
            time_horizon = overall_impact.get('time_horizon', '中期').lower()
            if time_horizon == '短期':
                tags.append('短期影响')
            elif time_horizon == '长期':
                tags.append('长期影响')

            # 市场特定标签
            markets = {
                'us_market': '美股',
                'a_share_market': 'A股',
                'hk_market': '港股'
            }

            for market_key, market_name in markets.items():
                market_data = analysis.get(market_key, {})
                impact_level = market_data.get('impact_level', '中').lower()
                if impact_level in ['高', '极高']:
                    tags.append(f'{market_name}重点关注')

            # 风险相关标签
            risk_assessment = analysis.get('risk_assessment', {})
            if risk_assessment:
                short_term_risk = risk_assessment.get('short_term_risk', '中').lower()
                if short_term_risk in ['高', '极高']:
                    tags.append('高风险')

                medium_term_risk = risk_assessment.get('medium_term_risk', '中').lower()
                if medium_term_risk in ['高', '极高']:
                    tags.append('中期风险')

            # 投资建议标签
            investment_advice = analysis.get('investment_advice', {})
            if investment_advice:
                strategy = investment_advice.get('strategy', '').lower()
                if '买入' in strategy or 'buy' in strategy:
                    tags.append('买入机会')
                elif '卖出' in strategy or 'sell' in strategy:
                    tags.append('卖出信号')
                elif '观望' in strategy or 'hold' in strategy:
                    tags.append('观望等待')

            # 去重并限制标签数量
            tags = list(set(tags))[:8]  # 最多8个标签

        except Exception as e:
            logger.error(f"生成影响标签失败: {e}")
            tags = ['分析异常']

        return tags

class NewsImpactAnalyzer:
    """新闻影响分析管理器"""
    
    def __init__(self, db_path: str = "data/news_impact_analysis.db"):
        self.db_path = db_path
        self.glm_client = get_glm_client()
        self.llm_manager = LLMManager()
        self.init_database()
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_connection() as conn:
                # 创建新闻影响分析表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS news_impact_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        news_hash VARCHAR(64) UNIQUE NOT NULL,
                        news_title TEXT NOT NULL,
                        news_content TEXT,
                        news_source VARCHAR(100),
                        news_publish_time DATETIME,
                        analysis_result TEXT NOT NULL,
                        overall_impact_level VARCHAR(10),
                        us_market_impact VARCHAR(10),
                        a_share_impact VARCHAR(10),
                        hk_market_impact VARCHAR(10),
                        impact_score REAL DEFAULT 50.0,
                        impact_tags TEXT DEFAULT '[]',
                        analysis_model VARCHAR(20) DEFAULT 'glm',
                        analysis_status VARCHAR(20) DEFAULT 'completed',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建分析缓存表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS analysis_cache (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        cache_key VARCHAR(64) UNIQUE NOT NULL,
                        cache_data TEXT NOT NULL,
                        expires_at DATETIME NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                conn.execute("CREATE INDEX IF NOT EXISTS idx_news_impact_hash ON news_impact_analysis(news_hash)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_news_impact_created ON news_impact_analysis(created_at DESC)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_analysis_cache_key ON analysis_cache(cache_key)")
                conn.execute("CREATE INDEX IF NOT EXISTS idx_analysis_cache_expires ON analysis_cache(expires_at)")
                
                conn.commit()
                logger.info("新闻影响分析数据库初始化完成")
                
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _generate_news_hash(self, title: str, content: str) -> str:
        """生成新闻内容的哈希值"""
        content_str = f"{title}|{content}"
        return hashlib.sha256(content_str.encode('utf-8')).hexdigest()
    
    async def analyze_news(self, news_data: Dict[str, Any], model: str = 'glm') -> Dict[str, Any]:
        """
        分析单条新闻的市场影响
        
        Args:
            news_data: 新闻数据，包含title, content, source等字段
            model: 分析模型选择，'glm' 或 'gemini'
            
        Returns:
            分析结果
        """
        try:
            title = news_data.get('title', '')
            content = news_data.get('content', '')
            source = news_data.get('source', '')
            publish_time = news_data.get('publish_time', '')
            
            if not title or not content:
                return {
                    'success': False,
                    'error': '新闻标题或内容为空'
                }
            
            # 生成新闻哈希
            news_hash = self._generate_news_hash(title, content)
            
            # 检查是否已经分析过
            existing_analysis = self._get_existing_analysis(news_hash)
            if existing_analysis:
                logger.info(f"使用已有分析结果: {news_hash}")
                return {
                    'success': True,
                    'analysis': json.loads(existing_analysis['analysis_result']),
                    'cached': True,
                    'analysis_id': existing_analysis['id']
                }
            
            # 根据选择的模型进行分析
            logger.info(f"开始使用{model}模型分析新闻: {title[:50]}...")

            if model == 'gemini' and self.llm_manager.is_available():
                analysis_result = await self.llm_manager.analyze_news_impact_with_gemini(title, content)
            else:
                # 默认使用GLM或者Gemini不可用时回退到GLM
                analysis_result = await self.glm_client.analyze_news_impact(title, content)

            if not analysis_result.get('success'):
                return analysis_result

            # 计算影响评分和生成标签
            analysis_data = analysis_result['analysis']
            impact_score = NewsImpactScorer.calculate_impact_score(analysis_data)
            impact_tags = NewsImpactScorer.generate_impact_tags(analysis_data)

            # 将评分和标签添加到分析结果中
            analysis_data['impact_score'] = impact_score
            analysis_data['impact_tags'] = impact_tags

            # 保存分析结果
            analysis_id = self._save_analysis_result(
                news_hash, title, content, source, publish_time,
                analysis_data, impact_score, impact_tags, model
            )

            return {
                'success': True,
                'analysis': analysis_data,
                'cached': False,
                'analysis_id': analysis_id,
                'impact_score': impact_score,
                'impact_tags': impact_tags
            }
            
        except Exception as e:
            logger.error(f"新闻分析失败: {e}")
            return {
                'success': False,
                'error': f'分析过程中发生错误: {str(e)}'
            }
    
    def _get_existing_analysis(self, news_hash: str) -> Optional[Dict[str, Any]]:
        """获取已有的分析结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM news_impact_analysis WHERE news_hash = ? AND analysis_status = 'completed'",
                    (news_hash,)
                )
                row = cursor.fetchone()
                return dict(row) if row else None
        except Exception as e:
            logger.error(f"查询已有分析结果失败: {e}")
            return None
    
    def _save_analysis_result(self, news_hash: str, title: str, content: str,
                            source: str, publish_time: str, analysis: Dict[str, Any],
                            impact_score: float = 50.0, impact_tags: List[str] = None,
                            model: str = 'glm') -> int:
        """保存分析结果到数据库"""
        try:
            with self.get_connection() as conn:
                # 提取关键指标
                overall_impact = analysis.get('overall_impact', {}).get('level', '中')
                us_impact = analysis.get('us_market', {}).get('impact_level', '中')
                a_share_impact = analysis.get('a_share_market', {}).get('impact_level', '中')
                hk_impact = analysis.get('hk_market', {}).get('impact_level', '中')

                # 处理标签
                if impact_tags is None:
                    impact_tags = []
                tags_json = json.dumps(impact_tags, ensure_ascii=False)

                cursor = conn.execute("""
                    INSERT OR REPLACE INTO news_impact_analysis
                    (news_hash, news_title, news_content, news_source, news_publish_time,
                     analysis_result, overall_impact_level, us_market_impact,
                     a_share_impact, hk_market_impact, impact_score, impact_tags,
                     analysis_model, analysis_status, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'completed', CURRENT_TIMESTAMP)
                """, (
                    news_hash, title, content, source, publish_time,
                    json.dumps(analysis, ensure_ascii=False),
                    overall_impact, us_impact, a_share_impact, hk_impact,
                    impact_score, tags_json, model
                ))

                conn.commit()
                return cursor.lastrowid

        except Exception as e:
            logger.error(f"保存分析结果失败: {e}")
            raise
    
    def get_analysis_by_id(self, analysis_id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取分析结果"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM news_impact_analysis WHERE id = ?",
                    (analysis_id,)
                )
                row = cursor.fetchone()
                if row:
                    result = dict(row)
                    result['analysis_result'] = json.loads(result['analysis_result'])
                    return result
                return None
        except Exception as e:
            logger.error(f"获取分析结果失败: {e}")
            return None
    
    def get_recent_analyses(self, limit: int = 20, impact_level: Optional[str] = None,
                          min_score: Optional[float] = None) -> List[Dict[str, Any]]:
        """获取最近的分析结果（包含评分和标签）"""
        try:
            with self.get_connection() as conn:
                query = """
                    SELECT id, news_title, news_source, news_publish_time,
                           overall_impact_level, us_market_impact, a_share_impact, hk_market_impact,
                           impact_score, impact_tags, analysis_model, created_at
                    FROM news_impact_analysis
                    WHERE analysis_status = 'completed'
                """
                params = []

                if impact_level:
                    query += " AND overall_impact_level = ?"
                    params.append(impact_level)

                if min_score is not None:
                    query += " AND impact_score >= ?"
                    params.append(min_score)

                query += " ORDER BY impact_score DESC, created_at DESC LIMIT ?"
                params.append(limit)

                cursor = conn.execute(query, params)
                rows = cursor.fetchall()

                results = []
                for row in rows:
                    result = dict(row)
                    # 解析标签JSON
                    try:
                        result['impact_tags'] = json.loads(result['impact_tags']) if result['impact_tags'] else []
                    except:
                        result['impact_tags'] = []
                    results.append(result)

                return results

        except Exception as e:
            logger.error(f"获取最近分析结果失败: {e}")
            return []

    def get_high_impact_news(self, hours_back: int = 24, min_score: float = 70.0) -> List[Dict[str, Any]]:
        """获取高影响力新闻"""
        try:
            with self.get_connection() as conn:
                cutoff_time = (datetime.now() - timedelta(hours=hours_back)).isoformat()

                cursor = conn.execute("""
                    SELECT id, news_title, news_source, news_publish_time,
                           overall_impact_level, impact_score, impact_tags, analysis_model,
                           created_at
                    FROM news_impact_analysis
                    WHERE analysis_status = 'completed'
                    AND impact_score >= ?
                    AND created_at > ?
                    ORDER BY impact_score DESC, created_at DESC
                """, (min_score, cutoff_time))

                rows = cursor.fetchall()

                results = []
                for row in rows:
                    result = dict(row)
                    try:
                        result['impact_tags'] = json.loads(result['impact_tags']) if result['impact_tags'] else []
                    except:
                        result['impact_tags'] = []
                    results.append(result)

                return results

        except Exception as e:
            logger.error(f"获取高影响力新闻失败: {e}")
            return []
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        try:
            with self.get_connection() as conn:
                # 总分析数量
                cursor = conn.execute("SELECT COUNT(*) as total FROM news_impact_analysis WHERE analysis_status = 'completed'")
                total = cursor.fetchone()['total']
                
                # 按影响级别统计
                cursor = conn.execute("""
                    SELECT overall_impact_level, COUNT(*) as count 
                    FROM news_impact_analysis 
                    WHERE analysis_status = 'completed'
                    GROUP BY overall_impact_level
                """)
                impact_stats = {row['overall_impact_level']: row['count'] for row in cursor.fetchall()}
                
                # 今日分析数量
                cursor = conn.execute("""
                    SELECT COUNT(*) as today_count 
                    FROM news_impact_analysis 
                    WHERE analysis_status = 'completed' 
                    AND DATE(created_at) = DATE('now')
                """)
                today_count = cursor.fetchone()['today_count']
                
                return {
                    'total_analyses': total,
                    'today_analyses': today_count,
                    'impact_distribution': impact_stats,
                    'last_updated': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取分析统计失败: {e}")
            return {
                'total_analyses': 0,
                'today_analyses': 0,
                'impact_distribution': {},
                'error': str(e)
            }
    
    async def batch_analyze_news(self, news_list: List[Dict[str, Any]], 
                               max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """
        批量分析新闻
        
        Args:
            news_list: 新闻列表
            max_concurrent: 最大并发数
            
        Returns:
            分析结果列表
        """
        import asyncio
        
        results = []
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def analyze_single_news(news_data):
            async with semaphore:
                return await self.analyze_news(news_data)
        
        # 创建任务列表
        tasks = [analyze_single_news(news) for news in news_list]
        
        # 并发执行
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'success': False,
                    'error': f'分析异常: {str(result)}',
                    'news_index': i
                })
            else:
                processed_results.append(result)
        
        return processed_results

    async def auto_analyze_latest_important_news(self, hours_back: int = 2,
                                               max_news: int = 10,
                                               model: str = 'glm') -> Dict[str, Any]:
        """
        自动分析最新的重要新闻

        Args:
            hours_back: 回溯小时数
            max_news: 最大分析新闻数量
            model: 使用的AI模型

        Returns:
            分析结果统计
        """
        try:
            from backend.core.data.managers.financial_news_manager import get_financial_news_manager

            news_manager = get_financial_news_manager()

            # 获取最新新闻
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=hours_back)

            latest_news = news_manager.get_news_by_timerange(
                start_time=start_time,
                end_time=end_time,
                limit=max_news * 2  # 获取更多新闻以便筛选
            )

            if not latest_news:
                return {
                    'success': True,
                    'message': '没有找到最新新闻',
                    'analyzed_count': 0,
                    'high_impact_count': 0
                }

            # 筛选重要新闻（基于标题关键词）
            important_keywords = [
                '央行', '利率', 'GDP', '通胀', '美联储', '加息', '降息',
                '财报', '业绩', '重组', '并购', 'IPO', '退市',
                '政策', '监管', '制裁', '贸易', '关税',
                '突发', '重大', '紧急', '暴跌', '暴涨'
            ]

            important_news = []
            for news in latest_news:
                title = news.get('title', '').lower()
                if any(keyword in title for keyword in important_keywords):
                    important_news.append(news)
                    if len(important_news) >= max_news:
                        break

            if not important_news:
                # 如果没有找到重要新闻，取最新的几条
                important_news = latest_news[:max_news]

            # 批量分析
            analysis_results = await self.batch_analyze_news(important_news, max_concurrent=3)

            # 统计结果
            analyzed_count = 0
            high_impact_count = 0

            for result in analysis_results:
                if result.get('success'):
                    analyzed_count += 1
                    impact_score = result.get('impact_score', 0)
                    if impact_score >= 70:
                        high_impact_count += 1

            return {
                'success': True,
                'analyzed_count': analyzed_count,
                'high_impact_count': high_impact_count,
                'total_news_found': len(latest_news),
                'important_news_selected': len(important_news),
                'analysis_results': analysis_results,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"自动分析最新重要新闻失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'analyzed_count': 0,
                'high_impact_count': 0
            }
    
    def cleanup_old_analyses(self, days: int = 30) -> int:
        """清理旧的分析记录"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    DELETE FROM news_impact_analysis 
                    WHERE created_at < datetime('now', '-{} days')
                """.format(days))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"清理了 {deleted_count} 条旧分析记录")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理旧分析记录失败: {e}")
            return 0

# 全局分析器实例
_news_impact_analyzer = None

def get_news_impact_analyzer() -> NewsImpactAnalyzer:
    """获取新闻影响分析器实例"""
    global _news_impact_analyzer
    if _news_impact_analyzer is None:
        _news_impact_analyzer = NewsImpactAnalyzer()
    return _news_impact_analyzer 