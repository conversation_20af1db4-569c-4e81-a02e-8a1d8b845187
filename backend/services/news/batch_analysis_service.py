#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量新闻分析服务
提供高效的批量AI分析功能，支持并发处理和进度跟踪
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import uuid

from backend.services.news.news_impact_analyzer import get_news_impact_analyzer
from backend.core.cache.smart_cache_manager import get_smart_cache_manager

logger = logging.getLogger(__name__)

@dataclass
class BatchAnalysisTask:
    """批量分析任务"""
    task_id: str
    news_items: List[Dict[str, Any]]
    model: str
    status: str = 'pending'  # pending, running, completed, failed
    progress: float = 0.0
    total_items: int = 0
    completed_items: int = 0
    failed_items: int = 0
    results: List[Dict[str, Any]] = None
    error_message: Optional[str] = None
    created_at: datetime = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    def __post_init__(self):
        if self.results is None:
            self.results = []
        if self.created_at is None:
            self.created_at = datetime.now()
        self.total_items = len(self.news_items)

class BatchAnalysisService:
    """批量新闻分析服务"""
    
    def __init__(self, max_concurrent: int = 3, max_batch_size: int = 50):
        """
        初始化批量分析服务
        
        Args:
            max_concurrent: 最大并发数
            max_batch_size: 最大批量大小
        """
        self.max_concurrent = max_concurrent
        self.max_batch_size = max_batch_size
        self.analyzer = get_news_impact_analyzer()
        self.cache_manager = get_smart_cache_manager()
        
        # 任务管理
        self.active_tasks: Dict[str, BatchAnalysisTask] = {}
        self.task_history: List[BatchAnalysisTask] = []
        self.max_history_size = 100
        
        # 性能统计
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'total_news_analyzed': 0,
            'average_analysis_time': 0.0,
            'cache_hit_rate': 0.0
        }
    
    async def create_batch_analysis_task(self, 
                                       news_items: List[Dict[str, Any]], 
                                       model: str = 'glm',
                                       priority: str = 'normal') -> str:
        """
        创建批量分析任务
        
        Args:
            news_items: 新闻数据列表
            model: AI模型选择
            priority: 任务优先级
            
        Returns:
            任务ID
        """
        # 验证输入
        if not news_items:
            raise ValueError("新闻列表不能为空")
        
        if len(news_items) > self.max_batch_size:
            raise ValueError(f"批量大小超过限制 ({self.max_batch_size})")
        
        # 创建任务
        task_id = str(uuid.uuid4())
        task = BatchAnalysisTask(
            task_id=task_id,
            news_items=news_items.copy(),
            model=model
        )
        
        self.active_tasks[task_id] = task
        self.stats['total_tasks'] += 1
        
        logger.info(f"创建批量分析任务: {task_id}, 新闻数量: {len(news_items)}, 模型: {model}")
        
        return task_id
    
    async def start_batch_analysis(self, task_id: str, 
                                 progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        开始执行批量分析任务
        
        Args:
            task_id: 任务ID
            progress_callback: 进度回调函数
            
        Returns:
            分析结果
        """
        if task_id not in self.active_tasks:
            raise ValueError(f"任务不存在: {task_id}")
        
        task = self.active_tasks[task_id]
        
        if task.status != 'pending':
            raise ValueError(f"任务状态无效: {task.status}")
        
        task.status = 'running'
        task.started_at = datetime.now()
        
        try:
            logger.info(f"开始执行批量分析任务: {task_id}")
            
            # 执行批量分析
            results = await self._execute_batch_analysis(task, progress_callback)
            
            # 更新任务状态
            task.status = 'completed'
            task.completed_at = datetime.now()
            task.results = results
            task.progress = 100.0
            
            # 更新统计信息
            self.stats['completed_tasks'] += 1
            self.stats['total_news_analyzed'] += task.completed_items
            
            # 计算平均分析时间
            if task.started_at and task.completed_at:
                analysis_time = (task.completed_at - task.started_at).total_seconds()
                if task.completed_items > 0:
                    avg_time = analysis_time / task.completed_items
                    self._update_average_analysis_time(avg_time)
            
            logger.info(f"批量分析任务完成: {task_id}, 成功: {task.completed_items}, 失败: {task.failed_items}")
            
            return {
                'task_id': task_id,
                'status': 'completed',
                'total_items': task.total_items,
                'completed_items': task.completed_items,
                'failed_items': task.failed_items,
                'results': results,
                'analysis_time_seconds': analysis_time if task.started_at and task.completed_at else 0
            }
            
        except Exception as e:
            # 处理任务失败
            task.status = 'failed'
            task.error_message = str(e)
            task.completed_at = datetime.now()
            
            self.stats['failed_tasks'] += 1
            
            logger.error(f"批量分析任务失败: {task_id}, 错误: {e}")
            
            return {
                'task_id': task_id,
                'status': 'failed',
                'error': str(e),
                'completed_items': task.completed_items,
                'failed_items': task.failed_items
            }
        
        finally:
            # 移动任务到历史记录
            self._move_task_to_history(task_id)
    
    async def _execute_batch_analysis(self, task: BatchAnalysisTask, 
                                    progress_callback: Optional[Callable] = None) -> List[Dict[str, Any]]:
        """执行批量分析"""
        results = []
        semaphore = asyncio.Semaphore(self.max_concurrent)
        
        async def analyze_single_news(news_item: Dict[str, Any], index: int) -> Dict[str, Any]:
            """分析单条新闻"""
            async with semaphore:
                try:
                    # 检查缓存
                    cache_key_data = {
                        'title': news_item.get('title', ''),
                        'content': news_item.get('content', ''),
                        'model': task.model
                    }
                    
                    cached_result, cache_hit = self.cache_manager.get(
                        'news_analysis', cache_key_data
                    )
                    
                    if cache_hit:
                        result = {
                            'news_id': news_item.get('id'),
                            'news_title': news_item.get('title', ''),
                            'success': True,
                            'cached': True,
                            'analysis': cached_result,
                            'index': index
                        }
                    else:
                        # 执行AI分析
                        analysis_result = await self.analyzer.analyze_news(
                            news_item, model=task.model
                        )
                        
                        if analysis_result.get('success'):
                            # 缓存结果
                            self.cache_manager.set(
                                'news_analysis', cache_key_data, 
                                analysis_result.get('analysis'), 
                                ttl_seconds=3600  # 1小时缓存
                            )
                            
                            result = {
                                'news_id': news_item.get('id'),
                                'news_title': news_item.get('title', ''),
                                'success': True,
                                'cached': False,
                                'analysis': analysis_result.get('analysis'),
                                'impact_score': analysis_result.get('impact_score', 0),
                                'impact_tags': analysis_result.get('impact_tags', []),
                                'index': index
                            }
                            
                            task.completed_items += 1
                        else:
                            result = {
                                'news_id': news_item.get('id'),
                                'news_title': news_item.get('title', ''),
                                'success': False,
                                'error': analysis_result.get('error', '分析失败'),
                                'index': index
                            }
                            task.failed_items += 1
                    
                    # 更新进度
                    task.progress = ((task.completed_items + task.failed_items) / task.total_items) * 100
                    
                    # 调用进度回调
                    if progress_callback:
                        try:
                            await progress_callback(task.progress, task.completed_items, task.failed_items)
                        except Exception as e:
                            logger.warning(f"进度回调失败: {e}")
                    
                    return result
                    
                except Exception as e:
                    task.failed_items += 1
                    task.progress = ((task.completed_items + task.failed_items) / task.total_items) * 100
                    
                    logger.error(f"分析新闻失败: {e}")
                    return {
                        'news_id': news_item.get('id'),
                        'news_title': news_item.get('title', ''),
                        'success': False,
                        'error': str(e),
                        'index': index
                    }
        
        # 创建分析任务
        analysis_tasks = [
            analyze_single_news(news_item, index)
            for index, news_item in enumerate(task.news_items)
        ]
        
        # 并发执行分析
        results = await asyncio.gather(*analysis_tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                task.failed_items += 1
                processed_results.append({
                    'success': False,
                    'error': str(result)
                })
            else:
                processed_results.append(result)
        
        # 按原始顺序排序
        processed_results.sort(key=lambda x: x.get('index', 0))
        
        return processed_results
    
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task = self.active_tasks.get(task_id)
        if not task:
            # 检查历史记录
            for hist_task in self.task_history:
                if hist_task.task_id == task_id:
                    task = hist_task
                    break
        
        if not task:
            return None
        
        return {
            'task_id': task.task_id,
            'status': task.status,
            'progress': task.progress,
            'total_items': task.total_items,
            'completed_items': task.completed_items,
            'failed_items': task.failed_items,
            'model': task.model,
            'created_at': task.created_at.isoformat(),
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None,
            'error_message': task.error_message
        }
    
    def _move_task_to_history(self, task_id: str):
        """将任务移动到历史记录"""
        if task_id in self.active_tasks:
            task = self.active_tasks.pop(task_id)
            self.task_history.append(task)
            
            # 限制历史记录大小
            if len(self.task_history) > self.max_history_size:
                self.task_history.pop(0)
    
    def _update_average_analysis_time(self, new_time: float):
        """更新平均分析时间"""
        if self.stats['average_analysis_time'] == 0:
            self.stats['average_analysis_time'] = new_time
        else:
            # 使用指数移动平均
            alpha = 0.1
            self.stats['average_analysis_time'] = (
                alpha * new_time + (1 - alpha) * self.stats['average_analysis_time']
            )
    
    def get_service_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        cache_stats = self.cache_manager.get_stats()
        
        return {
            'batch_analysis': self.stats.copy(),
            'active_tasks': len(self.active_tasks),
            'cache_performance': cache_stats.get('performance', {}),
            'system_config': {
                'max_concurrent': self.max_concurrent,
                'max_batch_size': self.max_batch_size
            }
        }


# 全局实例
_batch_service = None

def get_batch_analysis_service() -> BatchAnalysisService:
    """获取批量分析服务实例（单例模式）"""
    global _batch_service
    if _batch_service is None:
        _batch_service = BatchAnalysisService()
    return _batch_service
