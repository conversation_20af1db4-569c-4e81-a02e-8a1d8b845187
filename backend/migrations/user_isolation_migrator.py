#!/usr/bin/env python3
"""
用户隔离机制数据迁移工具
实现零停机迁移，为现有数据添加用户隔离支持
"""

import sqlite3
import logging
import os
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class UserIsolationMigrator:
    """用户隔离机制迁移器"""
    
    def __init__(self, db_path: str = "/Users/<USER>/Code/cash-flow/backend/data/financial_data.db"):
        self.db_path = db_path
        self.migration_version = "1.0.0"
        self.system_user_id = -1
        
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def check_migration_status(self) -> Dict[str, Any]:
        """检查迁移状态"""
        try:
            with self.get_connection() as conn:
                # 检查迁移日志表是否存在
                cursor = conn.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='data_migration_log'
                """)
                
                if not cursor.fetchone():
                    return {"status": "not_started", "message": "迁移日志表不存在"}
                
                # 检查用户隔离迁移状态
                cursor = conn.execute("""
                    SELECT * FROM data_migration_log 
                    WHERE migration_name = 'add_user_isolation'
                    ORDER BY created_at DESC LIMIT 1
                """)
                
                migration_record = cursor.fetchone()
                if not migration_record:
                    return {"status": "not_started", "message": "用户隔离迁移未开始"}
                
                return {
                    "status": migration_record["status"],
                    "version": migration_record["migration_version"],
                    "start_time": migration_record["start_time"],
                    "end_time": migration_record["end_time"],
                    "affected_rows": migration_record["affected_rows"],
                    "error_message": migration_record["error_message"]
                }
                
        except Exception as e:
            logger.error(f"检查迁移状态失败: {e}")
            return {"status": "error", "message": str(e)}
    
    def create_system_user(self) -> bool:
        """创建系统用户"""
        try:
            with self.get_connection() as conn:
                # 检查系统用户是否已存在
                cursor = conn.execute("SELECT id FROM users WHERE id = ?", (self.system_user_id,))
                if cursor.fetchone():
                    logger.info("系统用户已存在")
                    return True
                
                # 创建系统用户
                conn.execute("""
                    INSERT INTO users (id, username, email, password_hash, full_name, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    self.system_user_id,
                    'system',
                    'system@localhost',
                    'system_hash_placeholder',
                    'System User',
                    1,
                    datetime.now()
                ))
                
                conn.commit()
                logger.info("系统用户创建成功")
                return True
                
        except Exception as e:
            logger.error(f"创建系统用户失败: {e}")
            return False
    
    def backup_database(self) -> str:
        """备份数据库"""
        try:
            backup_path = f"{self.db_path}.backup_{int(time.time())}"
            
            with sqlite3.connect(self.db_path) as source:
                with sqlite3.connect(backup_path) as backup:
                    source.backup(backup)
            
            logger.info(f"数据库备份完成: {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            raise
    
    def execute_migration_sql(self) -> bool:
        """执行迁移SQL脚本"""
        try:
            migration_sql_path = os.path.join(
                os.path.dirname(__file__), 
                "..", 
                "..",
                "migrations", 
                "add_user_isolation.sql"
            )
            
            if not os.path.exists(migration_sql_path):
                logger.error(f"迁移SQL文件不存在: {migration_sql_path}")
                return False
            
            with open(migration_sql_path, 'r', encoding='utf-8') as f:
                migration_sql = f.read()
            
            with self.get_connection() as conn:
                # 分割SQL语句并逐个执行
                statements = [stmt.strip() for stmt in migration_sql.split(';') if stmt.strip()]
                
                for statement in statements:
                    if statement.startswith('--') or not statement:
                        continue
                    
                    try:
                        conn.execute(statement)
                        logger.debug(f"执行SQL语句成功: {statement[:50]}...")
                    except sqlite3.Error as e:
                        # 某些语句可能因为表已存在等原因失败，这是正常的
                        logger.warning(f"SQL语句执行警告: {e}, 语句: {statement[:50]}...")
                
                conn.commit()
                logger.info("迁移SQL脚本执行完成")
                return True
                
        except Exception as e:
            logger.error(f"执行迁移SQL脚本失败: {e}")
            return False
    
    def verify_migration(self) -> Dict[str, Any]:
        """验证迁移结果"""
        verification_results = {}
        
        try:
            with self.get_connection() as conn:
                # 检查表结构是否正确添加了user_id字段
                tables_to_check = [
                    'stock_info', 'cn_daily_data', 'hk_daily_data', 
                    'us_daily_data', 'factor_data'
                ]
                
                for table in tables_to_check:
                    cursor = conn.execute(f"PRAGMA table_info({table})")
                    columns = [row[1] for row in cursor.fetchall()]
                    verification_results[f"{table}_has_user_id"] = 'user_id' in columns
                
                # 检查新表是否创建成功
                new_tables = [
                    'user_watchlist', 'user_settings', 'user_ai_analysis_history',
                    'data_migration_log'
                ]
                
                for table in new_tables:
                    cursor = conn.execute("""
                        SELECT name FROM sqlite_master 
                        WHERE type='table' AND name=?
                    """, (table,))
                    verification_results[f"{table}_exists"] = cursor.fetchone() is not None
                
                # 检查索引是否创建成功
                cursor = conn.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='index' AND name LIKE 'idx_%'
                """)
                indexes = [row[0] for row in cursor.fetchall()]
                verification_results["indexes_created"] = len(indexes) > 0
                verification_results["index_count"] = len(indexes)
                
                # 检查系统用户是否存在
                cursor = conn.execute("SELECT id FROM users WHERE id = ?", (self.system_user_id,))
                verification_results["system_user_exists"] = cursor.fetchone() is not None
                
                logger.info("迁移验证完成")
                return verification_results
                
        except Exception as e:
            logger.error(f"迁移验证失败: {e}")
            verification_results["error"] = str(e)
            return verification_results
    
    def log_migration_status(self, status: str, error_message: str = None, 
                           affected_rows: int = 0) -> None:
        """记录迁移状态"""
        try:
            with self.get_connection() as conn:
                if status == "running":
                    conn.execute("""
                        INSERT INTO data_migration_log 
                        (migration_name, migration_version, status, start_time, affected_rows)
                        VALUES (?, ?, ?, ?, ?)
                    """, (
                        'add_user_isolation',
                        self.migration_version,
                        status,
                        datetime.now(),
                        affected_rows
                    ))
                else:
                    conn.execute("""
                        UPDATE data_migration_log 
                        SET status = ?, end_time = ?, error_message = ?, affected_rows = ?
                        WHERE migration_name = 'add_user_isolation' 
                        AND migration_version = ?
                        AND status = 'running'
                    """, (
                        status,
                        datetime.now(),
                        error_message,
                        affected_rows,
                        self.migration_version
                    ))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"记录迁移状态失败: {e}")
    
    def run_migration(self, create_backup: bool = True) -> Dict[str, Any]:
        """运行完整的迁移流程"""
        migration_result = {
            "success": False,
            "backup_path": None,
            "verification_results": {},
            "error_message": None
        }
        
        try:
            logger.info("开始用户隔离机制数据迁移")
            
            # 检查当前迁移状态
            current_status = self.check_migration_status()
            if current_status["status"] == "completed":
                logger.info("迁移已完成，跳过")
                migration_result["success"] = True
                migration_result["verification_results"] = self.verify_migration()
                return migration_result
            
            # 创建数据库备份
            if create_backup:
                migration_result["backup_path"] = self.backup_database()
            
            # 记录迁移开始
            self.log_migration_status("running")
            
            # 执行迁移SQL脚本
            if not self.execute_migration_sql():
                raise Exception("迁移SQL脚本执行失败")
            
            # 创建系统用户
            if not self.create_system_user():
                raise Exception("系统用户创建失败")
            
            # 验证迁移结果
            verification_results = self.verify_migration()
            migration_result["verification_results"] = verification_results
            
            # 检查验证结果
            failed_checks = [k for k, v in verification_results.items() 
                           if isinstance(v, bool) and not v]
            
            if failed_checks:
                raise Exception(f"迁移验证失败: {failed_checks}")
            
            # 记录迁移成功
            self.log_migration_status("completed")
            migration_result["success"] = True
            
            logger.info("用户隔离机制数据迁移完成")
            
        except Exception as e:
            error_message = str(e)
            logger.error(f"数据迁移失败: {error_message}")
            migration_result["error_message"] = error_message
            
            # 记录迁移失败
            self.log_migration_status("failed", error_message)
        
        return migration_result
    
    def rollback_migration(self, backup_path: str) -> bool:
        """回滚迁移（从备份恢复）"""
        try:
            if not os.path.exists(backup_path):
                logger.error(f"备份文件不存在: {backup_path}")
                return False
            
            # 替换当前数据库文件
            import shutil
            shutil.copy2(backup_path, self.db_path)
            
            logger.info(f"数据库已从备份恢复: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"回滚迁移失败: {e}")
            return False

def run_user_isolation_migration(db_path: str = None, create_backup: bool = True) -> Dict[str, Any]:
    """运行用户隔离迁移的便捷函数"""
    if db_path is None:
        db_path = "/Users/<USER>/Code/cash-flow/backend/data/financial_data.db"
    
    migrator = UserIsolationMigrator(db_path)
    return migrator.run_migration(create_backup)

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # 运行迁移
    result = run_user_isolation_migration()
    
    if result["success"]:
        print("✅ 用户隔离机制迁移成功完成")
        print(f"验证结果: {result['verification_results']}")
    else:
        print("❌ 用户隔离机制迁移失败")
        print(f"错误信息: {result['error_message']}")
        
        if result["backup_path"]:
            print(f"备份文件位置: {result['backup_path']}")
            print("可以使用备份文件进行回滚") 