#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财经新闻系统功能测试套件
测试新闻数据同步、AI分析、模型切换等核心功能
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FinancialNewsSystemTester:
    """财经新闻系统测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
    
    def log_test_result(self, test_name: str, success: bool, message: str = ""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        if success:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
        
        logger.info(f"{status} {test_name}: {message}")
    
    async def test_financial_news_manager(self):
        """测试财经新闻管理器"""
        try:
            from backend.core.data.managers.financial_news_manager import get_financial_news_manager
            
            news_manager = get_financial_news_manager()
            
            # 测试数据库连接
            try:
                stats = news_manager.get_statistics()
                self.log_test_result(
                    "财经新闻管理器 - 数据库连接", 
                    True, 
                    f"成功获取统计信息: {stats.get('total_news', 0)} 条新闻"
                )
            except Exception as e:
                self.log_test_result(
                    "财经新闻管理器 - 数据库连接", 
                    False, 
                    f"数据库连接失败: {e}"
                )
                return
            
            # 测试新闻保存功能
            test_news = [{
                'title': '测试新闻标题',
                'content': '这是一条测试新闻内容',
                'publish_time': datetime.now().isoformat(),
                'url': 'https://test.example.com',
                'category': '测试'
            }]
            
            try:
                inserted, updated = news_manager.save_news_batch(
                    test_news, 'test_source', '测试数据源'
                )
                self.log_test_result(
                    "财经新闻管理器 - 新闻保存", 
                    True, 
                    f"成功保存新闻: 新增 {inserted}, 更新 {updated}"
                )
            except Exception as e:
                self.log_test_result(
                    "财经新闻管理器 - 新闻保存", 
                    False, 
                    f"新闻保存失败: {e}"
                )
            
            # 测试新闻查询功能
            try:
                recent_news = news_manager.get_latest_news(limit=5)
                self.log_test_result(
                    "财经新闻管理器 - 新闻查询", 
                    True, 
                    f"成功查询到 {len(recent_news)} 条最新新闻"
                )
            except Exception as e:
                self.log_test_result(
                    "财经新闻管理器 - 新闻查询", 
                    False, 
                    f"新闻查询失败: {e}"
                )
                
        except ImportError as e:
            self.log_test_result(
                "财经新闻管理器 - 模块导入", 
                False, 
                f"模块导入失败: {e}"
            )
    
    async def test_ai_analysis_system(self):
        """测试AI分析系统"""
        try:
            from backend.services.news.news_impact_analyzer import get_news_impact_analyzer
            
            analyzer = get_news_impact_analyzer()
            
            # 测试GLM客户端
            try:
                glm_available = analyzer.glm_client.is_available()
                self.log_test_result(
                    "AI分析系统 - GLM客户端", 
                    glm_available, 
                    "GLM客户端可用" if glm_available else "GLM客户端不可用，请检查API配置"
                )
            except Exception as e:
                self.log_test_result(
                    "AI分析系统 - GLM客户端", 
                    False, 
                    f"GLM客户端测试失败: {e}"
                )
            
            # 测试Gemini客户端
            try:
                gemini_available = analyzer.llm_manager.is_available()
                self.log_test_result(
                    "AI分析系统 - Gemini客户端", 
                    gemini_available, 
                    "Gemini客户端可用" if gemini_available else "Gemini客户端不可用，请检查API配置"
                )
            except Exception as e:
                self.log_test_result(
                    "AI分析系统 - Gemini客户端", 
                    False, 
                    f"Gemini客户端测试失败: {e}"
                )
            
            # 测试新闻分析功能（如果有可用的模型）
            if analyzer.glm_client.is_available() or analyzer.llm_manager.is_available():
                test_news = {
                    'title': '央行宣布降准0.5个百分点',
                    'content': '中国人民银行今日宣布，为支持实体经济发展，决定于近期下调存款准备金率0.5个百分点，释放长期资金约1万亿元。',
                    'source': '测试来源',
                    'publish_time': datetime.now().isoformat()
                }
                
                try:
                    # 选择可用的模型
                    model = 'glm' if analyzer.glm_client.is_available() else 'gemini'
                    result = await analyzer.analyze_news(test_news, model=model)
                    
                    if result.get('success'):
                        analysis = result.get('analysis', {})
                        impact_score = result.get('impact_score', 0)
                        impact_tags = result.get('impact_tags', [])
                        
                        self.log_test_result(
                            f"AI分析系统 - 新闻分析({model.upper()})", 
                            True, 
                            f"分析成功，影响评分: {impact_score:.1f}, 标签数: {len(impact_tags)}"
                        )
                    else:
                        self.log_test_result(
                            f"AI分析系统 - 新闻分析({model.upper()})", 
                            False, 
                            f"分析失败: {result.get('error', '未知错误')}"
                        )
                        
                except Exception as e:
                    self.log_test_result(
                        "AI分析系统 - 新闻分析", 
                        False, 
                        f"新闻分析测试失败: {e}"
                    )
            else:
                self.log_test_result(
                    "AI分析系统 - 新闻分析", 
                    False, 
                    "没有可用的AI模型进行测试"
                )
                
        except ImportError as e:
            self.log_test_result(
                "AI分析系统 - 模块导入", 
                False, 
                f"模块导入失败: {e}"
            )
    
    async def test_cache_system(self):
        """测试缓存系统"""
        try:
            from backend.core.cache.smart_cache_manager import get_smart_cache_manager
            
            cache_manager = get_smart_cache_manager()
            
            # 测试缓存设置和获取
            test_data = {'test_key': 'test_value', 'timestamp': datetime.now().isoformat()}
            cache_key_data = {'test': 'cache_test'}
            
            try:
                # 设置缓存
                cache_manager.set('test_prefix', cache_key_data, test_data, ttl_seconds=60)
                
                # 获取缓存
                cached_value, hit = cache_manager.get('test_prefix', cache_key_data)
                
                if hit and cached_value == test_data:
                    self.log_test_result(
                        "缓存系统 - 基本功能", 
                        True, 
                        "缓存设置和获取功能正常"
                    )
                else:
                    self.log_test_result(
                        "缓存系统 - 基本功能", 
                        False, 
                        f"缓存功能异常: hit={hit}, value_match={cached_value == test_data}"
                    )
                    
            except Exception as e:
                self.log_test_result(
                    "缓存系统 - 基本功能", 
                    False, 
                    f"缓存功能测试失败: {e}"
                )
            
            # 测试缓存统计
            try:
                stats = cache_manager.get_stats()
                self.log_test_result(
                    "缓存系统 - 统计信息", 
                    True, 
                    f"缓存统计正常: 内存项目 {stats['memory_cache']['items']}, 命中率 {stats['performance']['hit_rate_percent']}%"
                )
            except Exception as e:
                self.log_test_result(
                    "缓存系统 - 统计信息", 
                    False, 
                    f"缓存统计测试失败: {e}"
                )
                
        except ImportError as e:
            self.log_test_result(
                "缓存系统 - 模块导入", 
                False, 
                f"模块导入失败: {e}"
            )
    
    async def test_supabase_sync(self):
        """测试Supabase同步功能"""
        try:
            from backend.services.tasks.local_to_supabase_sync import get_supabase_sync_service
            
            sync_service = get_supabase_sync_service()
            
            # 测试同步服务状态
            try:
                status = sync_service.get_sync_status()
                self.log_test_result(
                    "Supabase同步 - 服务状态", 
                    True, 
                    f"同步服务状态: 启用={status['enabled']}, 运行中={status['running']}"
                )
                
                # 如果启用了Supabase，测试同步功能
                if status['enabled']:
                    try:
                        # 测试同步最近1小时的数据
                        result = await sync_service.sync_recent_news(hours_back=1)
                        
                        if result.get('status') in ['success', 'no_data', 'no_valid_data']:
                            self.log_test_result(
                                "Supabase同步 - 数据同步", 
                                True, 
                                f"同步测试成功: {result.get('status')}, 同步数量: {result.get('synced_count', 0)}"
                            )
                        else:
                            self.log_test_result(
                                "Supabase同步 - 数据同步", 
                                False, 
                                f"同步测试失败: {result.get('status')}, 错误: {result.get('errors', [])}"
                            )
                    except Exception as e:
                        self.log_test_result(
                            "Supabase同步 - 数据同步", 
                            False, 
                            f"同步测试异常: {e}"
                        )
                else:
                    self.log_test_result(
                        "Supabase同步 - 数据同步", 
                        False, 
                        "Supabase同步功能未启用，请检查配置"
                    )
                    
            except Exception as e:
                self.log_test_result(
                    "Supabase同步 - 服务状态", 
                    False, 
                    f"同步服务测试失败: {e}"
                )
                
        except ImportError as e:
            self.log_test_result(
                "Supabase同步 - 模块导入", 
                False, 
                f"模块导入失败: {e}"
            )
    
    async def test_batch_analysis(self):
        """测试批量分析功能"""
        try:
            from backend.services.news.batch_analysis_service import get_batch_analysis_service
            from backend.core.data.managers.financial_news_manager import get_financial_news_manager
            
            batch_service = get_batch_analysis_service()
            news_manager = get_financial_news_manager()
            
            # 获取一些测试新闻
            try:
                test_news = news_manager.get_latest_news(limit=3)
                
                if test_news:
                    # 创建批量分析任务
                    task_id = await batch_service.create_batch_analysis_task(
                        news_items=test_news,
                        model='glm'
                    )
                    
                    self.log_test_result(
                        "批量分析 - 任务创建", 
                        True, 
                        f"成功创建批量分析任务: {task_id}"
                    )
                    
                    # 检查任务状态
                    status = batch_service.get_task_status(task_id)
                    if status:
                        self.log_test_result(
                            "批量分析 - 状态查询", 
                            True, 
                            f"任务状态查询成功: {status['status']}"
                        )
                    else:
                        self.log_test_result(
                            "批量分析 - 状态查询", 
                            False, 
                            "无法获取任务状态"
                        )
                        
                else:
                    self.log_test_result(
                        "批量分析 - 测试数据", 
                        False, 
                        "没有可用的测试新闻数据"
                    )
                    
            except Exception as e:
                self.log_test_result(
                    "批量分析 - 功能测试", 
                    False, 
                    f"批量分析测试失败: {e}"
                )
                
        except ImportError as e:
            self.log_test_result(
                "批量分析 - 模块导入", 
                False, 
                f"模块导入失败: {e}"
            )
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始运行财经新闻系统功能测试...")
        
        # 运行各项测试
        await self.test_financial_news_manager()
        await self.test_ai_analysis_system()
        await self.test_cache_system()
        await self.test_supabase_sync()
        await self.test_batch_analysis()
        
        # 输出测试结果摘要
        total_tests = self.passed_tests + self.failed_tests
        success_rate = (self.passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        logger.info("=" * 60)
        logger.info("📊 测试结果摘要")
        logger.info("=" * 60)
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {self.passed_tests} ✅")
        logger.info(f"失败测试: {self.failed_tests} ❌")
        logger.info(f"成功率: {success_rate:.1f}%")
        logger.info("=" * 60)
        
        # 输出失败的测试详情
        if self.failed_tests > 0:
            logger.info("❌ 失败测试详情:")
            for result in self.test_results:
                if not result['success']:
                    logger.info(f"  - {result['test_name']}: {result['message']}")
        
        return {
            'total_tests': total_tests,
            'passed_tests': self.passed_tests,
            'failed_tests': self.failed_tests,
            'success_rate': success_rate,
            'test_results': self.test_results
        }


async def main():
    """主函数"""
    tester = FinancialNewsSystemTester()
    results = await tester.run_all_tests()
    
    # 根据测试结果设置退出码
    exit_code = 0 if results['failed_tests'] == 0 else 1
    sys.exit(exit_code)


if __name__ == "__main__":
    asyncio.run(main())
