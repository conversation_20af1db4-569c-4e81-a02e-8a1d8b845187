#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能缓存管理器
提供多层缓存策略，优化新闻数据和AI分析结果的访问性能
"""

import json
import hashlib
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from threading import Lock
import sqlite3
from contextlib import contextmanager

logger = logging.getLogger(__name__)

@dataclass
class CacheItem:
    """缓存项数据结构"""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    size_bytes: int = 0

class SmartCacheManager:
    """智能缓存管理器"""
    
    def __init__(self, 
                 max_memory_items: int = 1000,
                 max_memory_size_mb: int = 100,
                 db_path: str = "data/cache.db"):
        """
        初始化智能缓存管理器
        
        Args:
            max_memory_items: 内存缓存最大项目数
            max_memory_size_mb: 内存缓存最大大小(MB)
            db_path: 持久化缓存数据库路径
        """
        self.max_memory_items = max_memory_items
        self.max_memory_size_bytes = max_memory_size_mb * 1024 * 1024
        self.db_path = db_path
        
        # 内存缓存
        self._memory_cache: Dict[str, CacheItem] = {}
        self._memory_lock = Lock()
        self._current_memory_size = 0
        
        # 初始化数据库
        self._init_database()
        
        # 缓存统计
        self.stats = {
            'memory_hits': 0,
            'memory_misses': 0,
            'db_hits': 0,
            'db_misses': 0,
            'evictions': 0,
            'total_requests': 0
        }
    
    def _init_database(self):
        """初始化缓存数据库"""
        try:
            with self._get_db_connection() as conn:
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS cache_items (
                        key TEXT PRIMARY KEY,
                        value TEXT NOT NULL,
                        created_at DATETIME NOT NULL,
                        expires_at DATETIME,
                        access_count INTEGER DEFAULT 0,
                        last_accessed DATETIME,
                        size_bytes INTEGER DEFAULT 0
                    )
                """)
                
                # 创建索引
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_cache_expires_at 
                    ON cache_items(expires_at)
                """)
                
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_cache_last_accessed 
                    ON cache_items(last_accessed)
                """)
                
                conn.commit()
                logger.info("缓存数据库初始化完成")
                
        except Exception as e:
            logger.error(f"初始化缓存数据库失败: {e}")
            raise
    
    @contextmanager
    def _get_db_connection(self):
        """获取数据库连接"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            conn.row_factory = sqlite3.Row
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def _generate_cache_key(self, prefix: str, data: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 创建稳定的数据字符串
        sorted_data = json.dumps(data, sort_keys=True, ensure_ascii=False)
        hash_obj = hashlib.md5(sorted_data.encode('utf-8'))
        return f"{prefix}:{hash_obj.hexdigest()}"
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小（字节）"""
        try:
            if isinstance(value, str):
                return len(value.encode('utf-8'))
            elif isinstance(value, (dict, list)):
                return len(json.dumps(value, ensure_ascii=False).encode('utf-8'))
            else:
                return len(str(value).encode('utf-8'))
        except:
            return 1024  # 默认1KB
    
    def _evict_memory_cache(self):
        """内存缓存淘汰策略（LRU + 大小限制）"""
        with self._memory_lock:
            # 如果超过项目数限制或大小限制，进行淘汰
            while (len(self._memory_cache) > self.max_memory_items or 
                   self._current_memory_size > self.max_memory_size_bytes):
                
                if not self._memory_cache:
                    break
                
                # 找到最久未访问的项目
                oldest_key = min(
                    self._memory_cache.keys(),
                    key=lambda k: self._memory_cache[k].last_accessed or datetime.min
                )
                
                # 移除项目
                removed_item = self._memory_cache.pop(oldest_key)
                self._current_memory_size -= removed_item.size_bytes
                self.stats['evictions'] += 1
                
                logger.debug(f"淘汰内存缓存项: {oldest_key}")
    
    def get(self, prefix: str, data: Dict[str, Any], 
            default: Any = None) -> Tuple[Any, bool]:
        """
        获取缓存值
        
        Args:
            prefix: 缓存前缀
            data: 用于生成缓存键的数据
            default: 默认值
            
        Returns:
            (缓存值, 是否命中缓存)
        """
        self.stats['total_requests'] += 1
        cache_key = self._generate_cache_key(prefix, data)
        now = datetime.now()
        
        # 1. 检查内存缓存
        with self._memory_lock:
            if cache_key in self._memory_cache:
                item = self._memory_cache[cache_key]
                
                # 检查是否过期
                if item.expires_at and now > item.expires_at:
                    del self._memory_cache[cache_key]
                    self._current_memory_size -= item.size_bytes
                else:
                    # 更新访问信息
                    item.access_count += 1
                    item.last_accessed = now
                    self.stats['memory_hits'] += 1
                    return item.value, True
        
        self.stats['memory_misses'] += 1
        
        # 2. 检查数据库缓存
        try:
            with self._get_db_connection() as conn:
                cursor = conn.execute("""
                    SELECT value, expires_at, access_count 
                    FROM cache_items 
                    WHERE key = ?
                """, (cache_key,))
                
                row = cursor.fetchone()
                if row:
                    # 检查是否过期
                    expires_at = datetime.fromisoformat(row['expires_at']) if row['expires_at'] else None
                    if expires_at and now > expires_at:
                        # 删除过期项
                        conn.execute("DELETE FROM cache_items WHERE key = ?", (cache_key,))
                        conn.commit()
                    else:
                        # 更新访问信息
                        conn.execute("""
                            UPDATE cache_items 
                            SET access_count = access_count + 1, last_accessed = ?
                            WHERE key = ?
                        """, (now.isoformat(), cache_key))
                        conn.commit()
                        
                        # 解析值
                        try:
                            value = json.loads(row['value'])
                        except:
                            value = row['value']
                        
                        # 添加到内存缓存
                        self._add_to_memory_cache(cache_key, value, expires_at)
                        
                        self.stats['db_hits'] += 1
                        return value, True
                        
        except Exception as e:
            logger.error(f"从数据库缓存获取数据失败: {e}")
        
        self.stats['db_misses'] += 1
        return default, False
    
    def set(self, prefix: str, data: Dict[str, Any], value: Any, 
            ttl_seconds: Optional[int] = None):
        """
        设置缓存值
        
        Args:
            prefix: 缓存前缀
            data: 用于生成缓存键的数据
            value: 缓存值
            ttl_seconds: 生存时间（秒）
        """
        cache_key = self._generate_cache_key(prefix, data)
        now = datetime.now()
        expires_at = now + timedelta(seconds=ttl_seconds) if ttl_seconds else None
        size_bytes = self._calculate_size(value)
        
        # 添加到内存缓存
        self._add_to_memory_cache(cache_key, value, expires_at, size_bytes)
        
        # 保存到数据库
        try:
            with self._get_db_connection() as conn:
                value_json = json.dumps(value, ensure_ascii=False) if not isinstance(value, str) else value
                
                conn.execute("""
                    INSERT OR REPLACE INTO cache_items 
                    (key, value, created_at, expires_at, access_count, last_accessed, size_bytes)
                    VALUES (?, ?, ?, ?, 0, ?, ?)
                """, (
                    cache_key, value_json, now.isoformat(),
                    expires_at.isoformat() if expires_at else None,
                    now.isoformat(), size_bytes
                ))
                conn.commit()
                
        except Exception as e:
            logger.error(f"保存到数据库缓存失败: {e}")
    
    def _add_to_memory_cache(self, key: str, value: Any, 
                           expires_at: Optional[datetime] = None,
                           size_bytes: Optional[int] = None):
        """添加到内存缓存"""
        if size_bytes is None:
            size_bytes = self._calculate_size(value)
        
        with self._memory_lock:
            # 如果已存在，先移除旧的
            if key in self._memory_cache:
                old_item = self._memory_cache[key]
                self._current_memory_size -= old_item.size_bytes
            
            # 创建新的缓存项
            item = CacheItem(
                key=key,
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at,
                last_accessed=datetime.now(),
                size_bytes=size_bytes
            )
            
            self._memory_cache[key] = item
            self._current_memory_size += size_bytes
            
            # 检查是否需要淘汰
            self._evict_memory_cache()
    
    def delete(self, prefix: str, data: Dict[str, Any]):
        """删除缓存项"""
        cache_key = self._generate_cache_key(prefix, data)
        
        # 从内存缓存删除
        with self._memory_lock:
            if cache_key in self._memory_cache:
                item = self._memory_cache.pop(cache_key)
                self._current_memory_size -= item.size_bytes
        
        # 从数据库删除
        try:
            with self._get_db_connection() as conn:
                conn.execute("DELETE FROM cache_items WHERE key = ?", (cache_key,))
                conn.commit()
        except Exception as e:
            logger.error(f"从数据库删除缓存项失败: {e}")
    
    def clear_expired(self):
        """清理过期缓存"""
        now = datetime.now()
        
        # 清理内存缓存
        with self._memory_lock:
            expired_keys = [
                key for key, item in self._memory_cache.items()
                if item.expires_at and now > item.expires_at
            ]
            
            for key in expired_keys:
                item = self._memory_cache.pop(key)
                self._current_memory_size -= item.size_bytes
        
        # 清理数据库缓存
        try:
            with self._get_db_connection() as conn:
                cursor = conn.execute("""
                    DELETE FROM cache_items 
                    WHERE expires_at IS NOT NULL AND expires_at < ?
                """, (now.isoformat(),))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                if deleted_count > 0:
                    logger.info(f"清理了{deleted_count}个过期缓存项")
                    
        except Exception as e:
            logger.error(f"清理过期缓存失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._memory_lock:
            memory_items = len(self._memory_cache)
            memory_size_mb = self._current_memory_size / (1024 * 1024)
        
        # 获取数据库统计
        db_items = 0
        try:
            with self._get_db_connection() as conn:
                cursor = conn.execute("SELECT COUNT(*) as count FROM cache_items")
                row = cursor.fetchone()
                if row:
                    db_items = row['count']
        except:
            pass
        
        hit_rate = 0
        if self.stats['total_requests'] > 0:
            total_hits = self.stats['memory_hits'] + self.stats['db_hits']
            hit_rate = (total_hits / self.stats['total_requests']) * 100
        
        return {
            'memory_cache': {
                'items': memory_items,
                'size_mb': round(memory_size_mb, 2),
                'max_items': self.max_memory_items,
                'max_size_mb': self.max_memory_size_bytes / (1024 * 1024)
            },
            'database_cache': {
                'items': db_items
            },
            'performance': {
                'hit_rate_percent': round(hit_rate, 2),
                'memory_hits': self.stats['memory_hits'],
                'memory_misses': self.stats['memory_misses'],
                'db_hits': self.stats['db_hits'],
                'db_misses': self.stats['db_misses'],
                'evictions': self.stats['evictions'],
                'total_requests': self.stats['total_requests']
            }
        }


# 全局实例
_cache_manager = None

def get_smart_cache_manager() -> SmartCacheManager:
    """获取智能缓存管理器实例（单例模式）"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = SmartCacheManager()
    return _cache_manager


# 缓存装饰器
def smart_cache(prefix: str, ttl_seconds: Optional[int] = 3600):
    """
    智能缓存装饰器
    
    Args:
        prefix: 缓存前缀
        ttl_seconds: 生存时间（秒），默认1小时
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            cache_manager = get_smart_cache_manager()
            
            # 生成缓存数据
            cache_data = {
                'function': func.__name__,
                'args': args,
                'kwargs': kwargs
            }
            
            # 尝试从缓存获取
            result, hit = cache_manager.get(prefix, cache_data)
            if hit:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set(prefix, cache_data, result, ttl_seconds)
            
            return result
        return wrapper
    return decorator
