#!/usr/bin/env python3
"""
股票元数据管理器
负责股票基本信息的持久化存储、定时更新和缓存管理
"""

import sqlite3
import logging
import time
import threading
from datetime import datetime, timedelta
from contextlib import contextmanager
from typing import Dict, List, Optional, Tuple
import pandas as pd
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
import os

logger = logging.getLogger(__name__)

class StockMetadataManager:
    """股票元数据管理器 - 负责持久化存储和定时更新股票基本信息"""
    
    def __init__(self, db_path: str = "/Users/<USER>/Code/cash-flow/backend/data/financial_data.db"):
        self.db_path = db_path
        self.scheduler = None
        self.lock = threading.RLock()
        self._cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 3600  # 1小时缓存
        
        self.ensure_db_directory()
        self.init_enhanced_database()
        self.start_scheduler()
    
    def ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def init_enhanced_database(self):
        """初始化增强的数据库表结构"""
        with self.get_connection() as conn:
            # 创建增强的股票元数据表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS stock_metadata (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    ts_code TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    name TEXT,
                    market TEXT NOT NULL,
                    industry TEXT,
                    list_date TEXT,
                    delist_date TEXT,
                    is_active INTEGER DEFAULT 1,
                    data_source TEXT NOT NULL,
                    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    update_frequency INTEGER DEFAULT 86400,  -- 更新频率（秒）
                    retry_count INTEGER DEFAULT 0,
                    last_error TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(ts_code, market)
                )
            """)
            
            # 创建数据更新日志表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS metadata_update_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    update_type TEXT NOT NULL,  -- 'full', 'incremental', 'retry'
                    market TEXT,
                    total_stocks INTEGER,
                    success_count INTEGER,
                    failed_count INTEGER,
                    start_time TIMESTAMP,
                    end_time TIMESTAMP,
                    error_details TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建API状态监控表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS api_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    api_name TEXT NOT NULL,  -- 'tushare', 'akshare'
                    endpoint TEXT NOT NULL,
                    status TEXT NOT NULL,    -- 'active', 'timeout', 'error'
                    response_time REAL,
                    last_success TIMESTAMP,
                    last_failure TIMESTAMP,
                    failure_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(api_name, endpoint)
                )
            """)
            
            # 创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_stock_metadata_symbol ON stock_metadata(symbol)",
                "CREATE INDEX IF NOT EXISTS idx_stock_metadata_market ON stock_metadata(market)",
                "CREATE INDEX IF NOT EXISTS idx_stock_metadata_active ON stock_metadata(is_active)",
                "CREATE INDEX IF NOT EXISTS idx_stock_metadata_updated ON stock_metadata(last_updated)",
                "CREATE INDEX IF NOT EXISTS idx_update_log_type ON metadata_update_log(update_type)",
                "CREATE INDEX IF NOT EXISTS idx_api_status_name ON api_status(api_name)"
            ]
            
            for index_sql in indexes:
                conn.execute(index_sql)
            
            conn.commit()
            logger.info("股票元数据数据库初始化完成")
    
    def start_scheduler(self):
        """启动定时任务调度器"""
        if self.scheduler is None:
            self.scheduler = BackgroundScheduler()
            
            # 每日凌晨2点更新股票元数据
            self.scheduler.add_job(
                func=self.scheduled_update_all_markets,
                trigger=CronTrigger(hour=2, minute=0),
                id='daily_metadata_update',
                name='每日股票元数据更新',
                replace_existing=True
            )
            
            # 每小时检查API状态
            self.scheduler.add_job(
                func=self.check_api_status,
                trigger=CronTrigger(minute=0),
                id='hourly_api_check',
                name='每小时API状态检查',
                replace_existing=True
            )
            
            # 每6小时清理过期缓存
            self.scheduler.add_job(
                func=self.cleanup_expired_data,
                trigger=CronTrigger(hour='*/6'),
                id='cache_cleanup',
                name='缓存清理任务',
                replace_existing=True
            )
            
            self.scheduler.start()
            logger.info("股票元数据定时任务调度器已启动")
    
    def stop_scheduler(self):
        """停止定时任务调度器"""
        if self.scheduler:
            self.scheduler.shutdown()
            self.scheduler = None
            logger.info("股票元数据定时任务调度器已停止")
    
    def get_cached_stock_list(self, market: str = None, force_refresh: bool = False) -> Dict[str, Dict]:
        """获取缓存的股票列表"""
        with self.lock:
            current_time = time.time()
            
            # 检查缓存是否有效
            if (not force_refresh and 
                self._cache_timestamp and 
                current_time - self._cache_timestamp < self._cache_ttl and
                self._cache):
                logger.info(f"使用缓存的股票数据，缓存时间: {datetime.fromtimestamp(self._cache_timestamp)}")
                if market:
                    return {k: v for k, v in self._cache.items() if v.get('market') == market}
                return self._cache.copy()
            
            # 从数据库加载
            stock_data = self._load_from_database(market)
            
            if stock_data:
                self._cache = stock_data
                self._cache_timestamp = current_time
                logger.info(f"从数据库加载了 {len(stock_data)} 只股票数据")
                return stock_data.copy()
            
            # 如果数据库为空，返回空字典
            logger.warning("数据库中没有股票元数据，建议运行数据更新任务")
            return {}
    
    def _load_from_database(self, market: str = None) -> Dict[str, Dict]:
        """从数据库加载股票数据"""
        try:
            with self.get_connection() as conn:
                if market:
                    query = """
                        SELECT symbol, name, market, industry, ts_code, last_updated
                        FROM stock_metadata 
                        WHERE is_active = 1 AND market = ?
                        ORDER BY symbol
                    """
                    cursor = conn.execute(query, (market,))
                else:
                    query = """
                        SELECT symbol, name, market, industry, ts_code, last_updated
                        FROM stock_metadata 
                        WHERE is_active = 1
                        ORDER BY symbol
                    """
                    cursor = conn.execute(query)
                
                stock_data = {}
                for row in cursor.fetchall():
                    symbol = row['symbol']
                    stock_data[symbol] = {
                        'name': row['name'] or f'{symbol} 股票',
                        'market': row['market'],
                        'industry': row['industry'] or '未知',
                        'ts_code': row['ts_code'],
                        'last_updated': row['last_updated']
                    }
                
                return stock_data
                
        except Exception as e:
            logger.error(f"从数据库加载股票数据失败: {e}")
            return {}
    
    def save_stock_metadata_batch(self, stock_list: pd.DataFrame, data_source: str, market: str):
        """批量保存股票元数据"""
        if stock_list.empty:
            logger.warning(f"股票列表为空，跳过{market}市场数据保存")
            return 0
        
        success_count = 0
        failed_count = 0
        
        try:
            with self.get_connection() as conn:
                for index, row in stock_list.iterrows():
                    try:
                        conn.execute("""
                            INSERT OR REPLACE INTO stock_metadata (
                                ts_code, symbol, name, market, industry, 
                                data_source, last_updated, retry_count, is_active
                            ) VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 0, 1)
                        """, (
                            row.get('ts_code', row.get('symbol')),
                            row['symbol'],
                            row.get('name', ''),
                            market,
                            row.get('industry', ''),
                            data_source
                        ))
                        success_count += 1
                    except Exception as e:
                        logger.error(f"保存股票 {row.get('symbol')} 元数据失败: {e}")
                        failed_count += 1
                
                conn.commit()
                
            # 清除缓存，强制下次重新加载
            with self.lock:
                self._cache = {}
                self._cache_timestamp = None
            
            logger.info(f"批量保存{market}股票元数据完成: 成功{success_count}只, 失败{failed_count}只")
            return success_count

        except Exception as e:
            logger.error(f"批量保存{market}股票元数据失败: {e}")
            return 0

    def scheduled_update_all_markets(self):
        """定时更新所有市场的股票元数据"""
        logger.info("开始定时更新所有市场股票元数据")
        start_time = datetime.now()

        total_success = 0
        total_failed = 0

        # 更新各个市场
        markets = [
            ('CN', 'A股'),
            ('HK', '港股'),
            ('US', '美股')
        ]

        for market_code, market_name in markets:
            try:
                logger.info(f"开始更新{market_name}数据...")
                success, failed = self._update_market_data(market_code, market_name)
                total_success += success
                total_failed += failed

                # 记录API状态
                self._record_api_status('akshare', f'{market_code}_stock_list',
                                      'active' if success > 0 else 'error')

            except Exception as e:
                logger.error(f"更新{market_name}数据失败: {e}")
                total_failed += 1
                self._record_api_status('akshare', f'{market_code}_stock_list', 'error', str(e))

        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        # 记录更新日志
        self._log_update_result('scheduled_full', None, total_success + total_failed,
                              total_success, total_failed, start_time, end_time)

        logger.info(f"定时更新完成: 成功{total_success}只, 失败{total_failed}只, 耗时{duration:.2f}秒")

    def _update_market_data(self, market_code: str, market_name: str) -> Tuple[int, int]:
        """更新特定市场的数据"""
        try:
            # 这里需要集成实际的数据获取逻辑
            # 暂时返回模拟结果
            from backend.core.data.managers.data_manager import init_data_manager

            data_manager = init_data_manager()
            if not data_manager.akshare_data_manager:
                logger.warning(f"AkShare数据管理器未初始化，跳过{market_name}更新")
                return 0, 1

            # 获取股票列表
            stock_df = data_manager.akshare_data_manager.download_stock_list(market_code)

            if not stock_df.empty:
                success_count = self.save_stock_metadata_batch(stock_df, 'akshare', market_name)
                return success_count, len(stock_df) - success_count
            else:
                logger.warning(f"未获取到{market_name}股票数据")
                return 0, 1

        except Exception as e:
            logger.error(f"更新{market_name}数据异常: {e}")
            return 0, 1

    def _record_api_status(self, api_name: str, endpoint: str, status: str, error_msg: str = None):
        """记录API状态"""
        try:
            with self.get_connection() as conn:
                if status == 'active':
                    conn.execute("""
                        INSERT OR REPLACE INTO api_status (
                            api_name, endpoint, status, last_success, failure_count
                        ) VALUES (?, ?, ?, CURRENT_TIMESTAMP, 0)
                    """, (api_name, endpoint, status))
                else:
                    conn.execute("""
                        INSERT OR REPLACE INTO api_status (
                            api_name, endpoint, status, last_failure, failure_count
                        ) VALUES (?, ?, ?, CURRENT_TIMESTAMP,
                                 COALESCE((SELECT failure_count FROM api_status
                                          WHERE api_name = ? AND endpoint = ?), 0) + 1)
                    """, (api_name, endpoint, status, api_name, endpoint))

                conn.commit()
        except Exception as e:
            logger.error(f"记录API状态失败: {e}")

    def _log_update_result(self, update_type: str, market: str, total: int,
                          success: int, failed: int, start_time: datetime, end_time: datetime):
        """记录更新结果"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT INTO metadata_update_log (
                        update_type, market, total_stocks, success_count, failed_count,
                        start_time, end_time
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (update_type, market, total, success, failed, start_time, end_time))
                conn.commit()
        except Exception as e:
            logger.error(f"记录更新日志失败: {e}")

    def check_api_status(self):
        """检查API状态"""
        logger.info("开始检查API状态")

        # 检查各个API端点的状态
        apis_to_check = [
            ('akshare', 'CN_stock_list'),
            ('akshare', 'HK_stock_list'),
            ('akshare', 'US_stock_list'),
            ('tushare', 'stock_basic')
        ]

        for api_name, endpoint in apis_to_check:
            try:
                # 这里可以添加实际的API健康检查逻辑
                # 暂时记录为检查状态
                self._record_api_status(api_name, endpoint, 'checked')
            except Exception as e:
                logger.error(f"检查API {api_name}/{endpoint} 状态失败: {e}")
                self._record_api_status(api_name, endpoint, 'error', str(e))

    def cleanup_expired_data(self):
        """清理过期数据"""
        logger.info("开始清理过期数据")

        try:
            with self.get_connection() as conn:
                # 清理30天前的更新日志
                conn.execute("""
                    DELETE FROM metadata_update_log
                    WHERE created_at < datetime('now', '-30 days')
                """)

                # 清理90天前的API状态记录
                conn.execute("""
                    DELETE FROM api_status
                    WHERE created_at < datetime('now', '-90 days')
                """)

                conn.commit()
                logger.info("过期数据清理完成")

        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")

    def get_update_statistics(self, days: int = 7) -> Dict:
        """获取更新统计信息"""
        try:
            with self.get_connection() as conn:
                # 获取最近N天的更新统计
                cursor = conn.execute("""
                    SELECT
                        update_type,
                        COUNT(*) as update_count,
                        SUM(success_count) as total_success,
                        SUM(failed_count) as total_failed,
                        AVG(julianday(end_time) - julianday(start_time)) * 24 * 3600 as avg_duration
                    FROM metadata_update_log
                    WHERE created_at >= datetime('now', '-{} days')
                    GROUP BY update_type
                """.format(days))

                stats = {}
                for row in cursor.fetchall():
                    stats[row['update_type']] = {
                        'update_count': row['update_count'],
                        'total_success': row['total_success'] or 0,
                        'total_failed': row['total_failed'] or 0,
                        'avg_duration': row['avg_duration'] or 0
                    }

                return stats

        except Exception as e:
            logger.error(f"获取更新统计失败: {e}")
            return {}

    def force_update_market(self, market: str) -> Dict:
        """强制更新指定市场数据"""
        logger.info(f"强制更新{market}市场数据")
        start_time = datetime.now()

        try:
            success, failed = self._update_market_data(market, market)
            end_time = datetime.now()

            self._log_update_result('manual_force', market, success + failed,
                                  success, failed, start_time, end_time)

            return {
                'success': True,
                'message': f'更新完成: 成功{success}只, 失败{failed}只',
                'success_count': success,
                'failed_count': failed,
                'duration': (end_time - start_time).total_seconds()
            }

        except Exception as e:
            logger.error(f"强制更新{market}市场数据失败: {e}")
            return {
                'success': False,
                'message': f'更新失败: {str(e)}',
                'success_count': 0,
                'failed_count': 1
            }

    def get_stock_count_by_market(self) -> Dict[str, int]:
        """获取各市场股票数量统计"""
        try:
            with self.get_connection() as conn:
                cursor = conn.execute("""
                    SELECT market, COUNT(*) as count
                    FROM stock_metadata
                    WHERE is_active = 1
                    GROUP BY market
                """)

                return {row['market']: row['count'] for row in cursor.fetchall()}

        except Exception as e:
            logger.error(f"获取股票数量统计失败: {e}")
            return {}


# 全局实例
_metadata_manager = None

def get_metadata_manager() -> StockMetadataManager:
    """获取全局股票元数据管理器实例"""
    global _metadata_manager
    if _metadata_manager is None:
        _metadata_manager = StockMetadataManager()
    return _metadata_manager

def cleanup_metadata_manager():
    """清理全局股票元数据管理器"""
    global _metadata_manager
    if _metadata_manager:
        _metadata_manager.stop_scheduler()
        _metadata_manager = None
