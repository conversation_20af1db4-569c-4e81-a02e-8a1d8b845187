#!/usr/bin/env python3
"""
数据访问层抽象接口
定义用户感知的数据管理器接口和基类
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
import pandas as pd
from datetime import datetime

class IUserAwareDataManager(ABC):
    """用户感知数据管理器接口"""
    
    @abstractmethod
    def get_stock_data(self, ts_code: str, start_date: str = None, end_date: str = None, 
                      user_id: Optional[int] = None) -> pd.DataFrame:
        """获取股票数据（支持用户过滤）"""
        pass
    
    @abstractmethod
    def save_stock_data(self, df: pd.DataFrame, ts_code: str, 
                       user_id: Optional[int] = None) -> None:
        """保存股票数据（关联用户）"""
        pass
    
    @abstractmethod
    def get_factor_data(self, ts_code: str, trade_date: str = None, 
                       factor_names: List[str] = None, 
                       user_id: Optional[int] = None) -> Dict[str, float]:
        """获取因子数据（支持用户过滤）"""
        pass
    
    @abstractmethod
    def save_factor_data(self, ts_code: str, trade_date: str, factors: Dict[str, float],
                        user_id: Optional[int] = None) -> None:
        """保存因子数据（关联用户）"""
        pass
    
    @abstractmethod
    def get_available_stocks(self, user_id: Optional[int] = None) -> List[str]:
        """获取可用股票列表（支持用户过滤）"""
        pass
    
    @abstractmethod
    def get_user_watchlist(self, user_id: int) -> List[str]:
        """获取用户关注列表"""
        pass
    
    @abstractmethod
    def add_to_watchlist(self, user_id: int, ts_code: str) -> bool:
        """添加股票到用户关注列表"""
        pass
    
    @abstractmethod
    def remove_from_watchlist(self, user_id: int, ts_code: str) -> bool:
        """从用户关注列表移除股票"""
        pass

class BaseUserAwareDataManager(IUserAwareDataManager):
    """用户感知数据管理器基类"""
    
    def __init__(self, underlying_manager: Any):
        """
        初始化基类
        
        Args:
            underlying_manager: 底层数据管理器实例
        """
        self.underlying_manager = underlying_manager
        self._system_user_id = -1  # 系统用户ID
    
    def _get_effective_user_id(self, user_id: Optional[int] = None) -> Optional[int]:
        """获取有效的用户ID"""
        if user_id is not None:
            return user_id
        
        # 尝试从用户上下文获取
        try:
            from backend.auth.user_context import get_current_user_id
            context_user_id = get_current_user_id()
            if context_user_id is not None:
                return context_user_id
        except ImportError:
            pass
        
        # 返回系统用户ID作为默认值
        return self._system_user_id
    
    def _is_system_user(self, user_id: Optional[int] = None) -> bool:
        """检查是否为系统用户"""
        effective_user_id = self._get_effective_user_id(user_id)
        return effective_user_id == self._system_user_id
    
    def _should_filter_by_user(self, user_id: Optional[int] = None) -> bool:
        """判断是否需要按用户过滤数据"""
        effective_user_id = self._get_effective_user_id(user_id)
        # 系统用户可以访问所有数据，不需要过滤
        return effective_user_id != self._system_user_id
    
    def _add_user_filter_to_query(self, base_query: str, user_id: Optional[int] = None) -> str:
        """为SQL查询添加用户过滤条件"""
        if not self._should_filter_by_user(user_id):
            return base_query
        
        effective_user_id = self._get_effective_user_id(user_id)
        
        # 如果查询中已经有WHERE子句
        if "WHERE" in base_query.upper():
            return f"{base_query} AND (user_id = {effective_user_id} OR user_id IS NULL)"
        else:
            return f"{base_query} WHERE (user_id = {effective_user_id} OR user_id IS NULL)"
    
    def _prepare_data_with_user_id(self, data: Dict[str, Any], 
                                  user_id: Optional[int] = None) -> Dict[str, Any]:
        """为数据添加用户ID字段"""
        effective_user_id = self._get_effective_user_id(user_id)
        
        # 如果是系统用户，不添加user_id字段（保持为NULL，表示公共数据）
        if effective_user_id == self._system_user_id:
            return data
        
        # 为普通用户数据添加user_id
        data_with_user = data.copy()
        data_with_user['user_id'] = effective_user_id
        return data_with_user

class DataAccessError(Exception):
    """数据访问错误"""
    pass

class UserDataIsolationError(DataAccessError):
    """用户数据隔离错误"""
    pass

class UnauthorizedDataAccessError(DataAccessError):
    """未授权的数据访问错误"""
    pass 