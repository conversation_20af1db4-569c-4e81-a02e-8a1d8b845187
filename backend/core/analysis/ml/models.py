#!/usr/bin/env python3
"""
机器学习模型模块
支持多种算法的训练、预测和评估
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Tuple, Optional
import logging
from datetime import datetime, timedelta
import joblib
import os
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.linear_model import LinearRegression, Ridge, Lasso
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

# 尝试导入可选的机器学习库
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    from sklearn.neural_network import MLPRegressor
    NEURAL_NETWORK_AVAILABLE = True
except ImportError:
    NEURAL_NETWORK_AVAILABLE = False

logger = logging.getLogger(__name__)

class MLModelManager:
    """机器学习模型管理器"""
    
    def __init__(self, model_dir: str = "models"):
        self.model_dir = model_dir
        os.makedirs(model_dir, exist_ok=True)
        
        # 支持的模型配置
        self.model_configs = {
            'linear_regression': {
                'name': '线性回归',
                'class': LinearRegression,
                'params': {},
                'available': True
            },
            'ridge_regression': {
                'name': '岭回归',
                'class': Ridge,
                'params': {'alpha': 1.0},
                'available': True
            },
            'lasso_regression': {
                'name': 'Lasso回归',
                'class': Lasso,
                'params': {'alpha': 1.0},
                'available': True
            },
            'random_forest': {
                'name': '随机森林',
                'class': RandomForestRegressor,
                'params': {'n_estimators': 100, 'random_state': 42},
                'available': True
            },
            'gradient_boosting': {
                'name': '梯度提升',
                'class': GradientBoostingRegressor,
                'params': {'n_estimators': 100, 'random_state': 42},
                'available': True
            }
        }
        
        # 添加可选模型
        if XGBOOST_AVAILABLE:
            self.model_configs['xgboost'] = {
                'name': 'XGBoost',
                'class': xgb.XGBRegressor,
                'params': {'n_estimators': 100, 'random_state': 42, 'eval_metric': 'rmse'},
                'available': True
            }
        
        if LIGHTGBM_AVAILABLE:
            self.model_configs['lightgbm'] = {
                'name': 'LightGBM',
                'class': lgb.LGBMRegressor,
                'params': {'n_estimators': 100, 'random_state': 42, 'verbose': -1},
                'available': True
            }
            
        if NEURAL_NETWORK_AVAILABLE:
            self.model_configs['neural_network'] = {
                'name': '神经网络',
                'class': MLPRegressor,
                'params': {'hidden_layer_sizes': (100, 50), 'random_state': 42, 'max_iter': 1000},
                'available': True
            }
        
        self.trained_models = {}
        self.scalers = {}
    
    def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用模型列表"""
        models = []
        for model_id, config in self.model_configs.items():
            models.append({
                'id': model_id,
                'name': config['name'],
                'available': config['available']
            })
        return models
    
    def create_model(self, model_id: str) -> Any:
        """创建模型实例"""
        if model_id not in self.model_configs:
            raise ValueError(f"不支持的模型类型: {model_id}")
        
        config = self.model_configs[model_id]
        if not config['available']:
            raise ValueError(f"模型 {model_id} 不可用，请安装相应依赖")
        
        return config['class'](**config['params'])
    
    def train_model(self, model_id: str, X: pd.DataFrame, y: pd.Series, 
                   test_size: float = 0.2) -> Dict[str, Any]:
        """训练模型"""
        try:
            # 创建模型
            model = self.create_model(model_id)
            
            # 数据预处理
            X_clean, y_clean = self._preprocess_data(X, y)
            
            # 划分训练测试集
            X_train, X_test, y_train, y_test = train_test_split(
                X_clean, y_clean, test_size=test_size, random_state=42
            )
            
            # 特征缩放
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # 训练模型
            model.fit(X_train_scaled, y_train)
            
            # 预测
            y_train_pred = model.predict(X_train_scaled)
            y_test_pred = model.predict(X_test_scaled)
            
            # 计算评估指标
            train_metrics = self._calculate_metrics(y_train, y_train_pred)
            test_metrics = self._calculate_metrics(y_test, y_test_pred)
            
            # 保存模型和缩放器
            self.trained_models[model_id] = model
            self.scalers[model_id] = scaler
            self._save_model(model_id, model, scaler)
            
            return {
                'model_id': model_id,
                'model_name': self.model_configs[model_id]['name'],
                'training_samples': len(X_train),
                'test_samples': len(X_test),
                'features': list(X_clean.columns),
                'train_metrics': train_metrics,
                'test_metrics': test_metrics,
                'feature_importance': self._get_feature_importance(model, X_clean.columns),
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"模型训练失败: {e}")
            return {
                'model_id': model_id,
                'status': 'error',
                'error': str(e)
            }
    
    def predict(self, model_id: str, X: pd.DataFrame) -> Dict[str, Any]:
        """使用训练好的模型进行预测"""
        try:
            # 检查模型是否存在
            if model_id not in self.trained_models:
                self._load_model(model_id)
            
            if model_id not in self.trained_models:
                raise ValueError(f"模型 {model_id} 未训练或不存在")
            
            model = self.trained_models[model_id]
            scaler = self.scalers[model_id]
            
            # 数据预处理
            X_clean = self._preprocess_predict_data(X)
            
            # 特征缩放
            X_scaled = scaler.transform(X_clean)
            
            # 预测
            predictions = model.predict(X_scaled)
            
            return {
                'model_id': model_id,
                'predictions': predictions.tolist(),
                'features_used': list(X_clean.columns),
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {
                'model_id': model_id,
                'status': 'error',
                'error': str(e)
            }
    
    def _preprocess_data(self, X: pd.DataFrame, y: pd.Series) -> Tuple[pd.DataFrame, pd.Series]:
        """数据预处理"""
        # 删除缺失值过多的列
        threshold = len(X) * 0.5
        X_clean = X.dropna(axis=1, thresh=threshold)
        
        # 删除包含缺失值的行
        valid_indices = X_clean.dropna().index
        X_clean = X_clean.loc[valid_indices]
        y_clean = y.loc[valid_indices]
        
        # 删除无限值
        X_clean = X_clean.replace([np.inf, -np.inf], np.nan).dropna()
        y_clean = y_clean.loc[X_clean.index]
        
        return X_clean, y_clean
    
    def _preprocess_predict_data(self, X: pd.DataFrame) -> pd.DataFrame:
        """预测数据预处理"""
        # 处理缺失值和无限值
        X_clean = X.replace([np.inf, -np.inf], np.nan)
        X_clean = X_clean.fillna(X_clean.median())
        
        return X_clean
    
    def _calculate_metrics(self, y_true: pd.Series, y_pred: np.ndarray) -> Dict[str, float]:
        """计算评估指标"""
        return {
            'mse': mean_squared_error(y_true, y_pred),
            'rmse': np.sqrt(mean_squared_error(y_true, y_pred)),
            'mae': mean_absolute_error(y_true, y_pred),
            'r2': r2_score(y_true, y_pred),
            'mape': np.mean(np.abs((y_true - y_pred) / np.maximum(np.abs(y_true), 1e-8))) * 100
        }
    
    def _get_feature_importance(self, model: Any, feature_names: List[str]) -> Dict[str, float]:
        """获取特征重要性"""
        importance_dict = {}
        
        if hasattr(model, 'feature_importances_'):
            # 树模型
            importances = model.feature_importances_
            for name, importance in zip(feature_names, importances):
                importance_dict[name] = float(importance)
        elif hasattr(model, 'coef_'):
            # 线性模型
            coefficients = np.abs(model.coef_)
            for name, coef in zip(feature_names, coefficients):
                importance_dict[name] = float(coef)
        
        return importance_dict
    
    def _save_model(self, model_id: str, model: Any, scaler: Any):
        """保存模型"""
        try:
            model_path = os.path.join(self.model_dir, f"{model_id}_model.joblib")
            scaler_path = os.path.join(self.model_dir, f"{model_id}_scaler.joblib")
            
            joblib.dump(model, model_path)
            joblib.dump(scaler, scaler_path)
            
            logger.info(f"模型 {model_id} 保存成功")
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
    
    def _load_model(self, model_id: str):
        """加载模型"""
        try:
            model_path = os.path.join(self.model_dir, f"{model_id}_model.joblib")
            scaler_path = os.path.join(self.model_dir, f"{model_id}_scaler.joblib")
            
            if os.path.exists(model_path) and os.path.exists(scaler_path):
                model = joblib.load(model_path)
                scaler = joblib.load(scaler_path)
                
                self.trained_models[model_id] = model
                self.scalers[model_id] = scaler
                
                logger.info(f"模型 {model_id} 加载成功")
            else:
                logger.warning(f"模型文件不存在: {model_id}")
        except Exception as e:
            logger.error(f"加载模型失败: {e}")

class ModelPredictor:
    """模型预测器"""
    
    def __init__(self, model_manager: MLModelManager):
        self.model_manager = model_manager
    
    def predict_stock_performance(self, symbols: List[str], model_id: str, 
                                factor_calculator) -> Dict[str, Any]:
        """预测股票表现"""
        try:
            predictions = {}
            
            for symbol in symbols:
                try:
                    # 获取因子数据
                    factors = factor_calculator.calculate_all_factors(symbol)
                    if not factors:
                        predictions[symbol] = {'error': '无法获取因子数据'}
                        continue
                    
                    # 准备特征数据
                    feature_data = pd.DataFrame([factors])
                    
                    # 预测
                    result = self.model_manager.predict(model_id, feature_data)
                    
                    if result['status'] == 'success':
                        predicted_return = result['predictions'][0]
                        predictions[symbol] = {
                            'stock_predictions': [{
                                'symbol': symbol,
                                'predicted_return': float(predicted_return),
                                'timestamp': datetime.now().isoformat()
                            }]
                        }
                    else:
                        predictions[symbol] = {'error': result['error']}
                        
                except Exception as e:
                    predictions[symbol] = {'error': str(e)}
            
            return {
                'model_id': model_id,
                'predictions': predictions,
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"股票预测失败: {e}")
            return {
                'model_id': model_id,
                'status': 'error',
                'error': str(e)
            }
    
    def compare_models(self, symbols: List[str], test_size: float = 0.2) -> Dict[str, Any]:
        """比较多个模型的性能"""
        try:
            # 这里可以实现模型比较逻辑
            # 暂时返回占位符结果
            return {
                'comparison': 'Model comparison feature coming soon',
                'status': 'success'
            }
        except Exception as e:
            logger.error(f"模型比较失败: {e}")
            return {
                'status': 'error',
                'error': str(e)
            } 