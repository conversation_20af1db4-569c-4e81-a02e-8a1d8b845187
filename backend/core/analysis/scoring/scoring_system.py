#!/usr/bin/env python3
"""
股票评分系统
基于多因子和机器学习模型的综合评分系统
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, timedelta
from backend.core.analysis.factors.basic_factors import FactorCalculator, FACTOR_METADATA
from backend.core.analysis.ml.models import MLModelManager, ModelPredictor
from backend.core.data.managers.data_manager import DataManager

logger = logging.getLogger(__name__)

class FactorScorer:
    """因子评分器"""
    
    def __init__(self):
        self.factor_weights = self._get_default_factor_weights()
        self.score_ranges = {
            'excellent': (80, 100),
            'good': (60, 80),
            'average': (40, 60),
            'poor': (20, 40),
            'very_poor': (0, 20)
        }
    
    def _get_default_factor_weights(self) -> Dict[str, Dict[str, float]]:
        """获取默认因子权重配置"""
        return {
            'technical': {
                'macd': 0.20,
                'rsi': 0.15,
                'sma50': 0.15,
                'bollinger_position': 0.12,
                'atr': 0.10,
                'ema20': 0.10,
                'sma200': 0.08,
                'stochastic_k': 0.05,
                'williams_r': 0.05,
            },
            'fundamental': {
                'roe': 0.20,
                'pe_ratio': 0.15,
                'pb_ratio': 0.15,
                'ps_ratio': 0.12,
                'debt_to_equity': 0.10,
                'roa': 0.10,
                'current_ratio': 0.08,
                'revenue_growth': 0.05,
                'earnings_growth': 0.05,
            },
            'capital': {
                'money_flow': 0.18,
                'big_order_ratio': 0.16,
                'institutional_holdings': 0.14,
                'margin_balance': 0.12,
                'volume_ratio': 0.10,
                'etf_flows': 0.10,
                'foreign_capital': 0.08,
                'insider_trading': 0.06,
                'capital_inflow': 0.06,
            },
            'chip': {
                'turnover_rate': 0.18,
                'chip_concentration': 0.16,
                'profit_ratio': 0.14,
                'cost_distribution': 0.12,
                'floating_ratio': 0.10,
                'lock_ratio': 0.10,
                'average_cost': 0.08,
                'chip_peak': 0.06,
                'chip_divergence': 0.06,
            }
        }
    
    def normalize_factor(self, factor_name: str, value: float, category: str) -> float:
        """标准化单个因子值到0-100分"""
        try:
            if pd.isna(value) or np.isinf(value):
                return 50.0  # 默认中性评分
            
            # 技术面因子标准化
            if category == 'technical':
                if factor_name == 'rsi':
                    if value <= 30:
                        return 90 + (30 - value) / 3  # 超卖，高分
                    elif value >= 70:
                        return 10 + (100 - value) / 3  # 超买，低分
                    else:
                        return 40 + (50 - abs(value - 50)) * 0.8  # 中性区域
                
                elif factor_name == 'macd':
                    return max(0, min(100, 50 + value * 500))  # MACD标准化
                
                elif factor_name == 'bollinger_position':
                    if 0.2 <= value <= 0.8:
                        return 80 + (0.5 - abs(value - 0.5)) * 40  # 中间位置最佳
                    else:
                        return max(0, 40 - abs(value - 0.5) * 80)
                
                elif 'sma' in factor_name or 'ema' in factor_name:
                    return max(0, min(100, 50 + value * 50))  # 移动平均线
                
                else:
                    return max(0, min(100, 50 + value * 25))  # 其他技术指标
            
            # 基本面因子标准化
            elif category == 'fundamental':
                if factor_name == 'pe_ratio':
                    if 10 <= value <= 20:
                        return 90 + (20 - abs(value - 15)) * 2
                    elif value < 5:
                        return 20
                    elif value > 50:
                        return 10
                    else:
                        return max(10, 70 - abs(value - 15) * 2)
                
                elif factor_name == 'roe':
                    return min(100, max(0, value * 5))  # ROE越高越好
                
                elif factor_name == 'debt_to_equity':
                    if 0.2 <= value <= 0.5:
                        return 80 + (0.35 - abs(value - 0.35)) * 133
                    else:
                        return max(0, 60 - abs(value - 0.35) * 100)
                
                else:
                    return max(0, min(100, 50 + value * 25))
            
            # 资金面和筹码面因子 - 使用相对排名法
            else:
                return max(0, min(100, 50 + value * 25))
                
        except Exception as e:
            logger.warning(f"因子 {factor_name} 标准化失败: {e}")
            return 50.0
    
    def calculate_factor_score(self, symbol: str, factor_calculator: FactorCalculator, data_manager: DataManager = None) -> Dict[str, Any]:
        """计算股票的因子评分"""
        try:
            # 获取股票数据
            if data_manager:
                stock_data = data_manager.get_stock_data(symbol)
                if stock_data.empty:
                    raise ValueError(f"无法获取股票 {symbol} 的历史数据")
                # 使用股票数据计算因子
                factors = factor_calculator.calculate_all_factors(stock_data)
            else:
                # 如果没有数据管理器，尝试使用模拟数据
                mock_data = self._generate_mock_data(symbol)
                factors = factor_calculator.calculate_all_factors(mock_data)
            
            if not factors:
                raise ValueError(f"无法获取股票 {symbol} 的因子数据")
            
            category_scores = {}
            total_score = 0
            total_weight = 0
            
            # 按类别计算评分
            for category, weights in self.factor_weights.items():
                category_total = 0
                category_weight = 0
                category_details = []
                
                for factor_name, weight in weights.items():
                    if factor_name in factors:
                        factor_value = factors[factor_name]
                        normalized_score = self.normalize_factor(factor_name, factor_value, category)
                        weighted_score = normalized_score * weight
                        
                        category_total += weighted_score
                        category_weight += weight
                        
                        category_details.append({
                            'factor': factor_name,
                            'value': factor_value,
                            'score': normalized_score,
                            'weight': weight,
                            'weighted_score': weighted_score
                        })
                
                if category_weight > 0:
                    category_avg_score = category_total / category_weight
                    category_scores[category] = {
                        'score': category_avg_score,
                        'weight': category_weight,
                        'details': category_details
                    }
                    
                    total_score += category_total
                    total_weight += category_weight
            
            # 计算综合评分
            overall_score = total_score / total_weight if total_weight > 0 else 50
            score_grade = self._get_score_grade(overall_score)
            
            return {
                'symbol': symbol,
                'overall_score': round(overall_score, 2),
                'score_grade': score_grade,
                'category_scores': category_scores,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"计算因子评分失败 ({symbol}): {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _get_score_grade(self, score: float) -> str:
        """根据评分获取等级"""
        for grade, (min_score, max_score) in self.score_ranges.items():
            if min_score <= score <= max_score:
                return grade
        return 'unknown'
    
    def _generate_mock_data(self, symbol: str) -> pd.DataFrame:
        """生成模拟股票数据用于测试"""
        import numpy as np
        from datetime import datetime, timedelta
        
        # 生成30天的模拟数据
        dates = pd.date_range(end=datetime.now(), periods=30, freq='D')
        np.random.seed(hash(symbol) % (2**32))  # 基于股票代码的固定种子
        
        # 模拟价格数据
        base_price = 100 + hash(symbol) % 100  # 基础价格
        price_changes = np.random.normal(0, 0.02, 30)  # 2%的日波动
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1))  # 价格不能为负
        
        mock_data = pd.DataFrame({
            'trade_date': dates.strftime('%Y%m%d'),
            'close': prices,
            'open': [p * (1 + np.random.uniform(-0.01, 0.01)) for p in prices],
            'high': [p * (1 + abs(np.random.uniform(0, 0.02))) for p in prices],
            'low': [p * (1 - abs(np.random.uniform(0, 0.02))) for p in prices],
            'volume': np.random.randint(1000000, 10000000, 30),
            'pe': np.random.uniform(10, 30, 30),
            'pb': np.random.uniform(1, 5, 30),
            'pct_change': np.concatenate([[0], np.diff(prices) / prices[:-1] * 100])
        })
        
        return mock_data

class MLScorer:
    """机器学习评分器"""
    
    def __init__(self, model_manager: MLModelManager):
        self.model_manager = model_manager
        self.default_models = ['random_forest', 'xgboost', 'lightgbm']
    
    def calculate_ml_score(self, symbol: str, factor_calculator: FactorCalculator, data_manager: DataManager = None) -> Dict[str, Any]:
        """计算股票的ML评分"""
        try:
            # 获取股票数据和因子数据
            if data_manager:
                stock_data = data_manager.get_stock_data(symbol)
                if stock_data.empty:
                    raise ValueError(f"无法获取股票 {symbol} 的历史数据")
                factors = factor_calculator.calculate_all_factors(stock_data)
            else:
                # 如果没有数据管理器，使用模拟数据
                mock_data = self._generate_mock_data(symbol)
                factors = factor_calculator.calculate_all_factors(mock_data)
            
            if not factors:
                raise ValueError(f"无法获取股票 {symbol} 的因子数据")
            
            # 准备特征数据
            feature_data = pd.DataFrame([factors])
            
            model_predictions = {}
            valid_predictions = []
            
            # 使用多个模型进行预测
            for model_id in self.default_models:
                try:
                    result = self.model_manager.predict(model_id, feature_data)
                    if result['status'] == 'success':
                        prediction = result['predictions'][0]
                        model_predictions[model_id] = prediction
                        valid_predictions.append(prediction)
                    else:
                        logger.warning(f"模型 {model_id} 预测失败: {result.get('error', 'Unknown error')}")
                except Exception as e:
                    logger.warning(f"模型 {model_id} 预测异常: {e}")
            
            if not valid_predictions:
                raise ValueError("所有模型预测都失败")
            
            # 集成预测结果
            ensemble_prediction = np.mean(valid_predictions)
            prediction_std = np.std(valid_predictions) if len(valid_predictions) > 1 else 0
            
            # 将预测收益率转换为0-100评分
            ml_score = self._prediction_to_score(ensemble_prediction)
            score_grade = self._get_score_grade(ml_score)
            
            return {
                'symbol': symbol,
                'ensemble_score': round(ml_score, 2),
                'score_grade': score_grade,
                'ensemble_prediction': round(ensemble_prediction, 4),
                'prediction_std': round(prediction_std, 4),
                'models_used': len(valid_predictions),
                'model_predictions': model_predictions,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"计算ML评分失败 ({symbol}): {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _prediction_to_score(self, prediction: float) -> float:
        """将预测收益率转换为评分"""
        # 假设预测收益率范围在-50%到+50%之间
        # 转换为0-100分数
        normalized = (prediction + 0.5) / 1.0  # 归一化到0-1
        score = max(0, min(100, normalized * 100))
        return score
    
    def _get_score_grade(self, score: float) -> str:
        """根据评分获取等级"""
        if score >= 80:
            return 'excellent'
        elif score >= 70:
            return 'good'
        elif score >= 50:
            return 'average'
        elif score >= 30:
            return 'poor'
        else:
            return 'very_poor'
    
    def _generate_mock_data(self, symbol: str) -> pd.DataFrame:
        """生成模拟股票数据用于测试"""
        import numpy as np
        from datetime import datetime, timedelta
        
        # 生成30天的模拟数据
        dates = pd.date_range(end=datetime.now(), periods=30, freq='D')
        np.random.seed(hash(symbol) % (2**32))  # 基于股票代码的固定种子
        
        # 模拟价格数据
        base_price = 100 + hash(symbol) % 100  # 基础价格
        price_changes = np.random.normal(0, 0.02, 30)  # 2%的日波动
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(max(new_price, 1))  # 价格不能为负
        
        mock_data = pd.DataFrame({
            'trade_date': dates.strftime('%Y%m%d'),
            'close': prices,
            'open': [p * (1 + np.random.uniform(-0.01, 0.01)) for p in prices],
            'high': [p * (1 + abs(np.random.uniform(0, 0.02))) for p in prices],
            'low': [p * (1 - abs(np.random.uniform(0, 0.02))) for p in prices],
            'volume': np.random.randint(1000000, 10000000, 30),
            'pe': np.random.uniform(10, 30, 30),
            'pb': np.random.uniform(1, 5, 30),
            'pct_change': np.concatenate([[0], np.diff(prices) / prices[:-1] * 100])
        })
        
        return mock_data

class StockRanker:
    """股票排名器"""
    
    def __init__(self, factor_scorer: FactorScorer, ml_scorer: MLScorer):
        self.factor_scorer = factor_scorer
        self.ml_scorer = ml_scorer
    
    def rank_stocks(self, symbols: List[str], factor_calculator: FactorCalculator,
                   factor_weight: float = 0.6, ml_weight: float = 0.4,
                   max_stocks: int = 20, data_manager: DataManager = None) -> Dict[str, Any]:
        """对股票进行排名"""
        try:
            if factor_weight + ml_weight != 1.0:
                raise ValueError("因子权重和ML权重之和必须等于1.0")
            
            rankings = []
            
            for symbol in symbols[:max_stocks]:  # 限制处理的股票数量
                try:
                    # 计算因子评分
                    factor_result = self.factor_scorer.calculate_factor_score(symbol, factor_calculator, data_manager)
                    factor_score = factor_result.get('overall_score', 50)
                    
                    # 计算ML评分
                    ml_result = self.ml_scorer.calculate_ml_score(symbol, factor_calculator, data_manager)
                    ml_score = ml_result.get('ensemble_score', 50)
                    
                    # 计算综合评分
                    composite_score = factor_score * factor_weight + ml_score * ml_weight
                    composite_grade = self._get_composite_grade(composite_score)
                    
                    # 确定推荐类型
                    recommendation_type = self._get_recommendation_type(
                        factor_score, ml_score, composite_score
                    )
                    
                    rankings.append({
                        'symbol': symbol,
                        'factor_score': round(factor_score, 2),
                        'ml_score': round(ml_score, 2),
                        'composite_score': round(composite_score, 2),
                        'composite_grade': composite_grade,
                        'recommendation_type': recommendation_type,
                        'factor_details': factor_result,
                        'ml_details': ml_result
                    })
                    
                except Exception as e:
                    logger.error(f"处理股票 {symbol} 失败: {e}")
                    continue
            
            # 按综合评分排序
            rankings.sort(key=lambda x: x['composite_score'], reverse=True)
            
            # 生成统计信息
            stats = self._generate_ranking_stats(rankings)
            
            return {
                'rankings': rankings,
                'factor_weight': factor_weight,
                'ml_weight': ml_weight,
                'total_analyzed': len(symbols),
                'total_ranked': len(rankings),
                'stats': stats,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"股票排名失败: {e}")
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _get_composite_grade(self, score: float) -> str:
        """获取综合评分等级"""
        if score >= 85:
            return 'excellent'
        elif score >= 70:
            return 'good'
        elif score >= 55:
            return 'average'
        elif score >= 40:
            return 'poor'
        else:
            return 'very_poor'
    
    def _get_recommendation_type(self, factor_score: float, ml_score: float, 
                               composite_score: float) -> str:
        """确定推荐类型"""
        if composite_score >= 85:
            return 'top_pick'
        elif factor_score > ml_score + 10:
            return 'value_play'
        elif ml_score > factor_score + 10:
            return 'growth_momentum'
        elif factor_score >= 70 and ml_score >= 70:
            return 'balanced'
        else:
            return 'neutral'
    
    def _generate_ranking_stats(self, rankings: List[Dict]) -> Dict[str, Any]:
        """生成排名统计信息"""
        if not rankings:
            return {}
        
        scores = [r['composite_score'] for r in rankings]
        grades = [r['composite_grade'] for r in rankings]
        types = [r['recommendation_type'] for r in rankings]
        
        return {
            'avg_score': round(np.mean(scores), 2),
            'median_score': round(np.median(scores), 2),
            'score_std': round(np.std(scores), 2),
            'grade_distribution': {grade: grades.count(grade) for grade in set(grades)},
            'type_distribution': {type_: types.count(type_) for type_ in set(types)},
            'top_performers': len([s for s in scores if s >= 80]),
            'underperformers': len([s for s in scores if s < 40])
        }

# 全局单例实例
_stock_ranker = None

def get_stock_ranker(factor_calculator: FactorCalculator = None, 
                    model_manager: MLModelManager = None) -> StockRanker:
    """获取股票排名器单例"""
    global _stock_ranker
    if _stock_ranker is None and factor_calculator and model_manager:
        factor_scorer = FactorScorer()
        ml_scorer = MLScorer(model_manager)
        _stock_ranker = StockRanker(factor_scorer, ml_scorer)
    return _stock_ranker 