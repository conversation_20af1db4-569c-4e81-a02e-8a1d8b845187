#!/usr/bin/env python3
"""
因子管理模块
包含技术面、基本面、资金面、筹码面等36个内置因子的计算逻辑
"""

import pandas as pd
import numpy as np
import yfinance as yf
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import logging
import time

logger = logging.getLogger(__name__)

class FactorCalculator:
    """因子计算器"""
    
    def __init__(self):
        # 移除缓存逻辑，因为数据获取和缓存由 DataManager 负责
        pass
    
    def _validate_data(self, data: np.ndarray, min_length: int = 1) -> bool:
        """验证输入数据的有效性"""
        if data is None or len(data) < min_length:
            return False
        if np.any(np.isnan(data)) or np.any(np.isinf(data)):
            logger.warning("数据包含NaN或无穷值")
            return False
        return True
    
    def _clean_data(self, data: np.ndarray) -> np.ndarray:
        """清理数据，移除异常值"""
        # 移除NaN和无穷值
        clean_data = data[~(np.isnan(data) | np.isinf(data))]
        return clean_data
    
    def calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI指标 - 使用标准的指数移动平均方法"""
        # 数据验证
        if not self._validate_data(prices, period + 1):
            return 50.0
        
        # 清理数据
        clean_prices = self._clean_data(prices)
        if len(clean_prices) < period + 1:
            return 50.0
        
        # 计算价格变化
        deltas = np.diff(clean_prices)
        
        # 分离涨跌
        ups = np.where(deltas > 0, deltas, 0)
        downs = np.where(deltas < 0, -deltas, 0)
        
        # 如果数据长度刚好等于所需长度，使用简单移动平均作为初始值
        if len(ups) == period:
            avg_up = np.mean(ups)
            avg_down = np.mean(downs)
        else:
            # 计算指数移动平均
            # 第一个周期使用简单移动平均
            first_avg_up = np.mean(ups[:period])
            first_avg_down = np.mean(downs[:period])
            
            # 后续使用指数移动平均
            avg_up = first_avg_up
            avg_down = first_avg_down
            alpha = 1.0 / period  # 平滑因子
            
            for i in range(period, len(ups)):
                avg_up = alpha * ups[i] + (1 - alpha) * avg_up
                avg_down = alpha * downs[i] + (1 - alpha) * avg_down
        
        # 避免除零错误
        if avg_down == 0:
            return 100.0
        
        rs = avg_up / avg_down
        rsi = 100 - (100 / (1 + rs))
        return float(rsi)
    
    def calculate_sma(self, prices: np.ndarray, period: int) -> float:
        """计算简单移动平均"""
        if len(prices) < period:
            return float(prices[-1]) if len(prices) > 0 else 0.0
        return float(np.mean(prices[-period:]))
    
    def calculate_rsi_vectorized(self, prices: np.ndarray, period: int = 14) -> np.ndarray:
        """向量化RSI计算 - 返回整个RSI序列"""
        if len(prices) < period + 1:
            return np.full(len(prices), 50.0)
        
        # 计算价格变化
        deltas = np.diff(prices)
        
        # 分离涨跌
        ups = np.where(deltas > 0, deltas, 0)
        downs = np.where(deltas < 0, -deltas, 0)
        
        # 使用pandas计算指数移动平均，这是最优化的方法
        import pandas as pd
        ups_ema = pd.Series(ups).ewm(span=period, adjust=False).mean().values
        downs_ema = pd.Series(downs).ewm(span=period, adjust=False).mean().values
        
        # 避免除零错误
        rs = np.where(downs_ema == 0, 100, ups_ema / downs_ema)
        rsi = 100 - (100 / (1 + rs))
        
        # 前面几个值设为50（因为数据不足）
        result = np.full(len(prices), 50.0)
        result[period:] = rsi[period-1:]
        
        return result
    
    def calculate_bollinger_bands(self, prices: np.ndarray, period: int = 20, std_dev: int = 2):
        """计算布林带"""
        if len(prices) < period:
            price = prices[-1] if len(prices) > 0 else 0.0
            return price, price, price
        
        # 使用滚动窗口计算，确保数据变化时结果也会变化
        recent_prices = prices[-period:]
        sma = np.mean(recent_prices)
        std = np.std(recent_prices, ddof=1)  # 使用样本标准差
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        
        return upper, sma, lower
    
    def calculate_technical_factors(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算技术面因子"""
        factors = {}
        
        # 价格数据
        # 确保列名标准化
        close = data['close'].values if 'close' in data.columns else None
        high = data['high'].values if 'high' in data.columns else None
        low = data['low'].values if 'low' in data.columns else None
        volume = data['volume'].values if 'volume' in data.columns else None

        if close is None or len(close) == 0:
            logger.warning("数据中缺少 'close' 列或数据为空，无法计算技术面因子")
            return {}
        
        try:
            # RSI相对强弱指标
            factors['rsi'] = self.calculate_rsi(close)
            
            # MACD指标（简化版）
            ema12 = pd.Series(close).ewm(span=12).mean().iloc[-1]
            ema26 = pd.Series(close).ewm(span=26).mean().iloc[-1]
            factors['macd'] = float(ema12 - ema26)
            
            # 布林带
            upper, middle, lower = self.calculate_bollinger_bands(close)
            if upper != lower:
                factors['bollinger'] = float((close[-1] - lower) / (upper - lower))
            else:
                factors['bollinger'] = 0.5
            
            # 移动平均线
            factors['sma20'] = self.calculate_sma(close, 20)
            factors['sma50'] = self.calculate_sma(close, 50)
            factors['sma200'] = self.calculate_sma(close, 200) if len(close) >= 200 else close[-1]
            
            # 成交量移动平均
            if volume is not None and len(volume) > 0:
                factors['volume_sma'] = self.calculate_sma(volume, 20)
            else:
                factors['volume_sma'] = 0.0
            
            # 平均真实波动率（简化版）
            if high is not None and low is not None and close is not None and len(high) > 1 and len(low) > 1 and len(close) > 1:
                tr_list = []
                for i in range(1, min(15, len(high))):
                    tr1 = high[i] - low[i]
                    tr2 = abs(high[i] - close[i-1])
                    tr3 = abs(low[i] - close[i-1])
                    tr_list.append(max(tr1, tr2, tr3))
                factors['atr'] = float(np.mean(tr_list)) if tr_list else 0.0
            else:
                factors['atr'] = 0.0
            
            # 随机指标（简化版）
            if high is not None and low is not None and close is not None and len(high) >= 14 and len(low) >= 14 and len(close) >= 14:
                recent_high = np.max(high[-14:])
                recent_low = np.min(low[-14:])
                if recent_high != recent_low:
                    factors['stoch'] = float((close[-1] - recent_low) / (recent_high - recent_low) * 100)
                else:
                    factors['stoch'] = 50.0
            else:
                factors['stoch'] = 50.0
            
        except Exception as e:
            logger.error(f"计算技术面因子错误: {e}")
            # 提供默认值
            default_factors = {
                'rsi': 50.0, 'macd': 0.0, 'bollinger': 0.5,
                'sma20': close[-1] if close is not None and len(close) > 0 else 0.0, 
                'sma50': close[-1] if close is not None and len(close) > 0 else 0.0, 
                'sma200': close[-1] if close is not None and len(close) > 0 else 0.0,
                'volume_sma': volume[-1] if volume is not None and len(volume) > 0 else 0.0,
                'atr': 0.0, 'stoch': 50.0
            }
            factors.update(default_factors)
        
        return factors
    
    def calculate_fundamental_factors(self, data: pd.DataFrame) -> Dict[str, float]: # 接收数据框
        """计算基本面因子"""
        factors = {}
        
        if data.empty:
            logger.warning("数据为空，无法计算基本面因子")
            return {
                'pe_ratio': 15.0, 'pb_ratio': 1.5, 'ps_ratio': 2.0,
                'roe': 0.15, 'roa': 0.05, 'debt_ratio': 0.3,
                'current_ratio': 1.5, 'revenue_growth': 0.05, 'eps_growth': 0.05
            }

        try:
            # 估值指标
            factors['pe_ratio'] = float(data['pe'].iloc[-1]) if 'pe' in data.columns and not pd.isna(data['pe'].iloc[-1]) else 15.0
            factors['pb_ratio'] = float(data['pb'].iloc[-1]) if 'pb' in data.columns and not pd.isna(data['pb'].iloc[-1]) else 2.0
            # 以下因子需要财务报表数据，这里用模拟值或yfinance获取，但为了解耦，这里不再直接获取
            factors['ps_ratio'] = 2.0 # info.get('priceToSalesTrailing12Months', 0.0) or 0.0
            
            # 盈利能力指标
            factors['roe'] = 0.15 # info.get('returnOnEquity', 0.0) or 0.0
            factors['roa'] = 0.05 # info.get('returnOnAssets', 0.0) or 0.0
            
            # 偿债能力指标
            factors['debt_ratio'] = 0.3 # info.get('debtToEquity', 0.0) or 0.0
            factors['current_ratio'] = 1.5 # info.get('currentRatio', 1.0) or 1.0
            
            # 成长性指标
            factors['revenue_growth'] = 0.05 # info.get('revenueGrowth', 0.0) or 0.0
            factors['eps_growth'] = 0.05 # info.get('earningsGrowth', 0.0) or 0.0
            
        except Exception as e:
            logger.error(f"计算基本面因子错误: {e}")
            # 提供默认值
            factors = {
                'pe_ratio': 15.0, 'pb_ratio': 1.5, 'ps_ratio': 2.0,
                'roe': 0.15, 'roa': 0.05, 'debt_ratio': 0.3,
                'current_ratio': 1.5, 'revenue_growth': 0.05, 'eps_growth': 0.05
            }
        
        return factors
    
    def calculate_chip_factors(self, data: pd.DataFrame) -> Dict[str, float]:
        """计算筹码面因子"""
        factors = {}
        
        if data.empty:
            logger.warning("数据为空，无法计算筹码面因子")
            return {
                'turnover_rate': 1.0, 'concentration': 0.5, 'avg_cost': 100.0,
                'profit_ratio': 0.0, 'locked_ratio': 0.5, 'floating_ratio': 0.5,
                'cost_distribution': 0.1, 'chip_peak': 100.0,
                'support_pressure': 0.1
            }

        try:
            close = data['close'].values if 'close' in data.columns else None
            volume = data['volume'].values if 'volume' in data.columns else None
            high = data['high'].values if 'high' in data.columns else None
            low = data['low'].values if 'low' in data.columns else None
            
            if close is None or volume is None or high is None or low is None or len(close) == 0:
                raise ValueError("计算筹码面因子所需的数据不足")

            # 换手率（简化计算）
            avg_volume = np.mean(volume[-20:]) if len(volume) > 20 else volume[-1]
            factors['turnover_rate'] = float(volume[-1] / avg_volume) if avg_volume > 0 else 1.0
            
            # 筹码集中度（基于价格波动性）
            price_std = np.std(close[-20:]) if len(close) > 20 else 0
            factors['concentration'] = float(1.0 / (1.0 + price_std / close[-1])) if close[-1] > 0 else 0.5
            
            # 平均成本（20日加权平均价）
            if len(close) > 20 and len(volume) > 20:
                weights = volume[-20:]
                prices = close[-20:]
                factors['avg_cost'] = float(np.average(prices, weights=weights))
            else:
                factors['avg_cost'] = float(close[-1])
            
            # 获利盘比例（当前价格高于平均成本的比例）
            avg_cost = factors['avg_cost']
            factors['profit_ratio'] = float(max(0, (close[-1] - avg_cost) / avg_cost)) if avg_cost > 0 else 0.0
            
            # 锁定盘比例（模拟长期持有比例）
            factors['locked_ratio'] = float(np.random.uniform(0.3, 0.7))
            
            # 浮筹比例
            factors['floating_ratio'] = float(1.0 - factors['locked_ratio'])
            
            # 成本分布（价格分散度）
            if len(close) > 20:
                cost_distribution = np.std(close[-20:]) / np.mean(close[-20:])
                factors['cost_distribution'] = float(cost_distribution)
            else:
                factors['cost_distribution'] = 0.1
            
            # 筹码峰值（主要成本区间，以当前价格为参考）
            factors['chip_peak'] = float(close[-1])
            
            # 支撑压力位（基于历史价格密集区）
            if len(close) > 50:
                # 简化的支撑压力计算
                support = np.percentile(close[-50:], 25)
                pressure = np.percentile(close[-50:], 75)
                factors['support_pressure'] = float((pressure - support) / close[-1]) if close[-1] > 0 else 0.1
            else:
                factors['support_pressure'] = 0.1
            
        except Exception as e:
            logger.error(f"计算筹码面因子错误: {e}")
            # 提供默认值
            factors = {
                'turnover_rate': 1.0, 'concentration': 0.5, 'avg_cost': close[-1] if close is not None and len(close) > 0 else 100.0,
                'profit_ratio': 0.0, 'locked_ratio': 0.5, 'floating_ratio': 0.5,
                'cost_distribution': 0.1, 'chip_peak': close[-1] if close is not None and len(close) > 0 else 100.0,
                'support_pressure': 0.1
            }
        
        return factors
    
    def calculate_all_factors(self, data: pd.DataFrame, factor_ids: List[str] = None) -> Dict[str, float]: # 接收数据框
        """计算所有因子（优先使用数据库数据）"""
        if data.empty:
            logger.warning(f"数据为空，无法计算因子")
            return {}
        
        all_factors = {}
        
        # 根据请求的因子类型计算
        if factor_ids is None:
            factor_ids = list(FACTOR_CALCULATORS.keys())
        
        for factor_id in factor_ids:
            if factor_id in FACTOR_CALCULATORS:
                try:
                    value = FACTOR_CALCULATORS[factor_id](data)
                    if value is not None and not (isinstance(value, float) and np.isnan(value)):
                        all_factors[factor_id] = value
                except Exception as e:
                    logger.error(f"计算因子失败 {factor_id}: {e}")
                    all_factors[factor_id] = 0.0
        
        return all_factors

# 因子元数据
FACTOR_METADATA = {
    # 技术面因子
    'rsi': {'name': 'RSI相对强弱指标', 'category': 'technical', 'description': '衡量股价超买超卖情况'},
    'macd': {'name': 'MACD指标', 'category': 'technical', 'description': '趋势跟踪动量指标'},
    'bollinger': {'name': '布林带', 'category': 'technical', 'description': '价格通道指标'},
    'sma20': {'name': '20日移动平均线', 'category': 'technical', 'description': '短期趋势指标'},
    'sma50': {'name': '50日移动平均线', 'category': 'technical', 'description': '中期趋势指标'},
    'sma200': {'name': '200日移动平均线', 'category': 'technical', 'description': '长期趋势指标'},
    'volume_sma': {'name': '成交量移动平均', 'category': 'technical', 'description': '成交量趋势'},
    'atr': {'name': '平均真实波动率', 'category': 'technical', 'description': '波动性指标'},
    'stoch': {'name': '随机指标', 'category': 'technical', 'description': '超买超卖指标'},
    
    # 基本面因子
    'pe_ratio': {'name': '市盈率', 'category': 'fundamental', 'description': '估值指标'},
    'pb_ratio': {'name': '市净率', 'category': 'fundamental', 'description': '估值指标'},
    'ps_ratio': {'name': '市销率', 'category': 'fundamental', 'description': '估值指标'},
    'roe': {'name': '净资产收益率', 'category': 'fundamental', 'description': '盈利能力指标'},
    'roa': {'name': '总资产收益率', 'category': 'fundamental', 'description': '盈利能力指标'},
    'debt_ratio': {'name': '资产负债率', 'category': 'fundamental', 'description': '偿债能力指标'},
    'current_ratio': {'name': '流动比率', 'category': 'fundamental', 'description': '流动性指标'},
    'revenue_growth': {'name': '营收增长率', 'category': 'fundamental', 'description': '成长性指标'},
    'eps_growth': {'name': 'EPS增长率', 'category': 'fundamental', 'description': '成长性指标'},
    
    # 筹码面因子
    'turnover_rate': {'name': '换手率', 'category': 'chip', 'description': '筹码活跃度'},
    'concentration': {'name': '筹码集中度', 'category': 'chip', 'description': '筹码分布集中程度'},
    'avg_cost': {'name': '平均成本', 'category': 'chip', 'description': '市场平均持股成本'},
    'profit_ratio': {'name': '获利盘比例', 'category': 'chip', 'description': '盈利筹码占比'},
    'locked_ratio': {'name': '锁定盘比例', 'category': 'chip', 'description': '长期持有筹码占比'},
    'floating_ratio': {'name': '浮筹比例', 'category': 'chip', 'description': '短期交易筹码占比'},
    'cost_distribution': {'name': '成本分布', 'category': 'chip', 'description': '筹码成本分布情况'},
    'chip_peak': {'name': '筹码峰值', 'category': 'chip', 'description': '主要成本区间'},
    'support_pressure': {'name': '支撑压力', 'category': 'chip', 'description': '基于筹码的支撑压力位'},
}

def get_all_factors():
    """获取所有内置因子列表"""
    return [
        {
            'id': factor_id,
            'name': metadata['name'],
            'category': metadata['category'],
            'description': metadata['description'],
            'isCustom': False,
            'isActive': True
        }
        for factor_id, metadata in FACTOR_METADATA.items()
    ]

# 标准化因子计算函数
def calculate_rsi_factor(df: pd.DataFrame) -> float:
    """计算RSI相对强弱指数 - 使用正确的指数移动平均"""
    if len(df) < 15 or 'close' not in df.columns:
        return 50.0
    
    delta = df['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    
    # 使用指数移动平均（EMA）而不是简单移动平均
    gain_ema = gain.ewm(span=14, adjust=False).mean()
    loss_ema = loss.ewm(span=14, adjust=False).mean()
    
    # 避免除零错误
    rs = gain_ema / loss_ema.replace(0, np.nan)
    rsi = 100 - (100 / (1 + rs))
    
    result = rsi.iloc[-1] if len(rsi) > 0 and not pd.isna(rsi.iloc[-1]) else 50.0
    return float(result)

def calculate_macd_factor(df: pd.DataFrame) -> float:
    """计算MACD指标"""
    if len(df) < 26 or 'close' not in df.columns:
        return 0.0
    
    exp1 = df['close'].ewm(span=12, adjust=False).mean()
    exp2 = df['close'].ewm(span=26, adjust=False).mean()
    macd = exp1 - exp2
    
    return float(macd.iloc[-1]) if not pd.isna(macd.iloc[-1]) else 0.0

def calculate_bollinger_factor(df: pd.DataFrame) -> float:
    """计算布林带位置（价格相对于布林带的位置）"""
    if len(df) < 20 or 'close' not in df.columns:
        return 0.5
    
    ma20 = df['close'].rolling(window=20).mean()
    std20 = df['close'].rolling(window=20).std()
    upper_band = ma20 + (std20 * 2)
    lower_band = ma20 - (std20 * 2)
    
    # 计算价格在布林带中的相对位置
    current_price = df['close'].iloc[-1]
    upper = upper_band.iloc[-1]
    lower = lower_band.iloc[-1]
    
    if upper == lower:
        return 0.5
    
    position = (current_price - lower) / (upper - lower)
    return float(np.clip(position, 0, 1))

def calculate_ma20_factor(df: pd.DataFrame) -> float:
    """计算20日移动平均线"""
    if len(df) < 20 or 'close' not in df.columns:
        return df['close'].iloc[-1] if len(df) > 0 else 100.0
    
    ma20 = df['close'].rolling(window=20).mean()
    return float(ma20.iloc[-1]) if not pd.isna(ma20.iloc[-1]) else float(df['close'].iloc[-1])

def calculate_ma50_factor(df: pd.DataFrame) -> float:
    """计算50日移动平均线"""
    if len(df) < 50 or 'close' not in df.columns:
        return df['close'].iloc[-1] if len(df) > 0 else 100.0
    
    ma50 = df['close'].rolling(window=50).mean()
    return float(ma50.iloc[-1]) if not pd.isna(ma50.iloc[-1]) else float(df['close'].iloc[-1])

def calculate_volume_ma_factor(df: pd.DataFrame) -> float:
    """计算成交量移动平均"""
    if len(df) < 20:
        return float(df['volume'].iloc[-1]) if len(df) > 0 and 'volume' in df.columns else 0.0
    
    volume_col = 'volume' if 'volume' in df.columns else 'vol' # 兼容列名
    if volume_col not in df.columns:
        return 0.0
    
    volume_ma = df[volume_col].rolling(window=20).mean()
    return float(volume_ma.iloc[-1]) if not pd.isna(volume_ma.iloc[-1]) else 0.0

def calculate_atr_factor(df: pd.DataFrame) -> float:
    """计算平均真实波动率"""
    if len(df) < 14:
        return 0.0
    
    required_cols = ['high', 'low', 'close']
    if not all(col in df.columns for col in required_cols):
        return 0.0
    
    high_low = df['high'] - df['low']
    high_close = np.abs(df['high'] - df['close'].shift())
    low_close = np.abs(df['low'] - df['close'].shift())
    
    true_range = np.maximum(high_low, np.maximum(high_close, low_close))
    atr = true_range.rolling(window=14).mean()
    
    return float(atr.iloc[-1]) if not pd.isna(atr.iloc[-1]) else 0.0

def calculate_pe_ratio_factor(df: pd.DataFrame) -> float:
    """计算市盈率"""
    if 'pe' in df.columns and len(df) > 0:
        pe = df['pe'].iloc[-1]
        return float(pe) if not pd.isna(pe) and pe > 0 else 15.0
    return 15.0  # 默认市盈率

def calculate_pb_ratio_factor(df: pd.DataFrame) -> float:
    """计算市净率"""
    if 'pb' in df.columns and len(df) > 0:
        pb = df['pb'].iloc[-1]
        return float(pb) if not pd.isna(pb) and pb > 0 else 2.0
    return 2.0  # 默认市净率

def calculate_price_change_factor(df: pd.DataFrame) -> float:
    """计算价格变化率"""
    if len(df) < 2 or 'close' not in df.columns:
        return 0.0
    
    if 'pct_change' in df.columns:
        pct_change = df['pct_change'].iloc[-1]
        return float(pct_change) if not pd.isna(pct_change) else 0.0
    
    # 手动计算
    current_price = df['close'].iloc[-1]
    prev_price = df['close'].iloc[-2]
    
    if prev_price == 0:
        return 0.0
    
    change = (current_price - prev_price) / prev_price * 100
    return float(change)

def calculate_volatility_factor(df: pd.DataFrame) -> float:
    """计算波动率"""
    if len(df) < 20 or 'close' not in df.columns:
        return 0.0
    
    returns = df['close'].pct_change().dropna()
    volatility = returns.rolling(window=20).std().iloc[-1]
    
    return float(volatility * np.sqrt(252)) if not pd.isna(volatility) else 0.0  # 年化波动率

# 因子计算函数映射
FACTOR_CALCULATORS = {
    'rsi': calculate_rsi_factor,
    'macd': calculate_macd_factor,
    'bollinger': calculate_bollinger_factor,
    'ma20': calculate_ma20_factor,
    'ma50': calculate_ma50_factor,
    'volume_ma': calculate_volume_ma_factor,
    'atr': calculate_atr_factor,
    'pe_ratio': calculate_pe_ratio_factor,
    'pb_ratio': calculate_pb_ratio_factor,
    'price_change': calculate_price_change_factor,
    'volatility': calculate_volatility_factor,
}

# 更新因子元数据
FACTOR_METADATA.update({
    'price_change': {'name': '价格变化率', 'category': 'technical', 'description': '当日价格变化百分比'},
    'volatility': {'name': '波动率', 'category': 'technical', 'description': '价格波动程度'},
})

# 筹码面因子
def calculate_turnover_rate_factor(df: pd.DataFrame) -> float:
    """计算换手率（简化）"""
    try:
        if 'volume' not in df.columns or len(df) < 1:
            return 1.0
        
        volume = df['volume'].dropna()  # 移除NaN值
        if len(volume) == 0:
            return 1.0
            
        if len(volume) < 20:
            return 1.0  # 数据不足，返回默认值
            
        recent_volume = volume.iloc[-20:]
        current_volume = volume.iloc[-1]
        avg_volume = recent_volume.mean()
        
        if avg_volume > 0 and not pd.isna(avg_volume) and not pd.isna(current_volume):
            ratio = float(current_volume / avg_volume)
            return ratio if ratio > 0 else 1.0
        else:
            return 1.0
    except Exception as e:
        logger.error(f"计算换手率因子失败: {e}")
        return 1.0

def calculate_concentration_factor(df: pd.DataFrame) -> float:
    """计算筹码集中度（基于价格波动性）"""
    try:
        if 'close' not in df.columns or len(df) < 20:
            return 0.5
        
        close = df['close'].dropna()  # 移除NaN值
        if len(close) < 20:
            return 0.5
            
        recent_close = close.iloc[-20:]
        current_price = close.iloc[-1]
        
        if pd.isna(current_price) or current_price <= 0:
            return 0.5
            
        price_std = recent_close.std()
        if pd.isna(price_std) or price_std < 0:
            return 0.5
            
        concentration = 1.0 / (1.0 + price_std / current_price)
        return float(concentration) if not pd.isna(concentration) else 0.5
    except Exception as e:
        logger.error(f"计算筹码集中度因子失败: {e}")
        return 0.5

def calculate_avg_cost_factor(df: pd.DataFrame) -> float:
    """计算平均成本（20日加权平均价）"""
    try:
        if 'close' not in df.columns or len(df) < 1:
            return 100.0
            
        if 'volume' not in df.columns or len(df) < 20:
            # 如果没有成交量数据，返回简单平均价格
            close = df['close'].dropna()
            if len(close) > 0:
                return float(close.iloc[-1])
            else:
                return 100.0
        
        close = df['close'].dropna()
        volume = df['volume'].dropna()
        
        if len(close) == 0 or len(volume) == 0:
            return 100.0
            
        # 确保close和volume长度一致
        min_length = min(len(close), len(volume))
        if min_length < 20:
            return float(close.iloc[-1]) if len(close) > 0 else 100.0
            
        recent_close = close.iloc[-20:]
        recent_volume = volume.iloc[-20:]
        
        # 移除NaN值对
        valid_mask = ~(pd.isna(recent_close) | pd.isna(recent_volume) | (recent_volume <= 0))
        valid_close = recent_close[valid_mask]
        valid_volume = recent_volume[valid_mask]
        
        if len(valid_close) == 0 or len(valid_volume) == 0:
            return float(close.iloc[-1]) if len(close) > 0 else 100.0
            
        total_volume = valid_volume.sum()
        if total_volume > 0:
            weighted_avg = (valid_close * valid_volume).sum() / total_volume
            return float(weighted_avg) if not pd.isna(weighted_avg) else float(close.iloc[-1])
        else:
            return float(close.iloc[-1]) if len(close) > 0 else 100.0
    except Exception as e:
        logger.error(f"计算平均成本因子失败: {e}")
        return 100.0

def calculate_profit_ratio_factor(df: pd.DataFrame) -> float:
    """计算获利盘比例"""
    try:
        if 'close' not in df.columns or len(df) == 0:
            return 0.0
        
        close = df['close'].dropna()
        if len(close) == 0:
            return 0.0
            
        current_price = close.iloc[-1]
        if pd.isna(current_price) or current_price <= 0:
            return 0.0
            
        avg_cost = calculate_avg_cost_factor(df)
        if pd.isna(avg_cost) or avg_cost <= 0:
            return 0.0
            
        if avg_cost > 0:
            profit_ratio = (current_price - avg_cost) / avg_cost
            return float(max(0, profit_ratio))
        else:
            return 0.0
    except Exception as e:
        logger.error(f"计算获利盘比例因子失败: {e}")
        return 0.0

def calculate_cost_distribution_factor(df: pd.DataFrame) -> float:
    """计算成本分布（价格分散度）"""
    try:
        if 'close' not in df.columns or len(df) < 20:
            return 0.1
        
        close = df['close'].dropna()
        if len(close) < 20:
            return 0.1
            
        recent_close = close.iloc[-20:]
        
        price_mean = recent_close.mean()
        price_std = recent_close.std()
        
        if pd.isna(price_mean) or pd.isna(price_std) or price_mean <= 0:
            return 0.1
            
        distribution = price_std / price_mean
        return float(distribution) if not pd.isna(distribution) and distribution >= 0 else 0.1
    except Exception as e:
        logger.error(f"计算成本分布因子失败: {e}")
        return 0.1

def calculate_chip_peak_factor(df: pd.DataFrame) -> float:
    """计算筹码峰值（主要成本区间，以当前价格为参考）"""
    try:
        if 'close' not in df.columns or len(df) == 0:
            return 100.0
        
        close = df['close'].dropna()
        if len(close) == 0:
            return 100.0
            
        current_price = close.iloc[-1]
        return float(current_price) if not pd.isna(current_price) else 100.0
    except Exception as e:
        logger.error(f"计算筹码峰值因子失败: {e}")
        return 100.0

def calculate_support_pressure_factor(df: pd.DataFrame) -> float:
    """计算支撑压力位（基于历史价格密集区）"""
    try:
        if 'close' not in df.columns or len(df) < 50:
            return 0.1
        
        close = df['close'].dropna()
        if len(close) < 50:
            return 0.1
            
        recent_close = close.iloc[-50:]
        current_price = close.iloc[-1]
        
        if pd.isna(current_price) or current_price <= 0:
            return 0.1
            
        support = recent_close.quantile(0.25)
        pressure = recent_close.quantile(0.75)
        
        if pd.isna(support) or pd.isna(pressure):
            return 0.1
            
        if current_price > 0:
            ratio = (pressure - support) / current_price
            return float(ratio) if not pd.isna(ratio) and ratio >= 0 else 0.1
        else:
            return 0.1
    except Exception as e:
        logger.error(f"计算支撑压力因子失败: {e}")
        return 0.1

FACTOR_CALCULATORS['turnover_rate'] = calculate_turnover_rate_factor
FACTOR_CALCULATORS['concentration'] = calculate_concentration_factor
FACTOR_CALCULATORS['avg_cost'] = calculate_avg_cost_factor
FACTOR_CALCULATORS['profit_ratio'] = calculate_profit_ratio_factor
FACTOR_CALCULATORS['cost_distribution'] = calculate_cost_distribution_factor
FACTOR_CALCULATORS['chip_peak'] = calculate_chip_peak_factor
FACTOR_CALCULATORS['support_pressure'] = calculate_support_pressure_factor

def test_rsi_calculation():
    """测试RSI计算的正确性"""
    # 创建测试数据 - 价格递增序列
    test_prices = np.array([44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89,
                           46.03, 46.83, 47.69, 46.49, 46.26, 47.09, 46.66, 46.80, 46.23, 46.32])
    
    calculator = FactorCalculator()
    rsi = calculator.calculate_rsi(test_prices, 14)
    
    print(f"测试RSI计算结果: {rsi:.2f}")
    
    # 测试数据长度变化时RSI是否变化
    rsi1 = calculator.calculate_rsi(test_prices[:15], 14)
    rsi2 = calculator.calculate_rsi(test_prices[:18], 14)
    rsi3 = calculator.calculate_rsi(test_prices, 14)
    
    print(f"15个数据点的RSI: {rsi1:.2f}")
    print(f"18个数据点的RSI: {rsi2:.2f}")
    print(f"20个数据点的RSI: {rsi3:.2f}")
    
    # 验证RSI值应该随数据长度变化
    if rsi1 != rsi2 or rsi2 != rsi3:
        print("✅ RSI计算正确：随数据变化而变化")
        return True
    else:
        print("❌ RSI计算可能有问题：值没有随数据变化")
        return False

def test_factor_calculation_consistency():
    """测试因子计算的一致性"""
    # 创建测试DataFrame
    test_data = {
        'close': [44, 44.34, 44.09, 44.15, 43.61, 44.33, 44.83, 45.85, 46.08, 45.89,
                 46.03, 46.83, 47.69, 46.49, 46.26, 47.09, 46.66, 46.80, 46.23, 46.32],
        'high': [44.5, 44.54, 44.29, 44.35, 43.81, 44.53, 45.03, 46.05, 46.28, 46.09,
                46.23, 47.03, 47.89, 46.69, 46.46, 47.29, 46.86, 47.00, 46.43, 46.52],
        'low': [43.5, 44.14, 43.89, 43.95, 43.41, 44.13, 44.63, 45.65, 45.88, 45.69,
               45.83, 46.63, 47.49, 46.29, 46.06, 46.89, 46.46, 46.60, 46.03, 46.12],
        'volume': [1000000] * 20
    }
    
    df = pd.DataFrame(test_data)
    
    # 测试不同长度的数据
    rsi1 = calculate_rsi_factor(df.iloc[:15])
    rsi2 = calculate_rsi_factor(df.iloc[:18])
    rsi3 = calculate_rsi_factor(df)
    
    print(f"DataFrame RSI 测试:")
    print(f"15行数据的RSI: {rsi1:.2f}")
    print(f"18行数据的RSI: {rsi2:.2f}")
    print(f"20行数据的RSI: {rsi3:.2f}")
    
    if rsi1 != rsi2 or rsi2 != rsi3:
        print("✅ DataFrame RSI计算正确：随数据变化而变化")
        return True
    else:
        print("❌ DataFrame RSI计算可能有问题：值没有随数据变化")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("因子计算系统测试")
    print("=" * 50)
    
    test1_result = test_rsi_calculation()
    print()
    test2_result = test_factor_calculation_consistency()
    
    print("\n" + "=" * 50)
    if test1_result and test2_result:
        print("🎉 所有测试通过！RSI计算问题已修复。")
    else:
        print("⚠️  仍有测试未通过，需要进一步调试。") 