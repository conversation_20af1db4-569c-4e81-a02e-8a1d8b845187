#!/usr/bin/env python3
"""
系统用户管理器
处理系统默认用户的创建、管理和数据分配
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

from .user_manager import UserManager
from .models import UserCreate, UserResponse as User
from .password_utils import hash_password

logger = logging.getLogger(__name__)

class SystemUserManager:
    """系统用户管理器"""
    
    SYSTEM_USER_ID = -1
    SYSTEM_USERNAME = "system"
    SYSTEM_EMAIL = "system@localhost"
    SYSTEM_FULL_NAME = "System User"
    
    def __init__(self, user_manager: UserManager):
        self.user_manager = user_manager
    
    def create_system_user(self) -> Optional[User]:
        """创建系统用户"""
        try:
            # 检查系统用户是否已存在
            existing_user = self.user_manager.get_user_by_id(self.SYSTEM_USER_ID)
            if existing_user:
                logger.info("系统用户已存在")
                return existing_user
            
            # 创建系统用户数据
            system_user_data = UserCreate(
                username=self.SYSTEM_USERNAME,
                email=self.SYSTEM_EMAIL,
                password="system_placeholder_password",  # 系统用户不需要真实密码
                full_name=self.SYSTEM_FULL_NAME
            )
            
            # 使用特殊的系统用户ID创建用户
            with self.user_manager.get_connection() as conn:
                password_hash = hash_password("system_placeholder_password")
                
                conn.execute("""
                    INSERT INTO users (id, username, email, password_hash, full_name, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    self.SYSTEM_USER_ID,
                    system_user_data.username,
                    system_user_data.email,
                    password_hash,
                    system_user_data.full_name,
                    True,
                    datetime.now(),
                    datetime.now()
                ))
                
                conn.commit()
                
                # 创建系统用户的默认设置
                self._create_system_user_settings(conn)
                
                logger.info("系统用户创建成功")
                
                # 返回创建的系统用户
                return self.user_manager.get_user_by_id(self.SYSTEM_USER_ID)
                
        except Exception as e:
            logger.error(f"创建系统用户失败: {e}")
            return None
    
    def _create_system_user_settings(self, conn) -> None:
        """为系统用户创建默认设置"""
        try:
            conn.execute("""
                INSERT INTO user_settings (
                    user_id, risk_preference, analysis_depth, preferred_markets,
                    notification_enabled, auto_sync_enabled, theme_preference,
                    language_preference, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                self.SYSTEM_USER_ID,
                'moderate',
                'standard',
                'CN,US,HK',
                False,  # 系统用户不需要通知
                True,
                'light',
                'zh-CN',
                datetime.now(),
                datetime.now()
            ))
            
            logger.debug("系统用户设置创建成功")
            
        except Exception as e:
            logger.warning(f"创建系统用户设置失败: {e}")
    
    def get_system_user(self) -> Optional[User]:
        """获取系统用户"""
        return self.user_manager.get_user_by_id(self.SYSTEM_USER_ID)
    
    def is_system_user(self, user_id: int) -> bool:
        """检查是否为系统用户"""
        return user_id == self.SYSTEM_USER_ID
    
    def assign_data_to_system_user(self, table_name: str) -> Dict[str, Any]:
        """将现有数据分配给系统用户"""
        try:
            with self.user_manager.get_connection() as conn:
                # 检查表是否存在user_id字段
                cursor = conn.execute(f"PRAGMA table_info({table_name})")
                columns = [row[1] for row in cursor.fetchall()]
                
                if 'user_id' not in columns:
                    return {
                        "success": False,
                        "message": f"表 {table_name} 不包含 user_id 字段"
                    }
                
                # 统计需要更新的记录数
                cursor = conn.execute(f"SELECT COUNT(*) FROM {table_name} WHERE user_id IS NULL")
                null_count = cursor.fetchone()[0]
                
                if null_count == 0:
                    return {
                        "success": True,
                        "message": f"表 {table_name} 中没有需要分配的数据",
                        "affected_rows": 0
                    }
                
                # 将user_id为NULL的记录分配给系统用户
                cursor = conn.execute(f"""
                    UPDATE {table_name} 
                    SET user_id = ? 
                    WHERE user_id IS NULL
                """, (self.SYSTEM_USER_ID,))
                
                affected_rows = cursor.rowcount
                conn.commit()
                
                logger.info(f"表 {table_name} 数据分配完成: {affected_rows} 条记录")
                
                return {
                    "success": True,
                    "message": f"成功将 {affected_rows} 条记录分配给系统用户",
                    "affected_rows": affected_rows
                }
                
        except Exception as e:
            logger.error(f"分配数据到系统用户失败: {table_name}, 错误: {e}")
            return {
                "success": False,
                "message": f"分配数据失败: {e}",
                "affected_rows": 0
            }
    
    def assign_all_data_to_system_user(self) -> Dict[str, Any]:
        """将所有现有数据分配给系统用户"""
        tables_to_assign = [
            'stock_info',
            'cn_daily_data',
            'hk_daily_data', 
            'us_daily_data',
            'factor_data'
        ]
        
        results = {}
        total_affected = 0
        
        for table in tables_to_assign:
            result = self.assign_data_to_system_user(table)
            results[table] = result
            if result["success"]:
                total_affected += result["affected_rows"]
        
        # 汇总结果
        success_count = sum(1 for r in results.values() if r["success"])
        
        return {
            "success": success_count == len(tables_to_assign),
            "total_affected_rows": total_affected,
            "table_results": results,
            "summary": f"成功处理 {success_count}/{len(tables_to_assign)} 个表，共分配 {total_affected} 条记录"
        }
    
    def create_public_data_policy(self) -> Dict[str, Any]:
        """创建公共数据访问策略"""
        try:
            # 公共数据策略：user_id为NULL的数据对所有用户可见
            # 系统用户数据（user_id=-1）也对所有用户可见
            policy = {
                "public_data_rules": [
                    "user_id IS NULL",  # 完全公共的数据
                    f"user_id = {self.SYSTEM_USER_ID}"  # 系统用户数据
                ],
                "private_data_rules": [
                    "user_id = current_user_id"  # 用户私有数据
                ],
                "access_priority": [
                    "private_data",  # 优先显示用户私有数据
                    "public_data"    # 然后显示公共数据
                ]
            }
            
            logger.info("公共数据访问策略创建成功")
            return {
                "success": True,
                "policy": policy
            }
            
        except Exception as e:
            logger.error(f"创建公共数据访问策略失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def get_system_user_statistics(self) -> Dict[str, Any]:
        """获取系统用户数据统计"""
        try:
            with self.user_manager.get_connection() as conn:
                stats = {}
                
                # 统计各表中系统用户的数据量
                tables = ['stock_info', 'cn_daily_data', 'hk_daily_data', 'us_daily_data', 'factor_data']
                
                for table in tables:
                    try:
                        cursor = conn.execute(f"""
                            SELECT COUNT(*) FROM {table} WHERE user_id = ?
                        """, (self.SYSTEM_USER_ID,))
                        stats[f"{table}_count"] = cursor.fetchone()[0]
                    except Exception:
                        stats[f"{table}_count"] = 0
                
                # 统计公共数据量（user_id为NULL）
                for table in tables:
                    try:
                        cursor = conn.execute(f"""
                            SELECT COUNT(*) FROM {table} WHERE user_id IS NULL
                        """, )
                        stats[f"{table}_public_count"] = cursor.fetchone()[0]
                    except Exception:
                        stats[f"{table}_public_count"] = 0
                
                return {
                    "success": True,
                    "statistics": stats
                }
                
        except Exception as e:
            logger.error(f"获取系统用户统计失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

def create_system_user_manager(user_manager: UserManager) -> SystemUserManager:
    """创建系统用户管理器的工厂函数"""
    return SystemUserManager(user_manager) 