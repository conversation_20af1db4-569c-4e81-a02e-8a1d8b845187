"""
Password hashing and verification utilities using bcrypt directly.
"""

import bcrypt


def hash_password(password: str) -> str:
    """
    Hash a password using bcrypt.
    
    Args:
        password: Plain text password to hash
        
    Returns:
        Hashed password string (decoded from bytes)
    """
    # Convert password to bytes
    password_bytes = password.encode('utf-8')
    
    # Generate salt and hash password
    salt = bcrypt.gensalt()
    hashed_password = bcrypt.hashpw(password_bytes, salt)
    
    # Return as string for storage
    return hashed_password.decode('utf-8')


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash.
    
    Args:
        plain_password: Plain text password to verify
        hashed_password: Hashed password to verify against
        
    Returns:
        True if password matches, False otherwise
    """
    # Convert inputs to bytes
    password_bytes = plain_password.encode('utf-8')
    hashed_bytes = hashed_password.encode('utf-8')
    
    # Use bcrypt to verify
    return bcrypt.checkpw(password_bytes, hashed_bytes)


def needs_update(hashed_password: str) -> bool:
    """
    Check if a hashed password needs to be updated (rehashed).
    
    Args:
        hashed_password: Hashed password to check
        
    Returns:
        False - bcrypt hashes don't need updates unless algorithm changes
    """
    # bcrypt hashes are self-contained and don't need regular updates
    # This function is kept for backward compatibility
    return False 