"""
Authentication module for the financial investment assistant.
Provides user authentication, authorization, and session management.
"""

from .password_utils import hash_password, verify_password
from .jwt_utils import create_access_token, create_refresh_token, verify_token, get_current_user
from .user_manager import UserManager
from .auth_dependencies import get_current_active_user, get_current_user_dependency
from .auth_routes import router as auth_router
from .models import (
    UserCreate, UserLogin, UserUpdate, UserResponse, UserInDB,
    Token, TokenRefresh, PasswordChange, UserSession, UserPreferences
)

__all__ = [
    "hash_password",
    "verify_password", 
    "create_access_token",
    "create_refresh_token",
    "verify_token",
    "get_current_user",
    "UserManager",
    "get_current_active_user",
    "get_current_user_dependency",
    "auth_router",
    "UserCreate",
    "UserLogin", 
    "UserUpdate",
    "UserResponse",
    "UserInDB",
    "Token",
    "TokenRefresh",
    "PasswordChange",
    "UserSession",
    "UserPreferences"
] 