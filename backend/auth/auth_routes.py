"""
Authentication API routes for user registration, login, and management.
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import HTTPAuthorizationCredentials
from .models import (
    UserCreate, UserLogin, User<PERSON>p<PERSON>, UserResponse, 
    Token, TokenRefresh, PasswordChange
)
from .user_manager import UserManager
from .jwt_utils import (
    create_access_token, create_refresh_token, 
    refresh_access_token, ACCESS_TOKEN_EXPIRE_MINUTES
)
from .auth_dependencies import (
    get_current_active_user, get_user_manager, 
    get_current_user_dependency, security
)

# Create router
router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    request: Request,
    user_manager: UserManager = Depends(get_user_manager)
) -> UserResponse:
    """
    Register a new user account.
    
    Args:
        user_data: User registration data
        request: FastAPI request object for logging
        user_manager: User manager dependency
        
    Returns:
        Created user information
        
    Raises:
        HTTPException: If username or email already exists
    """
    try:
        # Create user
        user = user_manager.create_user(user_data)
        
        # Log registration with IP and user agent
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        user_manager.log_user_activity(
            user.id, 
            "USER_REGISTERED", 
            "User registered successfully",
            ip_address,
            user_agent
        )
        
        return user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user account"
        )


@router.post("/login", response_model=Token)
async def login_user(
    user_credentials: UserLogin,
    request: Request,
    user_manager: UserManager = Depends(get_user_manager)
) -> Token:
    """
    Authenticate user and return access and refresh tokens.
    
    Args:
        user_credentials: User login credentials
        request: FastAPI request object for logging
        user_manager: User manager dependency
        
    Returns:
        JWT tokens and expiration information
        
    Raises:
        HTTPException: If authentication fails
    """
    # Authenticate user
    user = user_manager.authenticate_user(
        user_credentials.username, 
        user_credentials.password
    )
    
    if not user:
        # Log failed login attempt
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        # Try to find user for logging (without password verification)
        existing_user = (
            user_manager.get_user_by_username(user_credentials.username) or 
            user_manager.get_user_by_email(user_credentials.username)
        )
        
        if existing_user:
            user_manager.log_user_activity(
                existing_user.id,
                "LOGIN_FAILED",
                "Failed login attempt - invalid password",
                ip_address,
                user_agent
            )
        
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create token data
    token_data = {
        "sub": str(user.id),
        "username": user.username,
        "email": user.email,
        "is_active": user.is_active
    }
    
    # Generate tokens
    access_token = create_access_token(token_data)
    refresh_token = create_refresh_token(token_data)
    
    # Log successful login with additional details
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("user-agent")
    user_manager.log_user_activity(
        user.id,
        "USER_LOGIN",
        "User logged in successfully",
        ip_address,
        user_agent
    )
    
    return Token(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60,  # Convert to seconds
        user=user
    )


@router.post("/refresh", response_model=Dict[str, str])
async def refresh_token(
    token_data: TokenRefresh,
    request: Request,
    user_manager: UserManager = Depends(get_user_manager)
) -> Dict[str, str]:
    """
    Refresh access token using refresh token.
    
    Args:
        token_data: Refresh token data
        request: FastAPI request object for logging
        user_manager: User manager dependency
        
    Returns:
        New access token
        
    Raises:
        HTTPException: If refresh token is invalid
    """
    try:
        # Generate new access token
        new_access_token = refresh_access_token(token_data.refresh_token)
        
        # Extract user info for logging (optional)
        from .jwt_utils import verify_token
        payload = verify_token(token_data.refresh_token, "refresh")
        user_id = payload.get("sub")
        
        if user_id:
            ip_address = request.client.host if request.client else None
            user_agent = request.headers.get("user-agent")
            user_manager.log_user_activity(
                int(user_id),
                "TOKEN_REFRESHED",
                "Access token refreshed",
                ip_address,
                user_agent
            )
        
        return {
            "access_token": new_access_token,
            "token_type": "bearer"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )


@router.post("/logout")
async def logout_user(
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    user_manager: UserManager = Depends(get_user_manager)
) -> Dict[str, str]:
    """
    Logout user (invalidate session).
    
    Note: With JWT tokens, logout is primarily handled on the client side
    by removing the tokens. This endpoint is for logging purposes.
    
    Args:
        request: FastAPI request object for logging
        current_user: Current authenticated user
        user_manager: User manager dependency
        
    Returns:
        Logout confirmation message
    """
    # Log logout
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("user-agent")
    user_manager.log_user_activity(
        current_user["id"],
        "USER_LOGOUT",
        "User logged out",
        ip_address,
        user_agent
    )
    
    return {"message": "Successfully logged out"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    user_manager: UserManager = Depends(get_user_manager)
) -> UserResponse:
    """
    Get current user information.
    
    Args:
        current_user: Current authenticated user
        user_manager: User manager dependency
        
    Returns:
        Current user information
        
    Raises:
        HTTPException: If user not found
    """
    user = user_manager.get_user_by_id(current_user["id"])
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    return user


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_data: UserUpdate,
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    user_manager: UserManager = Depends(get_user_manager)
) -> UserResponse:
    """
    Update current user information.
    
    Args:
        user_data: Updated user data
        request: FastAPI request object for logging
        current_user: Current authenticated user
        user_manager: User manager dependency
        
    Returns:
        Updated user information
        
    Raises:
        HTTPException: If update fails
    """
    try:
        updated_user = user_manager.update_user(current_user["id"], user_data)
        
        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Log user update with additional details
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        updated_fields = list(user_data.dict(exclude_unset=True).keys())
        user_manager.log_user_activity(
            current_user["id"],
            "USER_PROFILE_UPDATED",
            f"User profile updated: {updated_fields}",
            ip_address,
            user_agent
        )
        
        return updated_user
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user information"
        )


@router.put("/password")
async def change_password(
    password_data: PasswordChange,
    request: Request,
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    user_manager: UserManager = Depends(get_user_manager)
) -> Dict[str, str]:
    """
    Change user password.
    
    Args:
        password_data: Current and new password data
        request: FastAPI request object for logging
        current_user: Current authenticated user
        user_manager: User manager dependency
        
    Returns:
        Password change confirmation
        
    Raises:
        HTTPException: If password change fails
    """
    # Change password
    success = user_manager.change_password(
        current_user["id"],
        password_data.current_password,
        password_data.new_password
    )
    
    if not success:
        # Log failed password change
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        user_manager.log_user_activity(
            current_user["id"],
            "PASSWORD_CHANGE_FAILED",
            "Failed password change attempt - invalid current password",
            ip_address,
            user_agent
        )
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Log successful password change
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("user-agent")
    user_manager.log_user_activity(
        current_user["id"],
        "PASSWORD_CHANGED",
        "Password changed successfully",
        ip_address,
        user_agent
    )
    
    return {"message": "Password changed successfully"}


@router.get("/stats")
async def get_auth_stats(
    current_user: Dict[str, Any] = Depends(get_current_active_user),
    user_manager: UserManager = Depends(get_user_manager)
) -> Dict[str, Any]:
    """
    Get authentication system statistics.
    
    Note: This is a basic implementation. In production, you might want
    to add admin role checking.
    
    Args:
        current_user: Current authenticated user
        user_manager: User manager dependency
        
    Returns:
        Authentication system statistics
    """
    total_users = user_manager.get_user_count()
    active_users = user_manager.get_active_user_count()
    
    return {
        "total_users": total_users,
        "active_users": active_users,
        "inactive_users": total_users - active_users
    } 