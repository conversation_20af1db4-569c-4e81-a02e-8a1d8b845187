"""
Pydantic models for user authentication and management.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, EmailStr, Field


class UserBase(BaseModel):
    """Base user model with common fields."""
    username: str = Field(..., min_length=3, max_length=50, description="Username")
    email: EmailStr = Field(..., description="Email address")
    full_name: Optional[str] = Field(None, max_length=100, description="Full name")


class UserCreate(UserBase):
    """Model for user registration."""
    password: str = Field(..., min_length=8, max_length=100, description="Password")
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "johndo<PERSON>",
                "email": "<EMAIL>",
                "full_name": "<PERSON>",
                "password": "securepassword123"
            }
        }


class UserLogin(BaseModel):
    """Model for user login."""
    username: str = Field(..., description="Username or email")
    password: str = Field(..., description="Password")
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "johndoe",
                "password": "securepassword123"
            }
        }


class UserUpdate(BaseModel):
    """Model for updating user information."""
    email: Optional[EmailStr] = Field(None, description="Email address")
    full_name: Optional[str] = Field(None, max_length=100, description="Full name")
    is_active: Optional[bool] = Field(None, description="User active status")
    
    class Config:
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "full_name": "John Smith",
                "is_active": True
            }
        }


class UserResponse(UserBase):
    """Model for user information response."""
    id: int = Field(..., description="User ID")
    is_active: bool = Field(..., description="User active status")
    created_at: datetime = Field(..., description="Account creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "username": "johndoe",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "is_active": True,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
        }


class UserInDB(UserResponse):
    """Model for user data stored in database."""
    password_hash: str = Field(..., description="Hashed password")


class Token(BaseModel):
    """Model for authentication tokens."""
    access_token: str = Field(..., description="JWT access token")
    refresh_token: str = Field(..., description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: UserResponse = Field(..., description="User information")
    
    class Config:
        json_schema_extra = {
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
                "expires_in": 900,
                "user": {
                    "id": 1,
                    "username": "johndoe",
                    "email": "<EMAIL>",
                    "full_name": "John Doe",
                    "is_active": True,
                    "created_at": "2024-01-01T00:00:00Z",
                    "updated_at": "2024-01-01T00:00:00Z"
                }
            }
        }


class TokenRefresh(BaseModel):
    """Model for token refresh request."""
    refresh_token: str = Field(..., description="Refresh token")
    
    class Config:
        json_schema_extra = {
            "example": {
                "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            }
        }


class PasswordChange(BaseModel):
    """Model for password change request."""
    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, max_length=100, description="New password")
    
    class Config:
        json_schema_extra = {
            "example": {
                "current_password": "oldpassword123",
                "new_password": "newpassword456"
            }
        }


class UserSession(BaseModel):
    """Model for user session information."""
    user_id: int = Field(..., description="User ID")
    session_token: str = Field(..., description="Session token")
    created_at: datetime = Field(..., description="Session creation timestamp")
    expires_at: datetime = Field(..., description="Session expiration timestamp")
    is_active: bool = Field(default=True, description="Session active status")
    
    class Config:
        from_attributes = True


class UserPreferences(BaseModel):
    """Model for user preferences."""
    user_id: int = Field(..., description="User ID")
    theme: str = Field(default="system", description="UI theme preference")
    language: str = Field(default="zh", description="Language preference")
    timezone: str = Field(default="Asia/Shanghai", description="Timezone preference")
    notifications_enabled: bool = Field(default=True, description="Notifications enabled")
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "user_id": 1,
                "theme": "dark",
                "language": "zh",
                "timezone": "Asia/Shanghai",
                "notifications_enabled": True
            }
        } 