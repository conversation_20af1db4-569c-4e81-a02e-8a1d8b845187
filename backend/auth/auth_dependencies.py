"""
FastAPI dependency injection functions for authentication.
"""

from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from .jwt_utils import get_current_user
from .user_manager import UserManager

# HTTP Bearer token security scheme
security = HTTPBearer()


async def get_current_user_dependency(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> dict:
    """
    FastAPI dependency to get the current authenticated user.
    
    Args:
        credentials: HTTP Bearer token credentials
        
    Returns:
        Current user information
        
    Raises:
        HTTPException: If authentication fails
    """
    try:
        user_info = get_current_user(credentials.credentials)
        return user_info
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication failed",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_current_active_user(
    current_user: dict = Depends(get_current_user_dependency)
) -> dict:
    """
    FastAPI dependency to get the current active user.
    
    Args:
        current_user: Current user from token
        
    Returns:
        Current active user information
        
    Raises:
        HTTPException: If user is inactive
    """
    if not current_user.get("is_active", True):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Inactive user"
        )
    return current_user


async def get_user_manager() -> UserManager:
    """
    FastAPI dependency to get the user manager instance.
    
    Returns:
        UserManager instance
    """
    return UserManager()


async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)
) -> Optional[dict]:
    """
    FastAPI dependency to optionally get the current user.
    Returns None if no valid token is provided.
    
    Args:
        credentials: Optional HTTP Bearer token credentials
        
    Returns:
        Current user information or None
    """
    if not credentials:
        return None
        
    try:
        user_info = get_current_user(credentials.credentials)
        return user_info
    except HTTPException:
        return None
    except Exception:
        return None 