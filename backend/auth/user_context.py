#!/usr/bin/env python3
"""
用户上下文管理器
提供线程安全的用户上下文存储和管理功能
"""

import threading
from typing import Optional, Dict, Any
from contextlib import contextmanager
from functools import wraps
import logging

from .models import UserResponse as User

logger = logging.getLogger(__name__)

class UserContext:
    """用户上下文类，存储当前请求的用户信息"""
    
    def __init__(self, user: Optional[User] = None, user_id: Optional[int] = None, 
                 session_data: Optional[Dict[str, Any]] = None):
        self.user = user
        self.user_id = user_id or (user.id if user else None)
        self.session_data = session_data or {}
        self.is_authenticated = user is not None
        self.is_system_user = user_id == -1 if user_id else False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'user_id': self.user_id,
            'is_authenticated': self.is_authenticated,
            'is_system_user': self.is_system_user,
            'session_data': self.session_data
        }
    
    def __str__(self) -> str:
        return f"UserContext(user_id={self.user_id}, authenticated={self.is_authenticated})"

class UserContextManager:
    """用户上下文管理器，提供线程安全的上下文存储"""
    
    def __init__(self):
        self._local = threading.local()
        self._system_user_context = UserContext(user_id=-1)  # 系统用户上下文
    
    def set_context(self, context: UserContext) -> None:
        """设置当前线程的用户上下文"""
        self._local.context = context
        logger.debug(f"设置用户上下文: {context}")
    
    def get_context(self) -> Optional[UserContext]:
        """获取当前线程的用户上下文"""
        return getattr(self._local, 'context', None)
    
    def get_current_user_id(self) -> Optional[int]:
        """获取当前用户ID"""
        context = self.get_context()
        return context.user_id if context else None
    
    def get_system_context(self) -> UserContext:
        """获取系统用户上下文"""
        return self._system_user_context
    
    def is_authenticated(self) -> bool:
        """检查当前用户是否已认证"""
        context = self.get_context()
        return context.is_authenticated if context else False
    
    def is_system_user(self) -> bool:
        """检查当前是否为系统用户"""
        context = self.get_context()
        return context.is_system_user if context else False
    
    def clear_context(self) -> None:
        """清除当前线程的用户上下文"""
        if hasattr(self._local, 'context'):
            delattr(self._local, 'context')
        logger.debug("清除用户上下文")
    
    @contextmanager
    def context_scope(self, context: UserContext):
        """用户上下文作用域管理器"""
        old_context = self.get_context()
        try:
            self.set_context(context)
            yield context
        finally:
            if old_context:
                self.set_context(old_context)
            else:
                self.clear_context()

# 全局用户上下文管理器实例
user_context_manager = UserContextManager()

def with_user_context(func):
    """装饰器：自动注入用户上下文到函数参数"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        context = user_context_manager.get_context()
        if context is None:
            logger.warning(f"函数 {func.__name__} 调用时没有用户上下文")
            # 使用系统用户上下文作为默认值
            context = user_context_manager.get_system_context()
        
        # 如果函数签名中有 user_context 参数，则注入
        import inspect
        sig = inspect.signature(func)
        if 'user_context' in sig.parameters:
            kwargs['user_context'] = context
        elif 'user_id' in sig.parameters and context.user_id:
            kwargs['user_id'] = context.user_id
        
        return func(*args, **kwargs)
    return wrapper

def require_user_context(func):
    """装饰器：要求必须有用户上下文"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        context = user_context_manager.get_context()
        if context is None or not context.is_authenticated:
            raise ValueError("此操作需要用户认证")
        return func(*args, **kwargs)
    return wrapper

def get_current_user_context() -> Optional[UserContext]:
    """获取当前用户上下文的便捷函数"""
    return user_context_manager.get_context()

def get_current_user_id() -> Optional[int]:
    """获取当前用户ID的便捷函数"""
    return user_context_manager.get_current_user_id()

def set_user_context(user: Optional[User] = None, user_id: Optional[int] = None, 
                    session_data: Optional[Dict[str, Any]] = None) -> UserContext:
    """设置用户上下文的便捷函数"""
    context = UserContext(user=user, user_id=user_id, session_data=session_data)
    user_context_manager.set_context(context)
    return context

def clear_user_context() -> None:
    """清除用户上下文的便捷函数"""
    user_context_manager.clear_context() 