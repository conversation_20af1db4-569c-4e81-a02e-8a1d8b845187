"""
User data access layer with CRUD operations and authentication logic.
"""

import sqlite3
import os
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from fastapi import HTTPException, status
from .models import UserCreate, UserUpdate, UserInDB, UserResponse
from .password_utils import hash_password, verify_password


class UserManager:
    """User data access layer for managing user accounts."""
    
    def __init__(self, db_path: str = "data/users.db"):
        """
        Initialize the user manager.
        
        Args:
            db_path: Path to the SQLite database file
        """
        self.db_path = db_path
        self._ensure_db_directory()
        self._init_database()
    
    def _ensure_db_directory(self):
        """Ensure the database directory exists."""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir, exist_ok=True)
    
    def _init_database(self):
        """Initialize the database tables."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create users table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password_hash VARCHAR(255) NOT NULL,
                    full_name VARCHAR(100),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create user_sessions table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    session_token VARCHAR(255) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            
            # Create user_preferences table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_preferences (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER UNIQUE NOT NULL,
                    theme VARCHAR(20) DEFAULT 'system',
                    language VARCHAR(10) DEFAULT 'zh',
                    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
                    notifications_enabled BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            
            # Create user_activity_logs table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS user_activity_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    action VARCHAR(100) NOT NULL,
                    details TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
                )
            """)
            
            # Create indexes for better performance
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_sessions_token ON user_sessions(session_token)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_activity_user_id ON user_activity_logs(user_id)")
            
            conn.commit()
    
    def create_user(self, user_data: UserCreate) -> UserResponse:
        """
        Create a new user account.
        
        Args:
            user_data: User registration data
            
        Returns:
            Created user information
            
        Raises:
            HTTPException: If username or email already exists
        """
        # Check if username or email already exists
        if self.get_user_by_username(user_data.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already registered"
            )
        
        if self.get_user_by_email(user_data.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        
        # Hash the password
        password_hash = hash_password(user_data.password)
        
        # Insert user into database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO users (username, email, password_hash, full_name)
                VALUES (?, ?, ?, ?)
            """, (user_data.username, user_data.email, password_hash, user_data.full_name))
            
            user_id = cursor.lastrowid
            conn.commit()
        
        # Create default user preferences
        self._create_default_preferences(user_id)
        
        # Log user creation
        self.log_user_activity(user_id, "USER_CREATED", "User account created")
        
        # Return created user
        return self.get_user_by_id(user_id)
    
    def authenticate_user(self, username: str, password: str) -> Optional[UserInDB]:
        """
        Authenticate a user with username/email and password.
        
        Args:
            username: Username or email
            password: Plain text password
            
        Returns:
            User information if authentication successful, None otherwise
        """
        # Try to find user by username or email
        user = self.get_user_by_username(username) or self.get_user_by_email(username)
        
        if not user:
            return None
        
        # Verify password
        if not verify_password(password, user.password_hash):
            return None
        
        # Check if user is active
        if not user.is_active:
            return None
        
        # Log successful login
        self.log_user_activity(user.id, "USER_LOGIN", "User logged in successfully")
        
        return user
    
    def get_user_by_id(self, user_id: int) -> Optional[UserResponse]:
        """
        Get user by ID.
        
        Args:
            user_id: User ID
            
        Returns:
            User information or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, username, email, full_name, is_active, created_at, updated_at
                FROM users WHERE id = ?
            """, (user_id,))
            
            row = cursor.fetchone()
            if row:
                return UserResponse(**dict(row))
        
        return None
    
    def get_user_by_username(self, username: str) -> Optional[UserInDB]:
        """
        Get user by username.
        
        Args:
            username: Username
            
        Returns:
            User information or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM users WHERE username = ?
            """, (username,))
            
            row = cursor.fetchone()
            if row:
                return UserInDB(**dict(row))
        
        return None
    
    def get_user_by_email(self, email: str) -> Optional[UserInDB]:
        """
        Get user by email.
        
        Args:
            email: Email address
            
        Returns:
            User information or None if not found
        """
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM users WHERE email = ?
            """, (email,))
            
            row = cursor.fetchone()
            if row:
                return UserInDB(**dict(row))
        
        return None
    
    def update_user(self, user_id: int, user_data: UserUpdate) -> Optional[UserResponse]:
        """
        Update user information.
        
        Args:
            user_id: User ID
            user_data: Updated user data
            
        Returns:
            Updated user information or None if not found
        """
        # Check if user exists
        if not self.get_user_by_id(user_id):
            return None
        
        # Build update query dynamically
        update_fields = []
        values = []
        
        if user_data.email is not None:
            # Check if email is already taken by another user
            existing_user = self.get_user_by_email(user_data.email)
            if existing_user and existing_user.id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
            update_fields.append("email = ?")
            values.append(user_data.email)
        
        if user_data.full_name is not None:
            update_fields.append("full_name = ?")
            values.append(user_data.full_name)
        
        if user_data.is_active is not None:
            update_fields.append("is_active = ?")
            values.append(user_data.is_active)
        
        if not update_fields:
            return self.get_user_by_id(user_id)
        
        # Add updated_at timestamp
        update_fields.append("updated_at = ?")
        values.append(datetime.now(timezone.utc).isoformat())
        values.append(user_id)
        
        # Execute update
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(f"""
                UPDATE users SET {', '.join(update_fields)}
                WHERE id = ?
            """, values)
            conn.commit()
        
        # Log user update
        self.log_user_activity(user_id, "USER_UPDATED", f"User information updated: {list(user_data.dict(exclude_unset=True).keys())}")
        
        return self.get_user_by_id(user_id)
    
    def change_password(self, user_id: int, current_password: str, new_password: str) -> bool:
        """
        Change user password.
        
        Args:
            user_id: User ID
            current_password: Current password
            new_password: New password
            
        Returns:
            True if password changed successfully, False otherwise
        """
        # Get user with password hash
        user = self.get_user_by_username_with_password(user_id)
        if not user:
            return False
        
        # Verify current password
        if not verify_password(current_password, user.password_hash):
            return False
        
        # Hash new password
        new_password_hash = hash_password(new_password)
        
        # Update password in database
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE users SET password_hash = ?, updated_at = ?
                WHERE id = ?
            """, (new_password_hash, datetime.now(timezone.utc).isoformat(), user_id))
            conn.commit()
        
        # Log password change
        self.log_user_activity(user_id, "PASSWORD_CHANGED", "User password changed")
        
        return True
    
    def get_user_by_username_with_password(self, user_id: int) -> Optional[UserInDB]:
        """Get user with password hash by user ID."""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
            
            row = cursor.fetchone()
            if row:
                return UserInDB(**dict(row))
        
        return None
    
    def _create_default_preferences(self, user_id: int):
        """Create default preferences for a new user."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO user_preferences (user_id)
                VALUES (?)
            """, (user_id,))
            conn.commit()
    
    def log_user_activity(self, user_id: int, action: str, details: str = None, ip_address: str = None, user_agent: str = None):
        """
        Log user activity.
        
        Args:
            user_id: User ID
            action: Action performed
            details: Additional details
            ip_address: User's IP address
            user_agent: User's browser user agent
        """
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO user_activity_logs (user_id, action, details, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, action, details, ip_address, user_agent))
            conn.commit()
    
    def get_user_count(self) -> int:
        """Get total number of users."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users")
            return cursor.fetchone()[0]
    
    def get_active_user_count(self) -> int:
        """Get number of active users."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = TRUE")
            return cursor.fetchone()[0] 