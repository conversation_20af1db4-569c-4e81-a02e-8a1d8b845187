#!/usr/bin/env python3
"""
数据管理API接口
提供数据下载、存储、查询的RESTful接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel
from typing import List, Dict, Optional
import logging
import pandas as pd
from datetime import datetime

from backend.core.data.managers.data_manager import init_data_manager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/data", tags=["数据管理"])

# 请求模型
class DataInitRequest(BaseModel):
    tushare_token: Optional[str] = None  # 可选，用于备用数据源
    stock_codes: List[str]
    start_date: Optional[str] = None

class DataUpdateRequest(BaseModel):
    tushare_token: Optional[str] = None  # 可选，用于备用数据源
    stock_codes: List[str]

class StockDataRequest(BaseModel):
    tushare_token: Optional[str] = None  # 可选，用于备用数据源
    ts_code: str
    start_date: Optional[str] = None
    end_date: Optional[str] = None

class FactorDataRequest(BaseModel):
    tushare_token: Optional[str] = None  # 可选，用于备用数据源
    ts_code: str
    factor_names: Optional[List[str]] = None

# 全局变量保存任务状态
_task_status = {}

# 依赖项：获取DataManager实例
# 这里不直接提供get_data_manager，而是每次请求时创建或获取
# 为了简化，这里每次都初始化，实际生产中可以考虑缓存或更复杂的单例模式
async def get_data_manager_dependency(tushare_token: str):
    return init_data_manager(tushare_token)

@router.post("/init")
async def initialize_data(request: DataInitRequest, background_tasks: BackgroundTasks):
    """初始化数据（后台任务）"""
    try:
        # 初始化数据管理器
        data_manager = init_data_manager(request.tushare_token)
        
        # 生成任务ID
        task_id = f"init_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        _task_status[task_id] = {"status": "running", "message": "数据初始化中..."}
        
        # 添加后台任务
        background_tasks.add_task(
            _run_data_initialization,
            task_id,
            data_manager,
            request.stock_codes,
            request.start_date
        )
        
        return {
            "task_id": task_id,
            "message": "数据初始化任务已启动",
            "status": "running"
        }
        
    except Exception as e:
        logger.error(f"数据初始化失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据初始化失败: {str(e)}")

async def _run_data_initialization(task_id: str, data_manager, stock_codes: List[str], start_date: str = None):
    """运行数据初始化（后台任务）"""
    try:
        _task_status[task_id] = {"status": "running", "message": "正在下载股票数据..."}
        data_manager.initialize_data(stock_codes, start_date)
        _task_status[task_id] = {"status": "completed", "message": "数据初始化完成"}
    except Exception as e:
        logger.error(f"数据初始化任务失败 {task_id}: {e}")
        _task_status[task_id] = {"status": "failed", "message": f"初始化失败: {str(e)}"}

@router.get("/init/status/{task_id}")
async def get_init_status(task_id: str):
    """获取初始化任务状态"""
    if task_id not in _task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return _task_status[task_id]

@router.post("/update")
async def update_data(request: DataUpdateRequest, background_tasks: BackgroundTasks):
    """更新数据（后台任务）"""
    try:
        data_manager = init_data_manager(request.tushare_token)
        # if data_manager is None: # 这行不再需要，因为init_data_manager会始终返回实例
        #    raise HTTPException(status_code=400, detail="数据管理器未初始化，请先调用初始化接口")
        
        # 生成任务ID
        task_id = f"update_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        _task_status[task_id] = {"status": "running", "message": "数据更新中..."}
        
        # 添加后台任务
        background_tasks.add_task(
            _run_data_update,
            task_id,
            data_manager,
            request.stock_codes
        )
        
        return {
            "task_id": task_id,
            "message": "数据更新任务已启动",
            "status": "running"
        }
        
    except Exception as e:
        logger.error(f"数据更新失败: {e}")
        raise HTTPException(status_code=500, detail=f"数据更新失败: {str(e)}")

async def _run_data_update(task_id: str, data_manager, stock_codes: List[str]):
    """运行数据更新（后台任务）"""
    try:
        _task_status[task_id] = {"status": "running", "message": "正在更新股票数据..."}
        data_manager.update_data(stock_codes)
        _task_status[task_id] = {"status": "completed", "message": "数据更新完成"}
    except Exception as e:
        logger.error(f"数据更新任务失败 {task_id}: {e}")
        _task_status[task_id] = {"status": "failed", "message": f"更新失败: {str(e)}"}

@router.get("/update/status/{task_id}")
async def get_update_status(task_id: str):
    """获取更新任务状态"""
    if task_id not in _task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return _task_status[task_id]

@router.post("/stocks") # 将GET改为POST，以便接收token
async def get_available_stocks(tushare_token: Optional[str] = None):
    """获取可用股票列表"""
    try:
        data_manager = init_data_manager(tushare_token)
        
        stocks = data_manager.get_available_stocks()
        return {"stocks": stocks, "count": len(stocks)}
        
    except Exception as e:
        logger.error(f"获取股票列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")

@router.post("/stock/data")
async def get_stock_data(request: StockDataRequest):
    """获取股票OHLCV数据"""
    try:
        data_manager = init_data_manager(request.tushare_token)
        # if data_manager is None:
        #    raise HTTPException(status_code=400, detail="数据管理器未初始化")
        
        df = data_manager.get_stock_data(request.ts_code, request.start_date, request.end_date)
        
        if df.empty:
            return {"message": f"未找到 {request.ts_code} 的数据", "data": []}
        
        # 转换为API友好的格式
        data = []
        # 确保 df 包含所需的列，并且它们是可迭代的
        # 遍历DataFrame的每一行，将行索引作为日期
        # 确保日期列被转换为字符串格式
        for index, row in df.iterrows():
            # 安全的数值转换函数
            def safe_float(value, default=0.0):
                if pd.isna(value) or value is None:
                    return default
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return default
            
            def safe_int(value, default=0):
                if pd.isna(value) or value is None:
                    return default
                try:
                    return int(float(value))
                except (ValueError, TypeError):
                    return default
            
            data.append({
                "date": str(row['trade_date']), # 确保日期是字符串
                "open": safe_float(row['open']),
                "high": safe_float(row['high']),
                "low": safe_float(row['low']),
                "close": safe_float(row['close']),
                "volume": safe_int(row['volume']),
                "amount": safe_float(row['amount']),
                "pct_change": safe_float(row['pct_change']),
                "pe": safe_float(row['pe']) if not pd.isna(row['pe']) and row['pe'] is not None else None,
                "pb": safe_float(row['pb']) if not pd.isna(row['pb']) and row['pb'] is not None else None
            })
        
        return {
            "ts_code": request.ts_code,
            "count": len(data),
            "data": data
        }
        
    except Exception as e:
        logger.error(f"获取股票数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票数据失败: {str(e)}")

@router.post("/factors") # 将GET改为POST，以便接收token
async def get_factor_data(request: FactorDataRequest):
    """获取因子数据"""
    try:
        data_manager = init_data_manager(request.tushare_token)
        # if data_manager is None:
        #    raise HTTPException(status_code=400, detail="数据管理器未初始化")
        
        # 对于因子数据，我们通常获取某个最新日期的所有因子，或者指定日期的因子
        # 这里简化为获取最新日期的所有因子
        latest_trade_date = data_manager.get_latest_trade_date(request.ts_code)
        if not latest_trade_date:
            raise HTTPException(status_code=404, detail=f"未找到 {request.ts_code} 的最新交易日期数据")
        
        factors = data_manager.get_factor_data(request.ts_code, latest_trade_date, request.factor_names)
        
        if not factors:
            return {"message": f"未找到 {request.ts_code} 的因子数据", "factors": {}}
        
        return {"ts_code": request.ts_code, "trade_date": latest_trade_date, "factors": factors}
        
    except Exception as e:
        logger.error(f"获取因子数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取因子数据失败: {str(e)}")

@router.post("/factors/calculate")
async def calculate_and_save_factors(request: DataUpdateRequest, background_tasks: BackgroundTasks):
    """计算并保存因子（后台任务）"""
    try:
        data_manager = init_data_manager(request.tushare_token)
        # if data_manager is None:
        #    raise HTTPException(status_code=400, detail="数据管理器未初始化，请先调用初始化接口")

        task_id = f"calculate_factors_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        _task_status[task_id] = {"status": "running", "message": "因子计算中..."}

        background_tasks.add_task(
            _run_factor_calculation,
            task_id,
            data_manager,
            request.stock_codes
        )

        return {"task_id": task_id, "message": "因子计算任务已启动", "status": "running"}

    except Exception as e:
        logger.error(f"启动因子计算任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"启动因子计算任务失败: {str(e)}")

async def _run_factor_calculation(task_id: str, data_manager, stock_codes: List[str]):
    """后台运行因子计算并保存"""
    try:
        _task_status[task_id] = {"status": "running", "message": "正在计算并保存因子..."}
        for ts_code in stock_codes:
            # 获取最新的股票数据
            daily_df = data_manager.get_stock_data(ts_code) # 这里获取所有数据，以便因子计算
            if not daily_df.empty:
                # data_manager._calculate_and_save_factors(ts_code, daily_df) # 这个方法是data_manager内部调用，API不直接暴露
                # 由于_calculate_and_save_factors是私有方法且已在DataManager中被调用，这里只需确保数据获取和保存即可
                # 再次调用是因为可能是在更新数据后，需要重新计算所有因子
                all_factors = data_manager.factor_calculator.calculate_all_factors(daily_df) # 直接使用data_manager的factor_calculator
                if all_factors:
                    latest_trade_date = daily_df['trade_date'].iloc[-1]
                    data_manager.save_factor_data(ts_code, latest_trade_date, all_factors)
                else:
                    logger.warning(f"未为 {ts_code} 计算出任何因子")
            else:
                logger.warning(f"未获取到 {ts_code} 的股票数据，跳过因子计算")

        _task_status[task_id] = {"status": "completed", "message": "因子计算完成"}
    except Exception as e:
        logger.error(f"因子计算任务失败 {task_id}: {e}")
        _task_status[task_id] = {"status": "failed", "message": f"因子计算失败: {str(e)}"}

@router.get("/factors/status/{task_id}")
async def get_factor_calculation_status(task_id: str):
    """获取因子计算任务状态"""
    if task_id not in _task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return _task_status[task_id]

@router.post("/database/info") # 将GET改为POST，以便接收token
async def get_database_info(tushare_token: str):
    """获取数据库状态信息"""
    try:
        data_manager = init_data_manager(tushare_token)
        # if data_manager is None:
        #    raise HTTPException(status_code=400, detail="数据管理器未初始化")
        
        info = data_manager.get_database_info()
        return info
        
    except Exception as e:
        logger.error(f"获取数据库信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取数据库信息失败: {str(e)}")

@router.delete("/tasks/cleanup")
async def cleanup_completed_tasks():
    """清理已完成/失败的后台任务状态"""
    initial_count = len(_task_status)
    keys_to_delete = [task_id for task_id, status_info in _task_status.items()
                      if status_info["status"] in ["completed", "failed"]]
    
    for key in keys_to_delete:
        del _task_status[key]
    
    return {"message": f"已清理 {len(keys_to_delete)} 个任务状态", "initial_count": initial_count, "remaining_count": len(_task_status)}

@router.post("/stock/info/update")
async def update_stock_info(request: DataUpdateRequest):
    """批量更新股票基本信息（包括行业信息）"""
    try:
        data_manager = init_data_manager(request.tushare_token)
        
        updated_count = 0
        failed_count = 0
        results = []
        
        for ts_code in request.stock_codes:
            try:
                # 判断市场类型
                if ts_code.endswith('.HK'):
                    market_type = 'HK'
                elif ts_code.endswith(('.SZ', '.SH')):
                    market_type = 'CN'
                else:
                    market_type = 'US'
                
                # 更新股票信息
                data_manager._update_stock_info(ts_code, market_type)
                
                # 获取更新后的信息
                with data_manager.db_manager.get_connection() as conn:
                    cursor = conn.execute(
                        "SELECT ts_code, symbol, name, market, industry FROM stock_info WHERE ts_code = ?",
                        (ts_code,)
                    )
                    result = cursor.fetchone()
                    if result:
                        results.append({
                            'ts_code': result['ts_code'],
                            'symbol': result['symbol'],
                            'name': result['name'],
                            'market': result['market'],
                            'industry': result['industry'],
                            'status': 'updated'
                        })
                        updated_count += 1
                    else:
                        results.append({
                            'ts_code': ts_code,
                            'status': 'not_found'
                        })
                        failed_count += 1
                        
            except Exception as e:
                logger.error(f"更新股票 {ts_code} 信息失败: {e}")
                results.append({
                    'ts_code': ts_code,
                    'status': 'failed',
                    'error': str(e)
                })
                failed_count += 1
        
        return {
            'success': True,
            'message': f'批量更新完成: 成功 {updated_count} 个，失败 {failed_count} 个',
            'updated_count': updated_count,
            'failed_count': failed_count,
            'results': results
        }
        
    except Exception as e:
        logger.error(f"批量更新股票信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量更新股票信息失败: {str(e)}")

# 导入pandas用于数据处理
import pandas as pd 