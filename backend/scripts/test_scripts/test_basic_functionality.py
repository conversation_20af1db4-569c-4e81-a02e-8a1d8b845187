#!/usr/bin/env python3
"""
基础功能测试脚本
验证重构后的核心功能是否正常工作
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(level=logging.WARNING)  # 减少日志输出

def test_data_manager():
    """测试数据管理器"""
    try:
        from backend.core.data.managers.data_manager import init_data_manager
        
        # 初始化数据管理器
        data_manager = init_data_manager("")  # 使用空token进行测试
        
        # 测试基本方法
        assert hasattr(data_manager, 'get_stock_data')
        assert hasattr(data_manager, 'save_daily_data')
        assert hasattr(data_manager, 'get_available_stocks')
        
        print("✅ 数据管理器测试通过")
        return True
    except Exception as e:
        print(f"❌ 数据管理器测试失败: {e}")
        return False

def test_factor_calculator():
    """测试因子计算器"""
    try:
        from backend.core.analysis.factors.basic_factors import FactorCalculator
        import pandas as pd
        import numpy as np
        
        # 创建因子计算器
        calculator = FactorCalculator()
        
        # 创建模拟数据
        dates = pd.date_range('2023-01-01', periods=30, freq='D')
        mock_data = pd.DataFrame({
            'trade_date': dates.strftime('%Y%m%d'),
            'close': np.random.uniform(100, 200, 30),
            'open': np.random.uniform(100, 200, 30),
            'high': np.random.uniform(150, 250, 30),
            'low': np.random.uniform(50, 150, 30),
            'volume': np.random.randint(1000000, 10000000, 30),
            'pe': np.random.uniform(10, 30, 30),
            'pb': np.random.uniform(1, 5, 30),
        })
        
        # 测试因子计算
        factors = calculator.calculate_all_factors(mock_data)
        assert isinstance(factors, dict)
        assert len(factors) > 0
        
        print("✅ 因子计算器测试通过")
        return True
    except Exception as e:
        print(f"❌ 因子计算器测试失败: {e}")
        return False

def test_api_routers():
    """测试API路由器"""
    try:
        from backend.apis.data_api import router as data_router
        from backend.apis.factor_api import factor_router
        from backend.apis.metadata_api import router as metadata_router
        
        # 检查路由器类型
        from fastapi import APIRouter
        assert isinstance(data_router, APIRouter)
        assert isinstance(factor_router, APIRouter)
        assert isinstance(metadata_router, APIRouter)
        
        print("✅ API路由器测试通过")
        return True
    except Exception as e:
        print(f"❌ API路由器测试失败: {e}")
        return False

def test_scoring_system():
    """测试评分系统"""
    try:
        from backend.core.analysis.scoring.scoring_system import FactorScorer
        
        # 创建评分器
        scorer = FactorScorer()
        
        # 测试基本方法
        assert hasattr(scorer, 'normalize_factor')
        assert hasattr(scorer, 'calculate_factor_score')
        
        # 测试因子标准化
        score = scorer.normalize_factor('rsi', 30, 'technical')
        assert isinstance(score, float)
        assert 0 <= score <= 100
        
        print("✅ 评分系统测试通过")
        return True
    except Exception as e:
        print(f"❌ 评分系统测试失败: {e}")
        return False

def test_ml_models():
    """测试机器学习模型"""
    try:
        from backend.core.analysis.ml.models import MLModelManager
        
        # 创建模型管理器
        manager = MLModelManager()
        
        # 测试基本方法
        assert hasattr(manager, 'train_model')
        assert hasattr(manager, 'predict')
        assert hasattr(manager, 'get_model_info')
        
        print("✅ ML模型测试通过")
        return True
    except Exception as e:
        print(f"❌ ML模型测试失败: {e}")
        return False

def test_services():
    """测试服务模块"""
    try:
        from backend.services.news.news_impact_analyzer import NewsImpactAnalyzer
        from backend.services.market.index_components import get_index_stocks, get_all_index_names
        
        # 测试新闻分析器
        analyzer = NewsImpactAnalyzer()
        assert hasattr(analyzer, 'analyze_news_impact')
        
        # 测试指数成分股
        index_names = get_all_index_names()
        assert isinstance(index_names, list)
        assert len(index_names) > 0
        
        stocks = get_index_stocks('NASDAQ_100')
        assert isinstance(stocks, list)
        assert len(stocks) > 0
        
        print("✅ 服务模块测试通过")
        return True
    except Exception as e:
        print(f"❌ 服务模块测试失败: {e}")
        return False

def test_server_integration():
    """测试服务器集成"""
    try:
        from backend.server import app
        from fastapi import FastAPI
        
        # 检查应用类型
        assert isinstance(app, FastAPI)
        
        # 检查路由数量
        routes = [route.path for route in app.routes]
        assert len(routes) > 10  # 应该有很多路由
        
        # 检查关键路由
        key_routes = ["/", "/health", "/data", "/factors"]
        for route in key_routes:
            assert any(route in r for r in routes), f"缺少关键路由: {route}"
        
        print("✅ 服务器集成测试通过")
        return True
    except Exception as e:
        print(f"❌ 服务器集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始基础功能测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)
    
    tests = [
        ("数据管理器", test_data_manager),
        ("因子计算器", test_factor_calculator),
        ("API路由器", test_api_routers),
        ("评分系统", test_scoring_system),
        ("ML模型", test_ml_models),
        ("服务模块", test_services),
        ("服务器集成", test_server_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        try:
            if test_func():
                passed += 1
            else:
                print(f"  ⚠️  {test_name} 测试未通过")
        except Exception as e:
            print(f"  ❌ {test_name} 测试异常: {e}")
    
    print("\n" + "="*60)
    print("📋 测试结果总结")
    print("="*60)
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("🎉 所有基础功能测试通过！重构成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
