#!/usr/bin/env python3
"""
重构后系统功能验证测试脚本
验证所有核心模块和API接口是否正常工作
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_core_imports():
    """测试核心模块导入"""
    print("\n🔍 测试核心模块导入...")
    
    tests = [
        ("数据管理器", "backend.core.data.managers.data_manager", "init_data_manager"),
        ("因子计算器", "backend.core.analysis.factors.basic_factors", "FactorCalculator"),
        ("增强因子", "backend.core.analysis.factors.enhanced_factors", "FactorManager"),
        ("ML模型", "backend.core.analysis.ml.models", "MLModelManager"),
        ("评分系统", "backend.core.analysis.scoring.scoring_system", "FactorScorer"),
        ("风险管理", "backend.core.analysis.risk.risk_management", "get_risk_analyzer"),
        ("回测引擎", "backend.core.analysis.backtesting.backtesting", "get_backtest_engine"),
        ("技术分析", "backend.core.analysis.technical.divergence_detector", "get_divergence_detector"),
        ("数据源", "backend.core.data.sources.data_sources", "get_data_source_manager"),
    ]
    
    success_count = 0
    for name, module, attr in tests:
        try:
            mod = __import__(module, fromlist=[attr])
            getattr(mod, attr)
            print(f"  ✅ {name}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {name}: {e}")
    
    print(f"\n📊 核心模块导入结果: {success_count}/{len(tests)} 成功")
    return success_count == len(tests)

def test_api_imports():
    """测试API模块导入"""
    print("\n🔍 测试API模块导入...")
    
    tests = [
        ("数据API", "backend.apis.data_api", "router"),
        ("因子API", "backend.apis.factor_api", "factor_router"),
        ("元数据API", "backend.apis.metadata_api", "router"),
        ("API统一入口", "backend.apis", "data_router"),
    ]
    
    success_count = 0
    for name, module, attr in tests:
        try:
            mod = __import__(module, fromlist=[attr])
            getattr(mod, attr)
            print(f"  ✅ {name}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {name}: {e}")
    
    print(f"\n📊 API模块导入结果: {success_count}/{len(tests)} 成功")
    return success_count == len(tests)

def test_service_imports():
    """测试服务模块导入"""
    print("\n🔍 测试服务模块导入...")
    
    tests = [
        ("新闻服务", "backend.services.news.news_impact_analyzer", "NewsImpactAnalyzer"),
        ("市场服务", "backend.services.market.index_components", "get_index_stocks"),
        ("任务调度", "backend.services.tasks.news_sync_scheduler", "start_news_scheduler"),
        ("服务统一入口", "backend.services", "get_index_stocks"),
    ]
    
    success_count = 0
    for name, module, attr in tests:
        try:
            mod = __import__(module, fromlist=[attr])
            getattr(mod, attr)
            print(f"  ✅ {name}")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {name}: {e}")
    
    print(f"\n📊 服务模块导入结果: {success_count}/{len(tests)} 成功")
    return success_count == len(tests)

def test_server_startup():
    """测试服务器启动"""
    print("\n🔍 测试服务器启动...")
    
    try:
        from backend.server import app
        print("  ✅ 服务器模块导入成功")
        
        # 检查路由注册
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/health", "/data", "/factors", "/api/metadata"]
        
        found_routes = 0
        for expected in expected_routes:
            if any(expected in route for route in routes):
                found_routes += 1
                print(f"  ✅ 路由 {expected} 已注册")
            else:
                print(f"  ❌ 路由 {expected} 未找到")
        
        print(f"\n📊 路由注册结果: {found_routes}/{len(expected_routes)} 成功")
        return found_routes == len(expected_routes)
        
    except Exception as e:
        print(f"  ❌ 服务器启动失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("\n🔍 测试目录结构...")
    
    base_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    expected_dirs = [
        "apis",
        "core/data/managers",
        "core/data/sources", 
        "core/data/storage",
        "core/analysis/factors",
        "core/analysis/ml",
        "core/analysis/scoring",
        "core/analysis/risk",
        "core/analysis/technical",
        "core/analysis/backtesting",
        "services/news",
        "services/market",
        "services/tasks",
        "scripts",
        "scripts/test_scripts",
        "ai",
        "auth",
        "utils",
        "logs"
    ]
    
    success_count = 0
    for dir_path in expected_dirs:
        full_path = os.path.join(base_path, dir_path)
        if os.path.exists(full_path):
            print(f"  ✅ {dir_path}")
            success_count += 1
        else:
            print(f"  ❌ {dir_path}")
    
    print(f"\n📊 目录结构检查结果: {success_count}/{len(expected_dirs)} 成功")
    return success_count == len(expected_dirs)

def main():
    """主测试函数"""
    print("🚀 开始重构后系统功能验证测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行所有测试
    tests = [
        ("目录结构", test_directory_structure),
        ("核心模块导入", test_core_imports),
        ("API模块导入", test_api_imports),
        ("服务模块导入", test_service_imports),
        ("服务器启动", test_server_startup),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 执行测试: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    # 输出总结
    print(f"\n{'='*60}")
    print("📋 测试结果总结")
    print('='*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！重构成功！")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
