#!/usr/bin/env python3
"""
股票推荐系统初始化脚本
使用AkShare下载并初始化股票基本信息到数据库
"""

import os
import sys
import logging
from datetime import datetime

# 添加backend模块到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_manager import init_data_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def init_stock_recommendations():
    """初始化股票推荐系统数据"""
    
    logger.info("开始初始化股票推荐系统...")
    
    try:
        # 初始化数据管理器（不需要Tushare token）
        data_manager = init_data_manager()
        
        if not data_manager.akshare_data_manager:
            logger.error("AkShare数据管理器未初始化，请检查akshare库是否已安装")
            return False
        
        # 下载并保存A股股票列表
        logger.info("下载A股股票列表...")
        cn_stocks = data_manager.akshare_data_manager.download_stock_list('CN')
        
        if not cn_stocks.empty:
            data_manager.save_stock_info(cn_stocks)
            logger.info(f"成功保存{len(cn_stocks)}只A股基本信息")
        else:
            logger.warning("未获取到A股列表")
        
        # 下载并保存美股股票列表
        logger.info("下载美股股票列表...")
        try:
            us_stocks = data_manager.akshare_data_manager.download_stock_list('US')
            if not us_stocks.empty:
                data_manager.save_stock_info(us_stocks)
                logger.info(f"成功保存{len(us_stocks)}只美股基本信息")
            else:
                logger.warning("未获取到美股列表")
        except Exception as e:
            logger.warning(f"下载美股列表失败: {e}")
        
        # 下载并保存港股股票列表
        logger.info("下载港股股票列表...")
        try:
            hk_stocks = data_manager.akshare_data_manager.download_stock_list('HK')
            if not hk_stocks.empty:
                data_manager.save_stock_info(hk_stocks)
                logger.info(f"成功保存{len(hk_stocks)}只港股基本信息")
            else:
                logger.warning("未获取到港股列表")
        except Exception as e:
            logger.warning(f"下载港股列表失败: {e}")
        
        # 检查最终结果
        available_stocks = data_manager.get_available_stocks()
        logger.info(f"股票推荐系统初始化完成！数据库中共有{len(available_stocks)}只股票")
        
        if len(available_stocks) > 0:
            logger.info("📈 股票搜索推荐功能现在可以正常使用了！")
            logger.info(f"   - 股票数量: {len(available_stocks)}")
            logger.info(f"   - 数据源: AkShare")
            logger.info(f"   - 初始化时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            return True
        else:
            logger.error("初始化失败，数据库中没有股票信息")
            return False
            
    except Exception as e:
        logger.error(f"初始化过程中发生错误: {e}")
        return False

def check_recommendations_status():
    """检查股票推荐系统状态"""
    
    logger.info("检查股票推荐系统状态...")
    
    try:
        data_manager = init_data_manager()
        available_stocks = data_manager.get_available_stocks()
        
        if available_stocks:
            logger.info(f"✅ 股票推荐系统正常，数据库中有{len(available_stocks)}只股票")
            
            # 显示一些样本
            if len(available_stocks) >= 5:
                logger.info(f"   样本股票: {available_stocks[:5]}")
            else:
                logger.info(f"   所有股票: {available_stocks}")
                
            return True
        else:
            logger.warning("⚠️  数据库中没有股票数据，需要运行初始化")
            return False
            
    except Exception as e:
        logger.error(f"检查状态时发生错误: {e}")
        return False

def main():
    """主函数"""
    
    print("=" * 60)
    print("🚀 股票推荐系统初始化工具")
    print("=" * 60)
    
    # 首先检查当前状态
    if check_recommendations_status():
        print("\n📊 当前状态良好，如需重新初始化请选择选项 2")
        choice = input("\n选择操作 (1=检查状态, 2=重新初始化, q=退出): ").strip()
    else:
        print("\n❌ 需要初始化股票推荐数据")
        choice = input("\n选择操作 (1=检查状态, 2=初始化, q=退出): ").strip()
    
    if choice == '1':
        check_recommendations_status()
    elif choice == '2':
        print("\n开始初始化...")
        success = init_stock_recommendations()
        if success:
            print("\n✅ 初始化完成！现在可以使用股票搜索推荐功能了")
        else:
            print("\n❌ 初始化失败，请检查网络连接和akshare库")
    elif choice.lower() == 'q':
        print("退出")
    else:
        print("无效选择")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main() 