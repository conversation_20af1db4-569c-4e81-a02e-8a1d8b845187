#!/usr/bin/env python3
"""
股票元数据管理脚本
提供命令行界面来管理股票元数据
"""

import os
import sys
import argparse
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.core.data.managers.stock_metadata_manager import get_metadata_manager, cleanup_metadata_manager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def show_statistics(days=7):
    """显示统计信息"""
    try:
        metadata_manager = get_metadata_manager()
        
        print(f"\n📊 股票元数据统计信息 (最近{days}天)")
        print("=" * 50)
        
        # 市场统计
        market_stats = metadata_manager.get_stock_count_by_market()
        print("\n🏢 各市场股票数量:")
        total_stocks = 0
        for market, count in market_stats.items():
            print(f"   {market}: {count:,} 只")
            total_stocks += count
        print(f"   总计: {total_stocks:,} 只")
        
        # 更新统计
        update_stats = metadata_manager.get_update_statistics(days)
        if update_stats:
            print(f"\n🔄 更新统计 (最近{days}天):")
            for update_type, stats in update_stats.items():
                print(f"   {update_type}:")
                print(f"     - 更新次数: {stats['update_count']}")
                print(f"     - 成功数量: {stats['total_success']:,}")
                print(f"     - 失败数量: {stats['total_failed']:,}")
                print(f"     - 平均耗时: {stats['avg_duration']:.2f} 秒")
        else:
            print(f"\n🔄 最近{days}天无更新记录")
        
        # 缓存信息
        cached_stocks = metadata_manager.get_cached_stock_list()
        cache_timestamp = metadata_manager._cache_timestamp
        
        print(f"\n💾 缓存信息:")
        print(f"   缓存股票数量: {len(cached_stocks):,}")
        if cache_timestamp:
            cache_time = datetime.fromtimestamp(cache_timestamp)
            print(f"   缓存时间: {cache_time.strftime('%Y-%m-%d %H:%M:%S')}")
            cache_age = (datetime.now() - cache_time).total_seconds()
            print(f"   缓存年龄: {cache_age:.0f} 秒")
        else:
            print("   缓存状态: 未缓存")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取统计信息失败: {e}")
        return False

def update_market(market=None, force=False):
    """更新市场数据"""
    try:
        metadata_manager = get_metadata_manager()
        
        if market:
            print(f"\n🔄 开始更新{market}市场数据...")
            result = metadata_manager.force_update_market(market)
            
            if result['success']:
                print(f"✅ {market}市场更新成功:")
                print(f"   - 成功: {result['success_count']} 只")
                print(f"   - 失败: {result['failed_count']} 只")
                print(f"   - 耗时: {result['duration']:.2f} 秒")
            else:
                print(f"❌ {market}市场更新失败: {result['message']}")
        else:
            print("\n🔄 开始更新所有市场数据...")
            metadata_manager.scheduled_update_all_markets()
            print("✅ 全市场更新任务已启动")
        
        return True
        
    except Exception as e:
        print(f"❌ 更新市场数据失败: {e}")
        return False

def search_stocks(query, limit=10):
    """搜索股票"""
    try:
        metadata_manager = get_metadata_manager()
        stock_data = metadata_manager.get_cached_stock_list()
        
        if not stock_data:
            print("❌ 没有可搜索的股票数据，请先更新数据")
            return False
        
        query_upper = query.upper()
        matches = []
        
        for symbol, info in stock_data.items():
            if (query_upper in symbol.upper() or 
                query.lower() in info.get('name', '').lower() or
                query.lower() in info.get('industry', '').lower()):
                matches.append({
                    'symbol': symbol,
                    'name': info.get('name', 'N/A'),
                    'market': info.get('market', 'N/A'),
                    'industry': info.get('industry', 'N/A')
                })
                
                if len(matches) >= limit:
                    break
        
        print(f"\n🔍 搜索 '{query}' 的结果 (显示前{limit}个):")
        print("=" * 80)
        
        if matches:
            for i, match in enumerate(matches, 1):
                print(f"{i:2d}. {match['symbol']:10s} | {match['name']:30s} | {match['market']:8s} | {match['industry']}")
        else:
            print("   未找到匹配的股票")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索股票失败: {e}")
        return False

def clear_cache():
    """清除缓存"""
    try:
        metadata_manager = get_metadata_manager()
        
        with metadata_manager.lock:
            metadata_manager._cache = {}
            metadata_manager._cache_timestamp = None
        
        print("✅ 缓存已清除")
        return True
        
    except Exception as e:
        print(f"❌ 清除缓存失败: {e}")
        return False

def cleanup_old_data():
    """清理过期数据"""
    try:
        metadata_manager = get_metadata_manager()
        metadata_manager.cleanup_expired_data()
        print("✅ 过期数据清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 清理过期数据失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="股票元数据管理工具")
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 统计信息命令
    stats_parser = subparsers.add_parser('stats', help='显示统计信息')
    stats_parser.add_argument('--days', type=int, default=7, help='统计天数 (默认: 7)')
    
    # 更新命令
    update_parser = subparsers.add_parser('update', help='更新市场数据')
    update_parser.add_argument('--market', choices=['CN', 'HK', 'US'], help='指定市场 (CN/HK/US)')
    update_parser.add_argument('--force', action='store_true', help='强制更新')
    
    # 搜索命令
    search_parser = subparsers.add_parser('search', help='搜索股票')
    search_parser.add_argument('query', help='搜索关键词')
    search_parser.add_argument('--limit', type=int, default=10, help='结果数量限制 (默认: 10)')
    
    # 缓存管理命令
    subparsers.add_parser('clear-cache', help='清除缓存')
    subparsers.add_parser('cleanup', help='清理过期数据')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print("🚀 股票元数据管理工具")
    print("=" * 50)
    
    try:
        if args.command == 'stats':
            show_statistics(args.days)
        elif args.command == 'update':
            update_market(args.market, args.force)
        elif args.command == 'search':
            search_stocks(args.query, args.limit)
        elif args.command == 'clear-cache':
            clear_cache()
        elif args.command == 'cleanup':
            cleanup_old_data()
        else:
            print(f"❌ 未知命令: {args.command}")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        logger.error(f"执行失败: {e}")
    finally:
        # 清理资源
        cleanup_metadata_manager()

if __name__ == "__main__":
    main()
