#!/usr/bin/env python3
"""
迁移验证脚本
帮助团队成员验证重构迁移是否成功
"""

import os
import sys
import importlib
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def check_import(module_path, item_name, description):
    """检查模块导入是否成功"""
    try:
        module = importlib.import_module(module_path)
        if hasattr(module, item_name):
            print(f"✅ {description}")
            return True
        else:
            print(f"❌ {description} - 缺少 {item_name}")
            return False
    except ImportError as e:
        print(f"❌ {description} - 导入失败: {e}")
        return False
    except Exception as e:
        print(f"⚠️  {description} - 异常: {e}")
        return False

def check_file_exists(file_path, description):
    """检查文件是否存在"""
    if os.path.exists(file_path):
        print(f"✅ {description}")
        return True
    else:
        print(f"❌ {description} - 文件不存在: {file_path}")
        return False

def main():
    """主验证函数"""
    print("🔍 开始迁移验证...")
    print("=" * 60)
    
    checks = []
    
    # 检查API模块
    print("\n📡 检查API模块...")
    checks.append(check_import("backend.apis.data_api", "router", "数据API"))
    checks.append(check_import("backend.apis.factor_api", "factor_router", "因子API"))
    checks.append(check_import("backend.apis.metadata_api", "router", "元数据API"))
    
    # 检查核心数据模块
    print("\n💾 检查数据管理模块...")
    checks.append(check_import("backend.core.data.managers.data_manager", "init_data_manager", "数据管理器"))
    checks.append(check_import("backend.core.data.managers.stock_metadata_manager", "get_metadata_manager", "股票元数据管理"))
    checks.append(check_import("backend.core.data.managers.financial_news_manager", "get_financial_news_manager", "财经新闻管理"))
    checks.append(check_import("backend.core.data.sources.data_sources", "get_data_source_manager", "数据源管理"))
    
    # 检查分析模块
    print("\n📊 检查分析模块...")
    checks.append(check_import("backend.core.analysis.factors.basic_factors", "FactorCalculator", "基础因子计算"))
    checks.append(check_import("backend.core.analysis.factors.enhanced_factors", "FactorManager", "增强因子计算"))
    checks.append(check_import("backend.core.analysis.ml.models", "MLModelManager", "机器学习模型"))
    checks.append(check_import("backend.core.analysis.scoring.scoring_system", "FactorScorer", "评分系统"))
    checks.append(check_import("backend.core.analysis.risk.risk_management", "get_risk_analyzer", "风险管理"))
    checks.append(check_import("backend.core.analysis.backtesting.backtesting", "get_backtest_engine", "回测引擎"))
    checks.append(check_import("backend.core.analysis.technical.divergence_detector", "get_divergence_detector", "背离检测"))
    checks.append(check_import("backend.core.analysis.technical.market_scanner", "MarketScanner", "市场扫描"))
    
    # 检查服务模块
    print("\n🔧 检查服务模块...")
    checks.append(check_import("backend.services.news.news_impact_analyzer", "NewsImpactAnalyzer", "新闻影响分析"))
    checks.append(check_import("backend.services.market.index_components", "get_index_stocks", "指数成分股"))
    checks.append(check_import("backend.services.tasks.news_sync_scheduler", "start_news_scheduler", "新闻同步调度"))
    
    # 检查服务器模块
    print("\n🖥️  检查服务器模块...")
    checks.append(check_import("backend.server", "app", "FastAPI应用"))
    
    # 检查目录结构
    print("\n📁 检查目录结构...")
    base_path = project_root / "backend"
    
    directories = [
        "apis",
        "core/data/managers",
        "core/data/sources", 
        "core/data/storage",
        "core/analysis/factors",
        "core/analysis/ml",
        "core/analysis/scoring",
        "core/analysis/risk",
        "core/analysis/technical",
        "core/analysis/backtesting",
        "services/news",
        "services/market",
        "services/tasks",
        "scripts",
        "ai",
        "auth",
        "utils"
    ]
    
    for directory in directories:
        dir_path = base_path / directory
        checks.append(check_file_exists(str(dir_path), f"目录: {directory}"))
    
    # 检查关键文件
    print("\n📄 检查关键文件...")
    key_files = [
        "backend/server.py",
        "backend/apis/__init__.py",
        "backend/core/__init__.py",
        "backend/services/__init__.py",
        "backend/scripts/__init__.py"
    ]
    
    for file_path in key_files:
        full_path = project_root / file_path
        checks.append(check_file_exists(str(full_path), f"文件: {file_path}"))
    
    # 统计结果
    print("\n" + "=" * 60)
    print("📋 验证结果统计")
    print("=" * 60)
    
    passed = sum(checks)
    total = len(checks)
    
    print(f"✅ 通过: {passed}/{total}")
    print(f"❌ 失败: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 恭喜！迁移验证全部通过！")
        print("✨ 您的环境已成功适配新的目录结构")
        return True
    else:
        print(f"\n⚠️  发现 {total - passed} 个问题需要解决")
        print("📖 请参考迁移指南进行修复：docs/MIGRATION_GUIDE.md")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
