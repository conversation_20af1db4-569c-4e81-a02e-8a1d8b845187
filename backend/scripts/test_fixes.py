#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
验证数据库表结构和新闻影响分析功能是否正常工作
"""

import sys
import os
import sqlite3
import json
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_database_structure():
    """测试数据库表结构是否正确"""
    logger.info("开始测试数据库表结构...")
    
    db_path = "data/news_impact_analysis.db"
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='news_impact_analysis'
        """)
        
        if not cursor.fetchone():
            logger.error("news_impact_analysis表不存在")
            conn.close()
            return False
        
        # 检查必需的列是否存在
        cursor.execute("PRAGMA table_info(news_impact_analysis)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]
        
        required_columns = ['impact_score', 'impact_tags']
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            logger.error(f"缺少必需的列: {missing_columns}")
            conn.close()
            return False
        
        logger.info("数据库表结构检查通过")
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"数据库表结构检查失败: {e}")
        return False

def test_news_impact_analyzer():
    """测试新闻影响分析功能"""
    logger.info("开始测试新闻影响分析功能...")
    
    try:
        from backend.services.news.news_impact_analyzer import NewsImpactAnalyzer
        
        # 创建分析器实例
        analyzer = NewsImpactAnalyzer()
        
        # 测试数据
        test_news = {
            'title': '测试新闻标题：央行降准释放流动性',
            'content': '央行宣布降准0.5个百分点，释放长期资金约1万亿元，支持实体经济发展。',
            'source': '测试来源',
            'publish_time': '2025-06-18 10:00:00'
        }
        
        # 执行分析（这里只测试数据库保存功能，不实际调用AI模型）
        logger.info("测试数据库保存功能...")
        
        # 模拟分析结果
        mock_analysis = {
            'overall_impact': {'level': '高', 'confidence': 0.8},
            'us_market': {'impact_level': '中'},
            'a_share_market': {'impact_level': '高'},
            'hk_market': {'impact_level': '中'},
            'impact_score': 75.0,
            'impact_tags': ['货币政策', '流动性', '央行']
        }
        
        # 测试保存分析结果
        news_hash = analyzer._generate_news_hash(test_news['title'], test_news['content'])
        analysis_id = analyzer._save_analysis_result(
            news_hash, 
            test_news['title'], 
            test_news['content'],
            test_news['source'], 
            test_news['publish_time'],
            mock_analysis,
            mock_analysis['impact_score'],
            mock_analysis['impact_tags'],
            'test'
        )
        
        if analysis_id:
            logger.info(f"新闻影响分析保存成功，ID: {analysis_id}")
            
            # 测试查询功能
            recent_analyses = analyzer.get_recent_analyses(limit=1)
            if recent_analyses and len(recent_analyses) > 0:
                logger.info("新闻影响分析查询功能正常")
                
                # 检查impact_tags字段是否正确解析
                first_analysis = recent_analyses[0]
                if 'impact_tags' in first_analysis and isinstance(first_analysis['impact_tags'], list):
                    logger.info("impact_tags字段解析正常")
                    return True
                else:
                    logger.error("impact_tags字段解析异常")
                    return False
            else:
                logger.error("查询分析结果失败")
                return False
        else:
            logger.error("保存分析结果失败")
            return False
            
    except Exception as e:
        logger.error(f"新闻影响分析功能测试失败: {e}")
        return False

def test_supabase_sync_deduplication():
    """测试Supabase同步去重功能"""
    logger.info("开始测试Supabase同步去重功能...")
    
    try:
        from backend.services.tasks.supabase_news_sync import SupabaseNewsHandler

        # 创建同步器实例（不需要实际的Supabase连接）
        sync_service = SupabaseNewsHandler()
        
        # 测试数据（包含重复URL）
        test_data = [
            {
                'title': '新闻1',
                'content': '内容1',
                'url': 'https://example.com/news1',
                'source': '测试源'
            },
            {
                'title': '新闻2',
                'content': '内容2',
                'url': 'https://example.com/news2',
                'source': '测试源'
            },
            {
                'title': '新闻1重复',
                'content': '内容1重复',
                'url': 'https://example.com/news1',  # 重复URL
                'source': '测试源'
            },
            {
                'title': '新闻3',
                'content': '内容3',
                'url': 'https://example.com/news3',
                'source': '测试源'
            }
        ]
        
        # 测试去重功能
        unique_data = sync_service._deduplicate_news_data(test_data)
        
        if len(unique_data) == 3:  # 应该去除1条重复数据
            logger.info("Supabase同步去重功能正常")
            return True
        else:
            logger.error(f"去重功能异常，期望3条数据，实际{len(unique_data)}条")
            return False
            
    except Exception as e:
        logger.error(f"Supabase同步去重功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("开始验证修复效果...")
    
    tests = [
        ("数据库表结构", test_database_structure),
        ("新闻影响分析功能", test_news_impact_analyzer),
        ("Supabase同步去重功能", test_supabase_sync_deduplication)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            status = "通过" if result else "失败"
            logger.info(f"{test_name}: {status}")
        except Exception as e:
            results[test_name] = False
            logger.error(f"{test_name}: 异常 - {e}")
    
    # 汇总结果
    passed_tests = sum(1 for result in results.values() if result)
    total_tests = len(results)
    
    logger.info(f"\n测试结果汇总:")
    logger.info(f"总测试数: {total_tests}")
    logger.info(f"通过测试: {passed_tests}")
    logger.info(f"失败测试: {total_tests - passed_tests}")
    
    if passed_tests == total_tests:
        logger.info("所有测试通过，修复效果验证成功！")
        return True
    else:
        logger.error("部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
