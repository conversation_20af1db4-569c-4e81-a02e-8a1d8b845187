#!/usr/bin/env python3
"""
使用Tushare API更新股票数据库
"""

import os
import sys
import pandas as pd
import tushare as ts
from dotenv import load_dotenv
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.data_manager import init_data_manager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_tushare_token():
    """加载Tushare API token"""
    load_dotenv()
    token = os.getenv('TUSHARE_TOKEN')
    if not token:
        raise ValueError("未找到TUSHARE_TOKEN环境变量，请在.env文件中设置")
    return token

def get_a_stock_list(pro):
    """获取A股股票列表"""
    try:
        logger.info("正在获取A股股票列表...")
        df = pro.stock_basic(
            exchange='', 
            list_status='L', 
            fields='ts_code,symbol,name,area,industry,list_date'
        )
        
        # 标准化数据格式
        stock_list = pd.DataFrame({
            'ts_code': df['ts_code'],
            'symbol': df['symbol'],
            'name': df['name'],
            'market': 'CN',
            'industry': df['industry'].fillna('未知')
        })
        
        logger.info(f"成功获取 {len(stock_list)} 只A股")
        return stock_list
        
    except Exception as e:
        logger.error(f"获取A股列表失败: {e}")
        return pd.DataFrame()

def get_hk_stock_list(pro):
    """获取港股股票列表"""
    try:
        logger.info("正在获取港股股票列表...")
        df = pro.hk_basic(list_status='L')
        
        # 标准化数据格式
        stock_list = pd.DataFrame({
            'ts_code': df['ts_code'],
            'symbol': df['ts_code'],  # 港股使用ts_code作为symbol
            'name': df['name'],
            'market': 'HK',
            'industry': '未知'  # 港股基础信息中没有行业分类
        })
        
        logger.info(f"成功获取 {len(stock_list)} 只港股")
        return stock_list
        
    except Exception as e:
        logger.error(f"获取港股列表失败: {e}")
        return pd.DataFrame()

def get_us_stock_list(pro, limit=1000):
    """获取美股股票列表"""
    try:
        logger.info(f"正在获取美股股票列表(限制{limit}只)...")
        df = pro.us_basic(limit=limit)
        
        # 过滤掉没有名称的股票
        df = df.dropna(subset=['name'])
        
        # 标准化数据格式
        stock_list = pd.DataFrame({
            'ts_code': df['ts_code'],
            'symbol': df['ts_code'],  # 美股使用ts_code作为symbol
            'name': df['name'],
            'market': 'US',
            'industry': '未知'  # 美股基础信息中没有行业分类
        })
        
        logger.info(f"成功获取 {len(stock_list)} 只美股")
        return stock_list
        
    except Exception as e:
        logger.error(f"获取美股列表失败: {e}")
        return pd.DataFrame()

def update_stock_database():
    """更新股票数据库"""
    try:
        # 加载Tushare token
        token = load_tushare_token()
        logger.info(f"使用Tushare token: {token[:20]}...")
        
        # 初始化Tushare Pro API
        pro = ts.pro_api(token)
        
        # 初始化数据管理器
        data_manager = init_data_manager()
        
        # 获取各市场股票列表
        all_stocks = []
        
        # 获取A股
        a_stocks = get_a_stock_list(pro)
        if not a_stocks.empty:
            all_stocks.append(a_stocks)
        
        # 获取港股
        hk_stocks = get_hk_stock_list(pro)
        if not hk_stocks.empty:
            all_stocks.append(hk_stocks)
        
        # 获取美股（限制数量以避免超时）
        us_stocks = get_us_stock_list(pro, limit=500)
        if not us_stocks.empty:
            all_stocks.append(us_stocks)
        
        # 合并所有股票数据
        if all_stocks:
            combined_stocks = pd.concat(all_stocks, ignore_index=True)
            logger.info(f"总共获取到 {len(combined_stocks)} 只股票")
            
            # 保存到数据库
            data_manager.save_stock_info(combined_stocks)
            logger.info("✅ 股票数据库更新完成！")
            
            # 显示统计信息
            market_stats = combined_stocks['market'].value_counts()
            logger.info("市场分布:")
            for market, count in market_stats.items():
                logger.info(f"  {market}: {count} 只")
            
            return True
        else:
            logger.error("未获取到任何股票数据")
            return False
            
    except Exception as e:
        logger.error(f"更新股票数据库失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 使用Tushare API更新股票数据库")
    print("=" * 60)
    
    try:
        success = update_stock_database()
        
        if success:
            print("\n✅ 股票数据库更新成功！")
            print("现在股票搜索功能将拥有完整的股票数据库。")
        else:
            print("\n❌ 股票数据库更新失败！")
            print("请检查网络连接和Tushare API配置。")
            
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        
    print("=" * 60)

if __name__ == "__main__":
    main()
