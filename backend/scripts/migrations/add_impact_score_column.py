#!/usr/bin/env python3
# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import sqlite3
import logging
import os
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def add_missing_columns():
    """向news_impact_analysis表添加缺失的列（impact_score和impact_tags）"""

    # 数据库路径
    db_path = "data/news_impact_analysis.db"

    # 确保数据库文件存在
    if not os.path.exists(db_path):
        logger.error(f"数据库文件不存在: {db_path}")
        return False

    try:
        # 连接到数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 检查表是否存在
        cursor.execute("""
            SELECT name FROM sqlite_master
            WHERE type='table' AND name='news_impact_analysis'
        """)

        if not cursor.fetchone():
            logger.error("news_impact_analysis表不存在")
            conn.close()
            return False

        # 获取当前表结构
        cursor.execute("PRAGMA table_info(news_impact_analysis)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        success = True

        # 检查并添加impact_score列
        if 'impact_score' not in column_names:
            logger.info("正在添加impact_score列...")
            cursor.execute("""
                ALTER TABLE news_impact_analysis
                ADD COLUMN impact_score REAL DEFAULT 50.0
            """)
            logger.info("impact_score列添加成功")
        else:
            logger.info("impact_score列已存在，无需添加")

        # 检查并添加impact_tags列
        if 'impact_tags' not in column_names:
            logger.info("正在添加impact_tags列...")
            cursor.execute("""
                ALTER TABLE news_impact_analysis
                ADD COLUMN impact_tags TEXT DEFAULT '[]'
            """)
            logger.info("impact_tags列添加成功")
        else:
            logger.info("impact_tags列已存在，无需添加")

        # 提交更改
        conn.commit()

        # 验证列是否添加成功
        cursor.execute("PRAGMA table_info(news_impact_analysis)")
        columns = cursor.fetchall()
        column_names = [column[1] for column in columns]

        missing_columns = []
        if 'impact_score' not in column_names:
            missing_columns.append('impact_score')
        if 'impact_tags' not in column_names:
            missing_columns.append('impact_tags')

        if missing_columns:
            logger.error(f"验证失败：以下列未能添加到表中: {missing_columns}")
            success = False
        else:
            logger.info("验证成功：所有必需的列都已存在于表中")

        conn.close()
        return success

    except Exception as e:
        logger.error(f"添加列失败: {e}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    logger.info("开始执行数据库迁移：添加缺失的列")
    success = add_missing_columns()

    if success:
        logger.info("数据库迁移完成")
        exit(0)
    else:
        logger.error("数据库迁移失败")
        exit(1)