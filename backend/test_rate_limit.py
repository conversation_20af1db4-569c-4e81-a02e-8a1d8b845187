#!/usr/bin/env python3
"""
测试Gemini API速率限制功能

用于验证LLM管理器的速率限制是否正常工作
"""

import asyncio
import time
import sys
import os
from datetime import datetime

# 添加backend路径到sys.path
backend_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, backend_path)

from ai.llm import LLMManager

async def test_rate_limit():
    """测试速率限制功能"""
    print("🧪 开始测试Gemini API速率限制功能")
    print("=" * 50)
    
    # 初始化LLM管理器
    llm_manager = LLMManager()
    
    if llm_manager.mock_mode:
        print("⚠️ 警告：当前处于模拟模式，无法测试真实API速率限制")
        print("请确保设置了GEMINI_API_KEY环境变量")
        return
    
    print(f"✅ LLM管理器初始化成功")
    print(f"📋 配置信息:")
    print(f"   - 模型: {llm_manager.model}")
    print(f"   - 每分钟请求限制: {llm_manager.requests_per_minute}")
    print(f"   - 最小请求间隔: {llm_manager.min_request_interval}秒")
    print()
    
    # 测试连续请求
    test_prompts = [
        "简要介绍人工智能",
        "什么是机器学习？",
        "解释深度学习的基本概念",
        "金融科技的发展趋势如何？",
        "请分析区块链技术的应用前景"
    ]
    
    print(f"🔥 开始连续发送 {len(test_prompts)} 个请求...")
    print("观察速率限制是否生效...")
    print()
    
    start_time = time.time()
    
    for i, prompt in enumerate(test_prompts, 1):
        request_start = time.time()
        print(f"📤 发送请求 {i}/{len(test_prompts)}: {prompt[:30]}...")
        
        try:
            # 同步调用测试
            response = llm_manager.get_gemini_response(
                prompt=prompt,
                temperature=0.7,
                max_tokens=100
            )
            
            request_end = time.time()
            request_duration = request_end - request_start
            
            print(f"✅ 请求 {i} 完成，耗时: {request_duration:.2f}秒")
            print(f"📝 响应预览: {response[:50]}...")
            
            # 检查与上次请求的实际间隔
            if i > 1:
                actual_interval = request_start - last_request_start
                print(f"⏱️ 与上次请求间隔: {actual_interval:.2f}秒")
                
                if actual_interval >= llm_manager.min_request_interval - 0.5:  # 允许0.5秒误差
                    print("✅ 速率限制生效")
                else:
                    print("⚠️ 速率限制可能未生效")
            
            last_request_start = request_start
            print("-" * 40)
            
        except Exception as e:
            print(f"❌ 请求 {i} 失败: {e}")
            break
    
    total_time = time.time() - start_time
    print()
    print("📊 测试结果统计:")
    print(f"   - 总耗时: {total_time:.2f}秒")
    print(f"   - 平均每请求耗时: {total_time/len(test_prompts):.2f}秒")
    print(f"   - 理论最短总耗时: {(len(test_prompts)-1) * llm_manager.min_request_interval:.2f}秒")
    
    if total_time >= (len(test_prompts)-1) * llm_manager.min_request_interval - 2:  # 允许2秒误差
        print("✅ 速率限制功能正常工作")
    else:
        print("⚠️ 速率限制可能未完全生效，请检查实现")

async def test_async_rate_limit():
    """测试异步速率限制功能"""
    print("\n🧪 开始测试异步API速率限制功能")
    print("=" * 50)
    
    llm_manager = LLMManager()
    
    if llm_manager.mock_mode:
        print("⚠️ 警告：当前处于模拟模式，跳过异步测试")
        return
    
    test_prompts = [
        "什么是云计算？",
        "大数据分析的应用场景",
        "物联网技术的发展"
    ]
    
    print(f"🔥 开始异步发送 {len(test_prompts)} 个请求...")
    
    start_time = time.time()
    
    for i, prompt in enumerate(test_prompts, 1):
        request_start = time.time()
        print(f"📤 异步请求 {i}/{len(test_prompts)}: {prompt[:30]}...")
        
        try:
            response = await llm_manager.get_completion(
                prompt=prompt,
                temperature=0.7,
                max_tokens=100
            )
            
            request_end = time.time()
            request_duration = request_end - request_start
            
            print(f"✅ 异步请求 {i} 完成，耗时: {request_duration:.2f}秒")
            print(f"📝 响应预览: {response[:50]}...")
            print("-" * 40)
            
        except Exception as e:
            print(f"❌ 异步请求 {i} 失败: {e}")
            break
    
    total_time = time.time() - start_time
    print()
    print("📊 异步测试结果:")
    print(f"   - 总耗时: {total_time:.2f}秒")
    print(f"   - 平均每请求耗时: {total_time/len(test_prompts):.2f}秒")

def main():
    """主函数"""
    print(f"🚀 Gemini API速率限制测试")
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查环境变量
    if not os.getenv('GEMINI_API_KEY'):
        print("❌ 错误：未设置GEMINI_API_KEY环境变量")
        print("请设置后再运行测试")
        return
    
    try:
        # 运行测试
        asyncio.run(test_rate_limit())
        asyncio.run(test_async_rate_limit())
        
        print("\n" + "=" * 50)
        print("🎉 测试完成！")
        print("💡 建议:")
        print("   1. 如果速率限制正常工作，深度分析功能应该更稳定")
        print("   2. 用户在使用深度分析时会看到等待提示")
        print("   3. 可以考虑在高峰时段增加等待时间")
        
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    main() 