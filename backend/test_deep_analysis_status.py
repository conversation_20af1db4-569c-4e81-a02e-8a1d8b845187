#!/usr/bin/env python3
"""
测试深度分析状态输出功能
"""

import asyncio
import json
import logging
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

async def test_deep_analysis_status():
    """测试深度分析的详细状态输出"""
    
    try:
        from ai.deep_research.core.deep_research_engine import DeepResearchEngine
        
        # 创建测试新闻数据
        test_news = {
            "id": 999,
            "title": "苹果公司发布新款AI芯片，股价大涨5%",
            "content": "苹果公司今日宣布推出全新的AI专用芯片M4 Neural，该芯片在机器学习性能方面比前代产品提升30%。受此消息影响，苹果股价在盘后交易中上涨5%，市值增加约1000亿美元。",
            "source": "财经新闻",
            "publish_time": datetime.now().isoformat()
        }
        
        # 创建深度研究引擎
        engine = DeepResearchEngine()
        
        print("🚀 开始测试深度分析状态输出...")
        print(f"📰 测试新闻: {test_news['title']}")
        print("=" * 60)
        
        # 执行深度分析并收集状态信息
        status_history = []
        
        async for progress in engine.analyze_news_deep(
            news_data=test_news,
            analysis_config={
                'analysis_type': 'deep',
                'max_research_loops': 1,
                'priority': 'high'
            }
        ):
            # 记录状态
            status_history.append(progress)
            
            # 实时显示状态
            timestamp = progress.get('timestamp', '')
            msg_type = progress.get('type', 'unknown')
            message = progress.get('message', '')
            
            print(f"[{timestamp}] {msg_type}: {message}")
            
            # 如果是LLM相关状态，添加特殊标记
            if msg_type.startswith('llm_'):
                print(f"  🤖 LLM状态更新: {msg_type}")
            
            # 如果是完成状态，展示结果摘要
            if msg_type == 'analysis_completed':
                result = progress.get('result', {})
                if result:
                    print("\n" + "=" * 60)
                    print("📊 分析完成摘要:")
                    print(f"  任务ID: {result.get('task_id', 'N/A')}")
                    print(f"  查询数量: {len(result.get('queries_used', []))}")
                    print(f"  来源数量: {len(result.get('sources', []))}")
                    final_analysis = result.get('final_analysis', {})
                    if isinstance(final_analysis, dict):
                        analysis_text = final_analysis.get('analysis', '')
                        print(f"  分析长度: {len(analysis_text)} 字符")
                    print("=" * 60)
                break
        
        print(f"\n✅ 测试完成！共收集到 {len(status_history)} 条状态信息")
        
        # 统计状态类型
        status_types = {}
        for status in status_history:
            msg_type = status.get('type', 'unknown')
            status_types[msg_type] = status_types.get(msg_type, 0) + 1
        
        print("\n📈 状态类型统计:")
        for msg_type, count in sorted(status_types.items()):
            emoji = "🤖" if msg_type.startswith('llm_') else "📝"
            print(f"  {emoji} {msg_type}: {count} 次")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_status_callback_integration():
    """测试状态回调集成"""
    
    print("\n🔧 测试状态回调集成...")
    
    try:
        from ai.llm import LLMManager
        
        # 测试LLM管理器的详细日志
        llm = LLMManager()
        
        print(f"📍 LLM模式: {'模拟模式' if llm.mock_mode else '正常模式'}")
        print(f"📍 速率限制: {'启用' if llm.rate_limit_enabled else '禁用'}")
        print(f"📍 请求间隔: {llm.min_request_interval} 秒")
        
        # 测试简单的API调用
        print("\n🧪 测试LLM API调用...")
        
        test_prompt = "请简要分析苹果公司的投资价值"
        response = llm.get_gemini_response(test_prompt, temperature=0.3)
        
        print(f"✅ API调用成功，响应长度: {len(response)} 字符")
        print(f"📝 响应预览: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 状态回调测试失败: {e}")
        return False

if __name__ == "__main__":
    async def main():
        print("🧪 深度分析状态输出功能测试")
        print("=" * 60)
        
        # 测试1: 基本深度分析流程
        success1 = await test_deep_analysis_status()
        
        # 测试2: 状态回调集成
        success2 = await test_status_callback_integration()
        
        print("\n" + "=" * 60)
        if success1 and success2:
            print("🎉 所有测试通过！深度分析状态输出功能正常工作")
        else:
            print("⚠️ 部分测试失败，请检查日志输出")
        print("=" * 60)
    
    asyncio.run(main()) 