#!/usr/bin/env python3
"""
四层思维链分析功能全面测试
验证系统实现的完整性、稳定性和准确性
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class FourLayerAnalysisTest:
    """四层分析测试类"""
    
    def __init__(self):
        self.test_results = []
        self.start_time = None
        
    def log_test_result(self, test_name: str, status: str, message: str, details: Any = None):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "status": status,  # "PASS", "FAIL", "WARNING"
            "message": message,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        # 实时输出测试结果
        status_emoji = {
            "PASS": "✅",
            "FAIL": "❌", 
            "WARNING": "⚠️"
        }
        
        print(f"{status_emoji.get(status, '🔍')} {test_name}: {message}")
        if details:
            print(f"   详情: {details}")
    
    async def test_imports_and_dependencies(self):
        """测试1：导入和依赖关系"""
        test_name = "导入和依赖关系测试"
        
        try:
            # 测试核心模块导入
            from backend.ai.deep_research.core.deep_research_engine import DeepResearchEngine
            from backend.ai.deep_research.analyzers.four_layer_analyzer import (
                FourLayerThinkingAnalyzer, 
                FourLayerAnalysisResult,
                LayerAnalysisResult
            )
            from backend.ai.llm import LLMManager
            
            self.log_test_result(test_name, "PASS", "所有核心模块导入成功")
            return True
            
        except ImportError as e:
            self.log_test_result(test_name, "FAIL", f"模块导入失败: {e}")
            return False
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"未知错误: {e}")
            return False
    
    async def test_engine_initialization(self):
        """测试2：引擎初始化"""
        test_name = "深度研究引擎初始化测试"
        
        try:
            from backend.ai.deep_research.core.deep_research_engine import DeepResearchEngine
            
            # 创建引擎实例
            engine = DeepResearchEngine()
            
            # 验证关键组件
            assert hasattr(engine, 'gemini_adapter'), "缺少Gemini适配器"
            assert hasattr(engine, 'news_adapter'), "缺少新闻适配器"
            assert hasattr(engine, 'four_layer_analyzer'), "缺少四层分析器"
            assert hasattr(engine, 'data_manager'), "缺少数据管理器"
            
            self.log_test_result(test_name, "PASS", "引擎初始化成功，所有组件就绪")
            return engine
            
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"引擎初始化失败: {e}")
            return None
    
    async def test_analyzer_initialization(self):
        """测试3：四层分析器初始化"""
        test_name = "四层分析器初始化测试"
        
        try:
            from backend.ai.deep_research.analyzers.four_layer_analyzer import FourLayerThinkingAnalyzer
            from backend.ai.llm import LLMManager
            
            # 创建LLM管理器
            llm_manager = LLMManager()
            
            # 创建四层分析器
            analyzer = FourLayerThinkingAnalyzer(llm_manager)
            
            # 验证关键方法存在
            assert hasattr(analyzer, 'analyze_news_event'), "缺少新闻事件分析方法"
            assert hasattr(analyzer, '_analyze_layer1_event_perception'), "缺少第一层分析方法"
            assert hasattr(analyzer, '_analyze_layer2_supply_chain'), "缺少第二层分析方法"
            assert hasattr(analyzer, '_analyze_layer3_domestic_impact'), "缺少第三层分析方法"
            assert hasattr(analyzer, '_analyze_layer4_target_selection'), "缺少第四层分析方法"
            
            self.log_test_result(test_name, "PASS", "四层分析器初始化成功，所有分析方法就绪")
            return analyzer
            
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"四层分析器初始化失败: {e}")
            return None
    
    def get_test_news_data(self) -> List[Dict[str, Any]]:
        """获取测试新闻数据"""
        return [
            {
                "title": "印度航空宣布缩减宽体机国际航班数量",
                "content": """
                印度航空公司今日宣布，由于运营成本上升和市场需求变化，
                将在未来6个月内缩减20%的宽体机国际航班数量。
                该决定主要影响飞往欧洲和北美的长途航线。
                公司表示这是重组计划的一部分，旨在提高运营效率和盈利能力。
                此次调整可能影响相关供应链，包括航空燃料、机场服务、
                航空设备维护等多个环节。
                """,
                "source": "财联社",
                "publish_time": "2025-06-19 11:24:12",
                "category": "航空运输",
                "test_case": "复杂供应链影响分析"
            },
            {
                "title": "锂电池核心材料价格大幅上涨30%",
                "content": """
                由于主要产地供应紧张，锂电池核心材料碳酸锂价格
                本周大幅上涨30%，达到每吨45万元的新高。
                这一价格变化将直接影响新能源汽车、储能系统
                和消费电子产品的成本结构。
                """,
                "source": "证券时报",
                "publish_time": "2025-06-19 14:20:00",
                "category": "原材料",
                "test_case": "价格传导机制分析"
            },
            {
                "title": "美国对特定芯片技术实施新出口限制",
                "content": """
                美国商务部宣布对特定高端芯片技术实施新的出口限制，
                主要针对AI芯片和高性能计算芯片。
                这一政策变化可能重塑全球芯片供应链格局，
                影响多个国家的科技产业发展。
                """,
                "source": "新华社",
                "publish_time": "2025-06-19 16:45:00",
                "category": "科技政策",
                "test_case": "政策冲击影响分析"
            }
        ]
    
    async def test_news_analysis_workflow(self, engine):
        """测试4：新闻分析工作流"""
        test_name = "新闻分析工作流测试"
        
        if not engine:
            self.log_test_result(test_name, "FAIL", "引擎未初始化，跳过测试")
            return False
        
        test_news_list = self.get_test_news_data()
        
        for i, news_data in enumerate(test_news_list):
            case_name = f"{test_name} - 案例{i+1}: {news_data['test_case']}"
            
            try:
                print(f"\n🔍 开始测试案例 {i+1}: {news_data['test_case']}")
                print(f"📰 新闻标题: {news_data['title']}")
                print("-" * 80)
                
                # 记录分析开始时间
                analysis_start = time.time()
                
                # 配置四层分析
                analysis_config = {
                    "use_four_layer_analysis": True,
                    "max_research_loops": 1,
                    "initial_search_query_count": 2,  # 减少查询数量以加快测试
                    "enable_detailed_status": True
                }
                
                # 执行分析
                analysis_steps = []
                error_occurred = False
                
                async for result in engine.analyze_news_deep(
                    news_data=news_data,
                    analysis_config=analysis_config
                ):
                    result_type = result.get("type", "unknown")
                    message = result.get("message", "")
                    
                    analysis_steps.append({
                        "type": result_type,
                        "message": message,
                        "timestamp": result.get("timestamp")
                    })
                    
                    # 实时输出关键步骤
                    if result_type in [
                        "task_started", "context_completed", "queries_generated",
                        "web_research_started", "four_layer_analysis_started",
                        "four_layer_completed", "analysis_completed"
                    ]:
                        print(f"   ✅ {message}")
                        
                        # 显示生成的查询
                        if result_type == "queries_generated":
                            queries = result.get("queries", [])
                            print(f"      生成查询数量: {len(queries)}")
                            for j, query in enumerate(queries):
                                print(f"      查询{j+1}: {query.get('query', '')}")
                        
                        # 显示四层分析结果
                        elif result_type == "four_layer_completed":
                            targets = result.get("investment_targets", [])
                            confidence = result.get("confidence", 0)
                            print(f"      推荐标的数量: {len(targets)}")
                            print(f"      整体置信度: {confidence:.1%}")
                            
                            # 显示前3个推荐标的
                            for j, target in enumerate(targets[:3]):
                                print(f"      标的{j+1}: {target.get('name', '')} "
                                      f"(置信度: {target.get('confidence', 0):.1%})")
                    
                    elif result_type == "error":
                        print(f"   ❌ {message}")
                        error_occurred = True
                        break
                    
                    elif result_type == "analysis_completed":
                        analysis_time = time.time() - analysis_start
                        
                        print(f"   🎉 分析完成，耗时: {analysis_time:.1f}秒")
                        
                        # 验证结果完整性
                        complete_result = result.get("result", {})
                        self._validate_analysis_result(complete_result, case_name)
                        break
                
                if not error_occurred:
                    self.log_test_result(
                        case_name, 
                        "PASS", 
                        f"工作流执行成功，共{len(analysis_steps)}个步骤",
                        {
                            "analysis_time": f"{time.time() - analysis_start:.1f}秒",
                            "steps_count": len(analysis_steps),
                            "news_category": news_data['category']
                        }
                    )
                else:
                    self.log_test_result(case_name, "FAIL", "工作流执行中发生错误")
                
            except Exception as e:
                self.log_test_result(case_name, "FAIL", f"测试执行异常: {e}")
                import traceback
                traceback.print_exc()
        
        return True
    
    def _validate_analysis_result(self, result: Dict[str, Any], test_case: str):
        """验证分析结果的完整性"""
        validation_name = f"{test_case} - 结果验证"
        
        try:
            # 检查必需字段
            required_fields = ['task_id', 'research_topic', 'analysis_type', 'completed_at']
            missing_fields = []
            
            for field in required_fields:
                if field not in result:
                    missing_fields.append(field)
            
            if missing_fields:
                self.log_test_result(
                    validation_name, 
                    "WARNING", 
                    f"缺少必需字段: {missing_fields}"
                )
            else:
                self.log_test_result(validation_name, "PASS", "结果结构完整")
            
            # 检查内容质量
            queries_used = result.get('queries_used', [])
            research_results = result.get('research_results', [])
            
            quality_checks = {
                "查询生成": len(queries_used) > 0,
                "研究结果": len(research_results) > 0,
                "分析类型": result.get('analysis_type') == 'four_layer_thinking',
                "任务完成": 'completed_at' in result
            }
            
            passed_checks = sum(quality_checks.values())
            total_checks = len(quality_checks)
            
            if passed_checks == total_checks:
                self.log_test_result(
                    f"{validation_name} - 质量检查", 
                    "PASS", 
                    f"所有质量检查通过 ({passed_checks}/{total_checks})"
                )
            else:
                failed_checks = [k for k, v in quality_checks.items() if not v]
                self.log_test_result(
                    f"{validation_name} - 质量检查", 
                    "WARNING", 
                    f"部分质量检查失败: {failed_checks}"
                )
                
        except Exception as e:
            self.log_test_result(validation_name, "FAIL", f"验证过程异常: {e}")
    
    async def test_error_handling(self, engine):
        """测试5：错误处理机制"""
        test_name = "错误处理机制测试"
        
        if not engine:
            self.log_test_result(test_name, "FAIL", "引擎未初始化，跳过测试")
            return False
        
        # 测试无效输入
        invalid_inputs = [
            {"title": "", "content": "", "test_case": "空标题和内容"},
            {"title": "A" * 1000, "content": "B" * 10000, "test_case": "超长文本"},
            {"invalid_field": "test", "test_case": "缺少必需字段"}
        ]
        
        for i, invalid_input in enumerate(invalid_inputs):
            case_name = f"{test_name} - {invalid_input['test_case']}"
            
            try:
                print(f"\n🧪 测试错误处理案例 {i+1}: {invalid_input['test_case']}")
                
                # 设置超时
                analysis_task = asyncio.create_task(
                    self._run_analysis_with_timeout(engine, invalid_input, 30)
                )
                
                result = await analysis_task
                
                if result["success"]:
                    self.log_test_result(
                        case_name, 
                        "PASS", 
                        "系统正确处理了无效输入，产生了合理的默认结果"
                    )
                else:
                    self.log_test_result(
                        case_name, 
                        "PASS", 
                        f"系统正确抛出错误: {result['error']}"
                    )
                
            except asyncio.TimeoutError:
                self.log_test_result(case_name, "WARNING", "分析超时（30秒），但未崩溃")
            except Exception as e:
                self.log_test_result(case_name, "PASS", f"系统正确捕获异常: {e}")
        
        return True
    
    async def _run_analysis_with_timeout(self, engine, news_data, timeout_seconds):
        """带超时的分析执行"""
        try:
            async for result in engine.analyze_news_deep(
                news_data=news_data,
                analysis_config={"use_four_layer_analysis": True}
            ):
                if result.get("type") == "analysis_completed":
                    return {"success": True, "result": result}
                elif result.get("type") == "error":
                    return {"success": False, "error": result.get("message")}
            
            return {"success": True, "result": "completed"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def test_performance_metrics(self, engine):
        """测试6：性能指标测试"""
        test_name = "性能指标测试"
        
        if not engine:
            self.log_test_result(test_name, "FAIL", "引擎未初始化，跳过测试")
            return False
        
        # 使用简单的测试数据
        simple_news = {
            "title": "测试新闻：某公司股价上涨5%",
            "content": "某知名公司今日股价上涨5%，市场反应积极。",
            "source": "测试源",
            "publish_time": "2025-06-19 12:00:00",
            "category": "股市"
        }
        
        try:
            print(f"\n📊 开始性能测试...")
            
            start_time = time.time()
            
            step_times = {}
            last_step_time = start_time
            
            async for result in engine.analyze_news_deep(
                news_data=simple_news,
                analysis_config={
                    "use_four_layer_analysis": True,
                    "max_research_loops": 1,
                    "initial_search_query_count": 1  # 最小查询数
                }
            ):
                result_type = result.get("type", "unknown")
                current_time = time.time()
                
                # 记录关键步骤的耗时
                if result_type in [
                    "queries_generated", "web_research_started", 
                    "four_layer_completed", "analysis_completed"
                ]:
                    step_times[result_type] = current_time - last_step_time
                    last_step_time = current_time
                
                if result_type == "analysis_completed":
                    break
            
            total_time = time.time() - start_time
            
            # 性能评估
            performance_metrics = {
                "总耗时": f"{total_time:.1f}秒",
                "查询生成耗时": f"{step_times.get('queries_generated', 0):.1f}秒",
                "网络搜索耗时": f"{step_times.get('web_research_started', 0):.1f}秒",
                "四层分析耗时": f"{step_times.get('four_layer_completed', 0):.1f}秒"
            }
            
            # 性能判断
            if total_time < 120:  # 2分钟内完成
                status = "PASS"
                message = f"性能良好，总耗时 {total_time:.1f}秒"
            elif total_time < 300:  # 5分钟内完成
                status = "WARNING"
                message = f"性能可接受，总耗时 {total_time:.1f}秒"
            else:
                status = "FAIL"
                message = f"性能较差，总耗时 {total_time:.1f}秒"
            
            self.log_test_result(test_name, status, message, performance_metrics)
            
            return True
            
        except Exception as e:
            self.log_test_result(test_name, "FAIL", f"性能测试异常: {e}")
            return False
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "="*80)
        print("📋 四层思维链分析功能测试报告")
        print("="*80)
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.test_results if r["status"] == "FAIL"])
        warning_tests = len([r for r in self.test_results if r["status"] == "WARNING"])
        
        print(f"\n📊 测试统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   ✅ 通过: {passed_tests}")
        print(f"   ❌ 失败: {failed_tests}")
        print(f"   ⚠️  警告: {warning_tests}")
        print(f"   🎯 成功率: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "   🎯 成功率: 0%")
        
        # 详细结果
        print(f"\n📝 详细测试结果:")
        for result in self.test_results:
            status_emoji = {"PASS": "✅", "FAIL": "❌", "WARNING": "⚠️"}
            emoji = status_emoji.get(result["status"], "🔍")
            
            print(f"   {emoji} {result['test_name']}")
            print(f"      {result['message']}")
            
            if result.get("details"):
                if isinstance(result["details"], dict):
                    for key, value in result["details"].items():
                        print(f"      {key}: {value}")
                else:
                    print(f"      详情: {result['details']}")
        
        # 总结
        print(f"\n🏁 测试总结:")
        if failed_tests == 0:
            if warning_tests == 0:
                print("   🎉 所有测试完美通过！四层思维链分析功能完全正常。")
            else:
                print("   ✅ 核心功能正常，存在少量警告需要关注。")
        else:
            print("   ⚠️  存在功能问题，需要修复后重新测试。")
        
        # 保存报告到文件
        report_data = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "warning_tests": warning_tests,
                "success_rate": (passed_tests/total_tests*100) if total_tests > 0 else 0,
                "test_time": datetime.now().isoformat()
            },
            "detailed_results": self.test_results
        }
        
        report_filename = f"four_layer_analysis_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_filename, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, ensure_ascii=False, indent=2)
            print(f"\n💾 测试报告已保存: {report_filename}")
        except Exception as e:
            print(f"\n❌ 保存测试报告失败: {e}")
        
        return failed_tests == 0

async def main():
    """主测试函数"""
    print("🚀 开始四层思维链分析功能全面测试")
    print("="*80)
    
    tester = FourLayerAnalysisTest()
    tester.start_time = time.time()
    
    # 执行所有测试
    tests_to_run = [
        ("导入和依赖关系", tester.test_imports_and_dependencies()),
        ("引擎初始化", tester.test_engine_initialization()),
        ("分析器初始化", tester.test_analyzer_initialization()),
    ]
    
    # 执行基础测试
    engine = None
    analyzer = None
    
    for test_name, test_coro in tests_to_run:
        print(f"\n🔍 执行测试: {test_name}")
        result = await test_coro
        
        if test_name == "引擎初始化":
            engine = result
        elif test_name == "分析器初始化":
            analyzer = result
    
    # 如果基础测试通过，执行高级测试
    if engine and analyzer:
        print(f"\n🔍 执行高级功能测试...")
        
        await tester.test_news_analysis_workflow(engine)
        await tester.test_error_handling(engine)
        await tester.test_performance_metrics(engine)
    else:
        print(f"\n⚠️  基础测试失败，跳过高级功能测试")
    
    # 生成最终报告
    total_time = time.time() - tester.start_time
    print(f"\n⏱️  总测试时间: {total_time:.1f}秒")
    
    success = tester.generate_test_report()
    
    if success:
        print(f"\n🎉 四层思维链分析功能测试全部通过！")
        sys.exit(0)
    else:
        print(f"\n❌ 四层思维链分析功能存在问题，请检查并修复。")
        sys.exit(1)

if __name__ == "__main__":
    # 设置事件循环策略（适用于某些系统）
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    asyncio.run(main()) 