#!/usr/bin/env python3
"""
AI工作流管理器
集成到backend的智能分析系统
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, AsyncGenerator
import json
from datetime import datetime

from langchain_core.messages import HumanMessage
from .graph import build_graph
from .agent_manager import Agent<PERSON>anager
from .llm import LLMManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,  # Default level is INFO
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)

def enable_debug_logging():
    """Enable debug level logging for more detailed execution information."""
    logging.getLogger("backend.ai").setLevel(logging.DEBUG)

logger = logging.getLogger(__name__)

class AIWorkflowManager:
    """AI工作流管理器 - 集成多智能体系统"""
    
    def __init__(self, data_manager=None):
        self.data_manager = data_manager
        self.llm_manager = LLMManager()
        self.agent_manager = AgentManager()
        self.graph = None
        self._initialize_graph()
        logger.info("AI工作流管理器初始化完成")
    
    def _initialize_graph(self):
        """初始化LangGraph工作流图"""
        try:
            self.graph = build_graph()
            logger.info("LangGraph工作流图初始化成功")
        except Exception as e:
            logger.error(f"LangGraph工作流图初始化失败: {e}")
            # 如果图初始化失败，回退到简单模式
            self.graph = None

    async def process_user_query(self, user_input: str, user_id: Optional[int] = None, **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """
        处理用户查询 - 主要入口点
        保持与原有接口的兼容性，支持用户隔离
        
        Args:
            user_input: 用户输入
            user_id: 用户ID，用于数据隔离
            **kwargs: 其他参数
            
        Yields:
            Dict[str, Any]: 流式返回处理结果
        """
        
        if not user_input or not user_input.strip():
            yield {
                "type": "error",
                "content": "输入不能为空",
                "timestamp": datetime.now().isoformat()
            }
            return
        
        try:
            # 1. 首先尝试使用多智能体工作流
            if self.graph:
                logger.info(f"使用多智能体工作流处理用户查询 (用户ID: {user_id})")
                async for result in self._run_agent_workflow(user_input, user_id=user_id, **kwargs):
                    yield result
            else:
                # 2. 回退到简单的智能体管理器
                logger.info(f"使用简单智能体管理器处理用户查询 (用户ID: {user_id})")
                async for result in self._run_simple_workflow(user_input, user_id=user_id, **kwargs):
                    yield result
                    
        except Exception as e:
            logger.error(f"处理用户查询时发生错误: {e}")
            yield {
                "type": "error",
                "content": f"处理请求时发生错误: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

    async def _run_agent_workflow(self, user_input: str, user_id: Optional[int] = None, **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """执行多智能体工作流"""
        
        logger.info("开始执行多智能体工作流")
        
        last_message_cnt = 0
        final_report_sent = False
        chart_data_sent = False  # Track if chart data has been sent
        step_progress = 0  # Track overall workflow progress
        current_step_name = "初始化工作流"
        total_expected_steps = 8  # Estimated: planner, research, analysis, bullish, bearish, trading, final
        workflow_error_occurred = False  # Track if any errors occurred
        fallback_report_content = ""  # Store content for fallback report
        coordinator_message_sent = False  # Track if coordinator initial message has been sent
        
        # Enhanced workflow step mapping
        workflow_steps = {
            0: {"name": "初始化分析任务", "substep": "准备分析环境和参数"},
            15: {"name": "制定分析计划", "substep": "根据用户需求制定详细的分析策略"},
            25: {"name": "数据收集与预处理", "substep": "获取相关市场数据和历史信息"},
            40: {"name": "研究分析", "substep": "深度分析市场趋势和技术指标"},
            60: {"name": "生成看多分析", "substep": "分析有利因素和投资机会"},
            75: {"name": "生成看空分析", "substep": "识别风险因素和市场威胁"},
            85: {"name": "制定交易建议", "substep": "基于分析结果提供具体操作建议"},
            95: {"name": "综合分析整理", "substep": "整合所有分析结果"},
            100: {"name": "生成最终报告", "substep": "完成综合投资分析报告"}
        }
        
        def get_step_info(progress):
            """Get step name and substep description for given progress"""
            for step_progress in sorted(workflow_steps.keys(), reverse=True):
                if progress >= step_progress:
                    return workflow_steps[step_progress]
            return workflow_steps[0]
        
        # Send initial progress with enhanced info
        initial_step = get_step_info(step_progress)
        yield {
            "type": "workflow_progress",
            "step_name": initial_step["name"],
            "substep": initial_step["substep"],
            "progress": step_progress,
            "total_steps": total_expected_steps,
            "timestamp": datetime.now().isoformat(),
            "estimated_time": 180000  # 3 minutes estimated
        }
        
        # 构建初始状态
        initial_state = {
            "messages": [HumanMessage(content=user_input)],
            "locale": kwargs.get("locale", "zh-CN"),
            "enable_background_investigation": kwargs.get("enable_background_investigation", True),
            "max_step_num": kwargs.get("max_step_num", 3),
        }
        
        # 使用用户特定的thread_id确保会话隔离
        thread_id = f"user_{user_id}_thread" if user_id else "default_thread"
        
        config = {
            "configurable": {
                "thread_id": thread_id,
                "max_plan_iterations": kwargs.get("max_plan_iterations", 1),
                "max_step_num": kwargs.get("max_step_num", 3),
                "enable_background_investigation": kwargs.get("enable_background_investigation", True),
            }
        }
        
        try:
            async for s in self.graph.astream(
                input=initial_state, config=config, stream_mode="values"
            ):
                try:
                    # Track workflow progress based on state updates
                    if isinstance(s, dict):
                        # Update progress based on workflow state
                        if "current_plan" in s and s["current_plan"]:
                            if step_progress < 15:
                                step_progress = 15
                                step_info = get_step_info(step_progress)
                                yield {
                                    "type": "workflow_progress",
                                    "step_name": step_info["name"],
                                    "substep": step_info["substep"],
                                    "progress": step_progress,
                                    "total_steps": total_expected_steps,
                                    "timestamp": datetime.now().isoformat(),
                                    "estimated_time": max(0, 180000 - (step_progress * 1800))
                                }
                        
                        # Track research completion
                        if "observations" in s and s["observations"]:
                            obs_count = len(s["observations"])
                            if obs_count > 0 and step_progress < 40:
                                step_progress = min(40, 25 + (obs_count * 5))  # 5% per research step
                                step_info = get_step_info(step_progress)
                                yield {
                                    "type": "workflow_progress",
                                    "step_name": step_info["name"],
                                    "substep": f"{step_info['substep']} (步骤 {obs_count})",
                                    "progress": step_progress,
                                    "total_steps": total_expected_steps,
                                    "timestamp": datetime.now().isoformat(),
                                    "estimated_time": max(0, 180000 - (step_progress * 1800))
                                }
                        
                        # Track report generation and collect content for fallback
                        if "bullish_report" in s and s["bullish_report"] and step_progress < 60:
                            step_progress = 60
                            step_info = get_step_info(step_progress)
                            fallback_report_content += f"\n\n## 看多分析\n{s['bullish_report']}"
                            yield {
                                "type": "workflow_progress",
                                "step_name": step_info["name"],
                                "substep": step_info["substep"],
                                "progress": step_progress,
                                "total_steps": total_expected_steps,
                                "timestamp": datetime.now().isoformat(),
                                "estimated_time": max(0, 180000 - (step_progress * 1800))
                            }
                        
                        if "bearish_report" in s and s["bearish_report"] and step_progress < 75:
                            step_progress = 75
                            step_info = get_step_info(step_progress)
                            fallback_report_content += f"\n\n## 看空分析\n{s['bearish_report']}"
                            yield {
                                "type": "workflow_progress",
                                "step_name": step_info["name"],
                                "substep": step_info["substep"],
                                "progress": step_progress,
                                "total_steps": total_expected_steps,
                                "timestamp": datetime.now().isoformat(),
                                "estimated_time": max(0, 180000 - (step_progress * 1800))
                            }
                        
                        if "trading_advice_report" in s and s["trading_advice_report"] and step_progress < 85:
                            step_progress = 85
                            step_info = get_step_info(step_progress)
                            fallback_report_content += f"\n\n## 交易建议\n{s['trading_advice_report']}"
                            yield {
                                "type": "workflow_progress",
                                "step_name": step_info["name"],
                                "substep": step_info["substep"],
                                "progress": step_progress,
                                "total_steps": total_expected_steps,
                                "timestamp": datetime.now().isoformat(),
                                "estimated_time": max(0, 180000 - (step_progress * 1800))
                            }
                        
                        # Collect observations for fallback
                        if "observations" in s and s["observations"]:
                            for obs in s["observations"]:
                                if obs and obs.strip() and obs not in fallback_report_content:
                                    fallback_report_content += f"\n\n## 研究发现\n{obs}"
                    
                    # Debug logging for state inspection
                    if isinstance(s, dict):
                        available_keys = list(s.keys())
                        logger.debug(f"🔍 WORKFLOW STREAMING: Current state keys: {available_keys}")
                        
                        # Log final report related fields
                        if any(key in available_keys for key in ['final_report', 'comprehensive_final_report', 'workflow_complete', 'workflow_stage']):
                            logger.info(f"🔍 WORKFLOW STREAMING: Final report related fields detected: {[k for k in available_keys if k in ['final_report', 'comprehensive_final_report', 'workflow_complete', 'workflow_stage']]}")
                            for key in ['final_report', 'comprehensive_final_report']:
                                if key in s and s[key]:
                                    logger.info(f"🔍 WORKFLOW STREAMING: {key} has content: {len(str(s[key]))} characters")
                    
                    # 检查是否有图表数据 - 只发送一次
                    if (isinstance(s, dict) and "chart_config" in s and s["chart_config"] 
                        and not chart_data_sent):
                        yield {
                            "type": "chart_data",
                            "content": s["chart_config"],
                            "chart_data": s.get("chart_data"),
                            "has_chart": s.get("has_chart_data", False),
                            "agent": "chart_generator",
                            "timestamp": datetime.now().isoformat()
                        }
                        chart_data_sent = True  # Mark chart data as sent
                    
                    # 优先检查是否有最终报告
                    if isinstance(s, dict) and "final_report" in s and s["final_report"] and not final_report_sent:
                        logger.info(f"🎯 WORKFLOW STREAMING: final_report detected in state")
                        logger.info(f"🎯 WORKFLOW STREAMING: final_report content length: {len(s['final_report'])}")
                        logger.info(f"🎯 WORKFLOW STREAMING: final_report preview: {s['final_report'][:200]}...")
                        
                        # Final progress update
                        step_progress = 100
                        step_info = get_step_info(step_progress)
                        yield {
                            "type": "workflow_progress",
                            "step_name": step_info["name"],
                            "substep": step_info["substep"],
                            "progress": step_progress,
                            "total_steps": total_expected_steps,
                            "timestamp": datetime.now().isoformat(),
                            "estimated_time": 0
                        }
                        
                        logger.info(f"🎯 WORKFLOW STREAMING: Sending final_report to frontend")
                        yield {
                            "type": "final_report",
                            "content": s["final_report"],
                            "agent": "coordinator",
                            "timestamp": datetime.now().isoformat()
                        }
                        final_report_sent = True
                        logger.info(f"🎯 WORKFLOW STREAMING: final_report sent successfully, breaking workflow loop")
                        break
                    
                    # Check for comprehensive_final_report as well (alternative field name)
                    if isinstance(s, dict) and "comprehensive_final_report" in s and s["comprehensive_final_report"] and not final_report_sent:
                        logger.info(f"🎯 WORKFLOW STREAMING: comprehensive_final_report detected in state")
                        logger.info(f"🎯 WORKFLOW STREAMING: comprehensive_final_report content length: {len(s['comprehensive_final_report'])}")
                        logger.info(f"🎯 WORKFLOW STREAMING: comprehensive_final_report preview: {s['comprehensive_final_report'][:200]}...")
                        
                        # Final progress update
                        step_progress = 100
                        step_info = get_step_info(step_progress)
                        yield {
                            "type": "workflow_progress",
                            "step_name": step_info["name"],
                            "substep": step_info["substep"],
                            "progress": step_progress,
                            "total_steps": total_expected_steps,
                            "timestamp": datetime.now().isoformat(),
                            "estimated_time": 0
                        }
                        
                        logger.info(f"🎯 WORKFLOW STREAMING: Sending comprehensive_final_report to frontend")
                        
                        # Send the comprehensive final report
                        final_report_data = {
                            "type": "final_report",
                            "content": s["comprehensive_final_report"],
                            "agent": "final_comprehensive_reporter",
                            "timestamp": datetime.now().isoformat(),
                            "report_type": "comprehensive",
                            "content_length": len(s["comprehensive_final_report"])
                        }
                        
                        logger.info(f"🎯 WORKFLOW STREAMING: Final report data prepared: {list(final_report_data.keys())}")
                        yield final_report_data
                        
                        final_report_sent = True
                        logger.info(f"🎯 WORKFLOW STREAMING: comprehensive_final_report sent successfully, breaking workflow loop")
                        logger.info(f"🎯 WORKFLOW STREAMING: Final report flags - sent: {final_report_sent}")
                        break
                    
                    # Enhanced progress tracking with current agent information
                    if isinstance(s, dict) and "current_active_agent" in s and s["current_active_agent"]:
                        current_agent = s["current_active_agent"]
                        workflow_stage = s.get("workflow_stage", "processing")
                        
                        # Map agents to friendly names and progress
                        agent_info = {
                            "technical_analysis_specialist": {"name": "技术分析师", "progress": 30},
                            "news_analysis_specialist": {"name": "新闻分析师", "progress": 50},
                            "fundamental_analysis_specialist": {"name": "基本面分析师", "progress": 70},
                            "researcher": {"name": "研究员", "progress": 40},
                            "coder": {"name": "数据分析师", "progress": 60}
                        }
                        
                        agent_display = agent_info.get(current_agent, {"name": current_agent, "progress": step_progress})
                        
                        yield {
                            "type": "workflow_progress",
                            "step_name": f"🔄 {agent_display['name']}正在分析",
                            "substep": f"专业{agent_display['name']}正在进行深度分析",
                            "progress": agent_display['progress'],
                            "total_steps": total_expected_steps,
                            "timestamp": datetime.now().isoformat(),
                            "estimated_time": max(0, 180000 - (agent_display['progress'] * 1800)),
                            "current_agent": current_agent
                        }
                    
                    # Skip intermediate agent messages - only process coordinator messages and final reports
                    if isinstance(s, dict) and "messages" in s and s["messages"]:
                        if len(s["messages"]) > last_message_cnt:
                            # Process new messages but filter out specialist agent messages
                            for i in range(last_message_cnt, len(s["messages"])):
                                message = s["messages"][i]
                                
                                # Skip user's own messages
                                if hasattr(message, 'content') and message.content == user_input:
                                    continue
                                
                                # Get message content and sender
                                message_content = getattr(message, 'content', str(message))
                                message_agent = getattr(message, 'name', 'system')
                                
                                # Only allow coordinator messages and final reports through
                                # Skip specialist agent messages (they store data silently)
                                if message_agent in ['technical_analysis_specialist', 'news_analysis_specialist', 
                                                   'fundamental_analysis_specialist', 'researcher', 'coder', 'technical_analyst']:
                                    logger.info(f"Filtering out intermediate message from {message_agent}")
                                    continue
                                
                                # Process coordinator messages
                                if (message_agent == 'coordinator' and not coordinator_message_sent 
                                    and message_content and message_content.strip()):
                                    
                                    # Only send coordinator messages if they are substantial (likely final reports)
                                    if len(message_content) > 200:
                                        yield {
                                            "type": "final_report",
                                            "content": message_content,
                                            "agent": "coordinator",
                                            "timestamp": datetime.now().isoformat()
                                        }
                                        final_report_sent = True
                                        coordinator_message_sent = True
                                        break
                                
                                # Process final reports only from specific reporter agents
                                elif (message_agent in ['final_comprehensive_reporter', 'bullish_reporter', 'bearish_reporter', 'trading_advice_reporter']
                                      and message_content and message_content.strip() and len(message_content) > 100):
                                    logger.info(f"Processing final report from reporter agent: {message_agent}")
                                    # This is a final report from a legitimate reporter
                                    yield {
                                        "type": "final_report",
                                        "content": message_content,
                                        "agent": message_agent,
                                        "timestamp": datetime.now().isoformat()
                                    }
                                    final_report_sent = True
                                    break
                            
                            last_message_cnt = len(s["messages"])
                        
                except Exception as e:
                    logger.error(f"处理流输出时发生错误: {e}")
                    workflow_error_occurred = True
                    yield {
                        "type": "error",
                        "content": f"处理输出时发生错误: {str(e)}",
                        "timestamp": datetime.now().isoformat()
                    }
            
            # CRITICAL: Ensure a final report is always sent, even if the workflow had errors
            if not final_report_sent:
                logger.warning("Final report was not sent during workflow execution, generating fallback report")
                
                # Generate fallback report based on collected content
                if fallback_report_content.strip():
                    fallback_final_report = f"""# 投资分析报告

基于多智能体分析系统收集的信息，以下是针对您查询的分析报告：

{fallback_report_content}

## 风险提示

本报告仅供参考，不构成投资建议。投资有风险，决策需谨慎。请结合自身情况和多方信息做出投资决定。

---
*报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}*
*生成方式: 智能体工作流自动整合*"""
                else:
                    # Last resort: generate a basic response
                    fallback_final_report = f"""# 分析报告

很抱歉，在处理您的查询"{user_input}"时遇到了一些技术问题，但我们仍为您提供以下信息：

## 查询内容
{user_input}

## 处理状态
工作流已尝试执行分析，但未能完成完整的报告生成。建议您：

1. 检查查询内容是否准确（如股票代码、公司名称等）
2. 稍后重试查询
3. 尝试简化查询内容

## 风险提示
本系统正在持续优化中，如遇问题请您谅解。投资有风险，决策需谨慎。

---
*报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}*
*生成方式: 系统自动生成*"""
                
                # Send fallback final report
                yield {
                    "type": "workflow_progress",
                    "step_name": "生成综合分析报告",
                    "progress": 100,
                    "total_steps": total_expected_steps,
                    "timestamp": datetime.now().isoformat()
                }
                
                yield {
                    "type": "final_report",
                    "content": fallback_final_report,
                    "agent": "fallback_coordinator",
                    "timestamp": datetime.now().isoformat()
                }
                final_report_sent = True
            
            # 工作流完成
            yield {
                "type": "workflow_complete",
                "content": "多智能体工作流执行完成",
                "progress": 100,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"多智能体工作流执行错误: {e}")
            
            # CRITICAL: Even if there's a major error, send a fallback final report
            if not final_report_sent:
                error_report = f"""# 系统错误报告

很抱歉，在处理您的查询"{user_input}"时发生了系统错误。

## 错误信息
{str(e)}

## 建议操作
1. 请检查查询格式是否正确
2. 稍后重试查询
3. 如问题持续存在，请联系技术支持

## 收集的信息
{fallback_report_content if fallback_report_content.strip() else "无可用分析信息"}

## 风险提示
本报告因系统错误生成，仅供参考。投资有风险，决策需谨慎。

---
*报告生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}*
*生成方式: 错误恢复机制*"""
                
                yield {
                    "type": "final_report",
                    "content": error_report,
                    "agent": "error_recovery",
                    "timestamp": datetime.now().isoformat()
                }
            
            yield {
                "type": "error",
                "content": f"工作流执行错误: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }

    async def _run_simple_workflow(self, user_input: str, user_id: Optional[int] = None, **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """简单工作流 - 作为备选方案"""
        
        logger.info("执行简单工作流")
        
        # 1. 分析用户意图
        yield {
            "type": "status",
            "content": "正在分析用户意图...",
            "timestamp": datetime.now().isoformat()
        }
        
        intent_analysis = await self.llm_manager.analyze_user_intent(user_input)
        
        yield {
            "type": "intent_analysis",
            "content": intent_analysis,
            "timestamp": datetime.now().isoformat()
        }
        
        # 2. 根据意图选择分析类型
        analysis_type = intent_analysis.get("analysis_type", "其他")
        symbols = intent_analysis.get("extracted_symbols", [])
        
        if symbols and analysis_type in ["技术分析", "基本面分析", "个股查询"]:
            symbol = symbols[0]
            
            yield {
                "type": "status", 
                "content": f"正在进行{analysis_type}分析 - {symbol}",
                "timestamp": datetime.now().isoformat()
            }
            
            # 使用工具增强分析
            analysis_result = await self.agent_manager.get_tool_enhanced_analysis(symbol, user_input)
            
            yield {
                "type": "analysis_result",
                "content": analysis_result,
                "timestamp": datetime.now().isoformat()
            }
        else:
            # 通用LLM回复
            yield {
                "type": "status",
                "content": "正在生成回复...",
                "timestamp": datetime.now().isoformat()
            }
            
            response = await self.llm_manager.get_completion(user_input)
            
            yield {
                "type": "llm_response",
                "content": response,
                "timestamp": datetime.now().isoformat()
            }

    # 保持与原有接口的兼容性
    async def _analyze_user_query(self, user_input: str) -> Dict[str, Any]:
        """分析用户查询 - 兼容性方法"""
        return await self.llm_manager.analyze_user_intent(user_input)

    async def _execute_analysis_plan(self, plan: Dict[str, Any], user_input: str) -> AsyncGenerator[Dict[str, Any], None]:
        """执行分析计划 - 兼容性方法"""
        async for result in self._run_simple_workflow(user_input):
            yield result

    async def _execute_technical_analysis(self, plan: Dict[str, Any], user_input: str) -> AsyncGenerator[Dict[str, Any], None]:
        """执行技术分析 - 兼容性方法"""
        symbols = plan.get("symbols", [])
        if symbols:
            result = await self.agent_manager.get_tool_enhanced_analysis(symbols[0], user_input)
            yield {
                "type": "technical_analysis",
                "content": result,
                "timestamp": datetime.now().isoformat()
            }

    async def _generate_final_report(self, plan: Dict[str, Any]) -> str:
        """生成最终报告 - 兼容性方法"""
        return await self.llm_manager.generate_report(plan, "comprehensive")

    def get_graph_visualization(self) -> str:
        """获取工作流图的可视化"""
        if self.graph:
            try:
                return self.graph.get_graph(xray=True).draw_mermaid()
            except Exception as e:
                logger.error(f"生成图形可视化失败: {e}")
                return "图形可视化暂不可用"
        else:
            return "工作流图未初始化"

# 为向后兼容保留原有的函数接口
async def run_agent_workflow_async(
    user_input: str,
    debug: bool = False,
    max_plan_iterations: int = 1,
    max_step_num: int = 3,
    enable_background_investigation: bool = True,
):
    """运行多智能体工作流 - 兼容性函数"""
    workflow_manager = AIWorkflowManager()
    
    async for result in workflow_manager.process_user_query(
        user_input,
        debug=debug,
        max_plan_iterations=max_plan_iterations,
        max_step_num=max_step_num,
        enable_background_investigation=enable_background_investigation
    ):
        print(f"[{result.get('type', 'unknown')}] {result.get('content', '')}")

if __name__ == "__main__":
    # 测试用例
    import asyncio
    
    async def test_workflow():
        workflow_manager = AIWorkflowManager()
        print(workflow_manager.get_graph_visualization())
        
        async for result in workflow_manager.process_user_query("分析苹果公司AAPL的股票"):
            print(f"[{result.get('type')}] {result.get('content')}")
    
    asyncio.run(test_workflow()) 