# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import json
import logging
import time
from typing import Annotated, Optional, List, Dict, Any
from datetime import datetime, timedelta
import akshare as ak
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed

from langchain_core.tools import tool
from ..decorators import log_io
from ..cache_decorator import news_cached
from ..cache_keys import news_key

logger = logging.getLogger(__name__)

# AkShare新闻源配置 - 映射到原Tushare源
AKSHARE_NEWS_SOURCES = {
    'sina': {
        'name': '新浪财经',
        'func': ak.stock_info_global_sina,
        'args': [],
        'kwargs': {},
        'cache_ttl': 300,  # 5分钟
        'description': '新浪财经全球财经快讯',
        'tushare_equivalent': 'sina'
    },

    '10jqka': {
        'name': '同花顺',
        'func': ak.stock_info_global_ths,
        'args': [],
        'kwargs': {},
        'cache_ttl': 300,  # 5分钟
        'description': '同花顺财经全球财经直播',
        'tushare_equivalent': '10jqka'
    },
    'fenghuang': {
        'name': '凤凰网',
        'func': ak.stock_info_global_sina,  # 使用新浪作为替代
        'args': [],
        'kwargs': {},
        'cache_ttl': 300,  # 5分钟
        'description': '财经快讯（新浪财经源）',
        'tushare_equivalent': 'fenghuang'
    },
    'jinrongjie': {
        'name': '金融界',
        'func': ak.stock_info_global_futu,
        'args': [],
        'kwargs': {},
        'cache_ttl': 300,  # 5分钟
        'description': '富途牛牛财经快讯',
        'tushare_equivalent': 'jinrongjie'
    },
    'yuncaijing': {
        'name': '云财经',
        'func': ak.stock_info_global_cls,
        'args': [],
        'kwargs': {'symbol': '全部'},
        'cache_ttl': 300,  # 5分钟
        'description': '财联社电报快讯',
        'tushare_equivalent': 'yuncaijing'
    }
}


def standardize_akshare_news_format(data: pd.DataFrame, source: str, source_name: str, hours_back: int) -> List[Dict[str, Any]]:
    """
    统一AkShare新闻数据格式，兼容Tushare格式
    
    Args:
        data: 原始数据DataFrame
        source: 数据源标识
        source_name: 数据源显示名称
        hours_back: 时间范围（小时）
        
    Returns:
        标准化的新闻数据列表，兼容Tushare格式
    """
    if data.empty:
        return []
    
    standardized_news = []
    cutoff_time = datetime.now() - timedelta(hours=hours_back)
    
    for _, row in data.iterrows():
        try:
            # 根据不同数据源处理不同的字段结构
            if source in ['sina', 'fenghuang']:
                # 新浪财经格式
                content = str(row.get('内容', ''))
                publish_time_str = str(row.get('时间', ''))
                
            elif source == '10jqka':
                # 同花顺格式
                content = str(row.get('内容', '') or row.get('标题', ''))
                publish_time_str = str(row.get('发布时间', ''))
                
            elif source == 'jinrongjie':
                # 富途牛牛格式
                content = str(row.get('内容', ''))
                publish_time_str = str(row.get('时间', ''))
                
            elif source == 'yuncaijing':
                # 财联社格式
                content = str(row.get('内容', '') or row.get('标题', ''))
                publish_date = str(row.get('发布日期', ''))
                publish_time = str(row.get('发布时间', ''))
                publish_time_str = f"{publish_date} {publish_time}" if publish_date and publish_time else publish_date or publish_time
                
            else:
                # 默认格式
                content = str(row.get('内容', '') or row.get('标题', ''))
                publish_time_str = str(row.get('时间', '') or row.get('发布时间', ''))
            
            # 跳过空内容的新闻
            if not content or content.strip() in ['', 'nan', 'None']:
                continue
            
            # 时间过滤（如果有有效时间）
            if publish_time_str and publish_time_str != 'nan':
                try:
                    # 尝试解析时间
                    if len(publish_time_str) == 10:  # YYYY-MM-DD
                        publish_time = datetime.strptime(publish_time_str, '%Y-%m-%d')
                    elif len(publish_time_str) == 19:  # YYYY-MM-DD HH:MM:SS
                        publish_time = datetime.strptime(publish_time_str, '%Y-%m-%d %H:%M:%S')
                    elif len(publish_time_str) == 16:  # YYYY-MM-DD HH:MM
                        publish_time = datetime.strptime(publish_time_str, '%Y-%m-%d %H:%M')
                    else:
                        # 如果时间格式不标准，使用当前时间
                        publish_time = datetime.now()
                    
                    # 过滤超出时间范围的新闻
                    if publish_time < cutoff_time:
                        continue
                        
                except:
                    # 时间解析失败，使用当前时间
                    publish_time = datetime.now()
                    publish_time_str = publish_time.strftime('%Y-%m-%d %H:%M:%S')
            else:
                # 没有时间信息，使用当前时间
                publish_time = datetime.now()
                publish_time_str = publish_time.strftime('%Y-%m-%d %H:%M:%S')
            
            # 构建兼容Tushare格式的新闻项
            # 使用content作为标题和内容，提高信息密度
            display_title = content[:100] + '...' if len(content) > 100 else content
            news_item = {
                "新闻标题": display_title,
                "新闻内容": content,
                "发布时间": publish_time_str,
                "文章来源": f"AkShare - {source_name}",
                "新闻源": source,
                "新闻源名称": source_name,
                "新闻链接": f"https://akshare.akfamily.xyz/data/news/{source}",
                "分类": str(row.get('channels', '') or row.get('分类', ''))
            }
            
            standardized_news.append(news_item)
            
        except Exception as e:
            logger.warning(f"处理新闻项失败 [{source}]: {e}")
            continue
    
    return standardized_news


def fetch_single_akshare_news(source: str, config: Dict[str, Any], hours_back: int, max_rows: int) -> Dict[str, Any]:
    """
    获取单个AkShare数据源的新闻
    
    Args:
        source: 数据源标识
        config: 数据源配置
        hours_back: 时间范围（小时）
        max_rows: 最大行数
        
    Returns:
        包含新闻数据和状态信息的字典
    """
    result = {
        'source': source,
        'source_name': config['name'],
        'status': 'error',
        'news_data': [],
        'count': 0,
        'error_message': None,
        'response_time': 0
    }
    
    start_time = time.time()
    
    try:
        logger.info(f"开始获取 {config['name']} 新闻数据...")
        
        # 调用akshare接口
        data = config['func'](*config['args'], **config['kwargs'])
        
        end_time = time.time()
        result['response_time'] = end_time - start_time
        
        if isinstance(data, pd.DataFrame) and not data.empty:
            # 标准化数据格式
            standardized_news = standardize_akshare_news_format(data, source, config['name'], hours_back)
            
            # 限制数量
            if len(standardized_news) > max_rows:
                standardized_news = standardized_news[:max_rows]
            
            if standardized_news:
                result['status'] = 'success'
                result['news_data'] = standardized_news
                result['count'] = len(standardized_news)
                logger.info(f"{config['name']} 获取成功: {len(standardized_news)} 条新闻")
            else:
                result['error_message'] = "时间范围内无新闻数据"
                logger.warning(f"{config['name']} 时间范围内无新闻数据")
        else:
            result['error_message'] = "返回数据为空或格式错误"
            logger.warning(f"{config['name']} 返回数据为空")
            
    except Exception as e:
        end_time = time.time()
        result['response_time'] = end_time - start_time
        result['error_message'] = str(e)
        logger.error(f"{config['name']} 获取失败: {e}")
    
    return result


@tool
@log_io
@news_cached(ttl=300)  # 5分钟缓存，保证实时性
def akshare_live_news_tool(
    sources: Annotated[str, "新闻源列表，用逗号分隔，如'sina,10jqka'。留空获取所有源"] = "",
    max_rows_per_source: Annotated[int, "每个新闻源返回的最大行数，默认10"] = 10,
    hours_back: Annotated[int, "获取最近几小时的新闻，默认6小时"] = 6,
    max_total_rows: Annotated[int, "总共返回的最大新闻数，默认50"] = 50,
) -> str:
    """【AkShare工具】实时新闻获取工具。

    获取指定新闻源的最新新闻，无需关键词过滤。支持多个新闻源同时获取。
    完全兼容原Tushare接口格式，无需修改前端代码。
    
    功能：获取AkShare各新闻源的最新新闻数据，按时间排序。
    数据来源：AkShare，无需Token，无API调用限制。

    Args:
        sources: 新闻源列表，支持sina,10jqka,fenghuang,jinrongjie,yuncaijing
        max_rows_per_source: 每个新闻源返回的最大行数
        hours_back: 获取最近几小时的新闻
        max_total_rows: 总共返回的最大新闻数

    Returns:
        JSON格式的实时新闻数据，包含多个新闻源的最新新闻，兼容Tushare格式
    """
    
    try:
        # 确定要获取的新闻源
        if sources.strip():
            source_list = [s.strip() for s in sources.split(',') if s.strip()]
            # 验证新闻源是否支持
            source_list = [s for s in source_list if s in AKSHARE_NEWS_SOURCES]
        else:
            source_list = list(AKSHARE_NEWS_SOURCES.keys())
        
        if not source_list:
            return json.dumps({
                "error": "无效的新闻源",
                "message": f"支持的新闻源: {', '.join(AKSHARE_NEWS_SOURCES.keys())}",
                "data": []
            }, ensure_ascii=False)

        logger.info(f"获取AkShare新闻源: {source_list}")

        # 使用线程池并发获取多个新闻源
        all_news_data = []
        
        with ThreadPoolExecutor(max_workers=min(len(source_list), 5)) as executor:
            # 提交所有任务
            future_to_source = {
                executor.submit(
                    fetch_single_akshare_news, 
                    source, 
                    AKSHARE_NEWS_SOURCES[source], 
                    hours_back, 
                    max_rows_per_source
                ): source
                for source in source_list
            }
            
            # 收集结果
            for future in as_completed(future_to_source):
                source = future_to_source[future]
                try:
                    result = future.result()
                    
                    if result['status'] == 'success' and result['news_data']:
                        all_news_data.extend(result['news_data'])
                    else:
                        logger.warning(f"新闻源 {source} 获取失败: {result.get('error_message', 'Unknown error')}")
                            
                except Exception as e:
                    logger.error(f"处理新闻源 {source} 结果失败: {e}")

        # 按发布时间排序
        try:
            all_news_data.sort(key=lambda x: x['发布时间'], reverse=True)
        except:
            logger.warning("新闻时间排序失败，使用原始顺序")

        # 限制总数
        if len(all_news_data) > max_total_rows:
            all_news_data = all_news_data[:max_total_rows]

        # 按新闻源分组统计
        source_stats = {}
        for item in all_news_data:
            source = item['新闻源']
            if source not in source_stats:
                source_stats[source] = {
                    'name': item['新闻源名称'],
                    'count': 0
                }
            source_stats[source]['count'] += 1

        result = {
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "sources_requested": source_list,
            "sources_stats": source_stats,
            "total_news": len(all_news_data),
            "time_range_hours": hours_back,
            "data": all_news_data
        }

        return json.dumps(result, ensure_ascii=False)

    except Exception as e:
        logger.error(f"获取AkShare实时新闻错误: {e}")
        return json.dumps({
            "error": "获取实时新闻失败",
            "message": str(e),
            "data": []
        }, ensure_ascii=False)


@tool
@log_io  
def akshare_news_summary_tool(hours_back: Annotated[int, "统计最近几小时的新闻，默认24小时"] = 24) -> str:
    """获取AkShare新闻源统计摘要信息。
    
    Args:
        hours_back: 统计最近几小时的新闻
        
    Returns:
        JSON格式的新闻源统计信息，兼容Tushare格式
    """
    try:
        source_summary = []
        
        for source_id, config in AKSHARE_NEWS_SOURCES.items():
            try:
                # 获取单个源的数据进行统计
                result = fetch_single_akshare_news(source_id, config, hours_back, 100)
                
                source_info = {
                    "id": source_id,
                    "name": config['name'],
                    "news_count": result['count'],
                    "status": result['status'],
                    "latest_news_time": None,
                    "response_time": result['response_time']
                }
                
                # 获取最新新闻时间
                if result['news_data']:
                    latest_news = max(result['news_data'], key=lambda x: x['发布时间'])
                    source_info['latest_news_time'] = latest_news['发布时间']
                
                if result['status'] != 'success':
                    source_info['error'] = result['error_message']
                
            except Exception as e:
                source_info = {
                    "id": source_id,
                    "name": config['name'],
                    "news_count": 0,
                    "status": "error",
                    "error": str(e),
                    "response_time": 0
                }
                
            source_summary.append(source_info)
        
        return json.dumps({
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "time_range_hours": hours_back,
            "sources": source_summary,
            "total_sources": len(AKSHARE_NEWS_SOURCES)
        }, ensure_ascii=False)
        
    except Exception as e:
        logger.error(f"获取AkShare新闻源摘要错误: {e}")
        return json.dumps({
            "error": "获取新闻源摘要失败",
            "message": str(e),
            "sources": []
        }, ensure_ascii=False)


if __name__ == "__main__":
    # 测试代码
    print("=== 测试AkShare实时新闻工具 ===")
    result = akshare_live_news_tool.invoke({
        "sources": "sina,10jqka",
        "max_rows_per_source": 5,
        "hours_back": 6,
        "max_total_rows": 20
    })
    print(result)
    
    print("\n=== 测试AkShare新闻源摘要工具 ===")
    result = akshare_news_summary_tool.invoke({"hours_back": 24})
    print(result) 