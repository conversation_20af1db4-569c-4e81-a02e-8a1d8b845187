#!/usr/bin/env python3
"""
用户感知的AI工作流管理器
实现用户特定的内存管理和会话隔离
"""

import logging
from typing import Optional, Dict, Any, AsyncGenerator
from datetime import datetime

from .workflow import AIWorkflowManager
from .graph.builder import build_graph_with_memory
from backend.auth.user_context import get_current_user_id

logger = logging.getLogger(__name__)

class UserAwareAIWorkflowManager(AIWorkflowManager):
    """用户感知的AI工作流管理器"""
    
    def __init__(self, data_manager=None):
        super().__init__(data_manager)
        self.user_graphs = {}  # 存储用户特定的图实例
        logger.info("用户感知AI工作流管理器初始化完成")
    
    def _get_user_graph(self, user_id: Optional[int] = None):
        """获取用户特定的图实例"""
        if user_id is None:
            user_id = get_current_user_id() or -1  # 使用系统用户ID作为默认值
        
        # 如果用户图不存在，创建一个新的
        if user_id not in self.user_graphs:
            try:
                # 为每个用户创建独立的内存图
                self.user_graphs[user_id] = build_graph_with_memory()
                logger.info(f"为用户 {user_id} 创建了独立的工作流图")
            except Exception as e:
                logger.error(f"为用户 {user_id} 创建工作流图失败: {e}")
                # 回退到无内存图
                from .graph.builder import build_graph
                self.user_graphs[user_id] = build_graph()
        
        return self.user_graphs[user_id]
    
    async def process_user_query(self, user_input: str, user_id: Optional[int] = None, **kwargs) -> AsyncGenerator[Dict[str, Any], None]:
        """
        处理用户查询 - 使用用户特定的图实例
        
        Args:
            user_input: 用户输入
            user_id: 用户ID，用于数据隔离
            **kwargs: 其他参数
            
        Yields:
            Dict[str, Any]: 流式返回处理结果
        """
        
        if not user_input or not user_input.strip():
            yield {
                "type": "error",
                "content": "输入不能为空",
                "timestamp": datetime.now().isoformat()
            }
            return
        
        # 获取用户特定的图实例
        user_graph = self._get_user_graph(user_id)
        
        # 临时替换全局图为用户特定图
        original_graph = self.graph
        self.graph = user_graph
        
        try:
            logger.info(f"使用用户特定工作流处理查询 (用户ID: {user_id})")
            async for result in super().process_user_query(user_input, user_id=user_id, **kwargs):
                yield result
        finally:
            # 恢复原始图
            self.graph = original_graph
    
    def clear_user_memory(self, user_id: int):
        """清除用户的聊天记录和内存"""
        if user_id in self.user_graphs:
            try:
                # 删除用户特定的图实例，下次访问时会重新创建
                del self.user_graphs[user_id]
                logger.info(f"已清除用户 {user_id} 的聊天记录和内存")
                return True
            except Exception as e:
                logger.error(f"清除用户 {user_id} 内存失败: {e}")
                return False
        return True
    
    def get_user_memory_stats(self, user_id: int) -> Dict[str, Any]:
        """获取用户内存使用统计"""
        if user_id in self.user_graphs:
            try:
                # 这里可以添加更详细的内存统计逻辑
                return {
                    "user_id": user_id,
                    "has_memory": True,
                    "graph_initialized": True,
                    "last_accessed": datetime.now().isoformat()
                }
            except Exception as e:
                logger.error(f"获取用户 {user_id} 内存统计失败: {e}")
                return {"user_id": user_id, "error": str(e)}
        else:
            return {
                "user_id": user_id,
                "has_memory": False,
                "graph_initialized": False
            }
    
    def cleanup_inactive_users(self, max_inactive_hours: int = 24):
        """清理长时间不活跃用户的内存"""
        # 这里可以实现基于时间的清理逻辑
        # 目前简单实现，后续可以添加更复杂的清理策略
        inactive_users = []
        for user_id in list(self.user_graphs.keys()):
            # 简单的清理逻辑，实际应该基于最后访问时间
            if len(self.user_graphs) > 100:  # 如果用户图太多，清理一些
                inactive_users.append(user_id)
        
        for user_id in inactive_users[:10]:  # 最多清理10个
            self.clear_user_memory(user_id)
        
        logger.info(f"清理了 {len(inactive_users)} 个不活跃用户的内存")
        return len(inactive_users) 