"""
引用管理工具

从deepreaserch模块迁移的引用处理功能
"""

from typing import Any, Dict, List
import logging

logger = logging.getLogger(__name__)


class CitationManager:
    """引用管理器"""
    
    @staticmethod
    def get_citations(response, resolved_urls_map: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        Extracts and formats citation information from a Gemini model's response.

        This function processes the grounding metadata provided in the response to
        construct a list of citation objects. Each citation object includes the
        start and end indices of the text segment it refers to, and a string
        containing formatted markdown links to the supporting web chunks.

        Args:
            response: The response object from the Gemini model, expected to have
                      a structure including `candidates[0].grounding_metadata`.
            resolved_urls_map: Dictionary mapping original URLs to resolved/shortened URLs

        Returns:
            list: A list of dictionaries, where each dictionary represents a citation
                  and has the following keys:
                  - "start_index" (int): The starting character index of the cited
                                         segment in the original text. Defaults to 0
                                         if not specified.
                  - "end_index" (int): The character index immediately after the
                                       end of the cited segment (exclusive).
                  - "segments" (list[dict]): A list of individual citation segments
                  - "segment_string" (str): A concatenated string of all markdown-
                                            formatted links for the citation.
                  Returns an empty list if no valid candidates or grounding supports
                  are found, or if essential data is missing.
        """
        citations = []

        # Ensure response and necessary nested structures are present
        if not response or not response.candidates:
            return citations

        candidate = response.candidates[0]
        if (
            not hasattr(candidate, "grounding_metadata")
            or not candidate.grounding_metadata
            or not hasattr(candidate.grounding_metadata, "grounding_supports")
        ):
            return citations

        try:
            for support in candidate.grounding_metadata.grounding_supports:
                citation = {}

                # Ensure segment information is present
                if not hasattr(support, "segment") or support.segment is None:
                    continue  # Skip this support if segment info is missing

                start_index = (
                    support.segment.start_index
                    if support.segment.start_index is not None
                    else 0
                )

                # Ensure end_index is present to form a valid segment
                if support.segment.end_index is None:
                    continue  # Skip if end_index is missing, as it's crucial

                # Add 1 to end_index to make it an exclusive end for slicing/range purposes
                citation["start_index"] = start_index
                citation["end_index"] = support.segment.end_index

                citation["segments"] = []
                if (
                    hasattr(support, "grounding_chunk_indices")
                    and support.grounding_chunk_indices
                ):
                    for ind in support.grounding_chunk_indices:
                        try:
                            chunk = candidate.grounding_metadata.grounding_chunks[ind]
                            resolved_url = resolved_urls_map.get(chunk.web.uri, None)
                            citation["segments"].append(
                                {
                                    "label": chunk.web.title.split(".")[:-1][0] if chunk.web.title else "Source",
                                    "short_url": resolved_url,
                                    "value": chunk.web.uri,
                                }
                            )
                        except (IndexError, AttributeError, NameError) as e:
                            logger.warning(f"Failed to process grounding chunk {ind}: {e}")
                            continue
                citations.append(citation)
        except Exception as e:
            logger.error(f"Error processing citations: {e}")
            
        return citations

    @staticmethod
    def insert_citation_markers(text: str, citations_list: List[Dict[str, Any]]) -> str:
        """
        Inserts citation markers into a text string based on start and end indices.

        Args:
            text (str): The original text string.
            citations_list (list): A list of dictionaries, where each dictionary
                                   contains 'start_index', 'end_index', and
                                   'segments' (the citation data to format).
                                   Indices are assumed to be for the original text.

        Returns:
            str: The text with citation markers inserted.
        """
        if not citations_list:
            return text
            
        # Sort citations by end_index in descending order.
        # If end_index is the same, secondary sort by start_index descending.
        # This ensures that insertions at the end of the string don't affect
        # the indices of earlier parts of the string that still need to be processed.
        sorted_citations = sorted(
            citations_list, key=lambda c: (c["end_index"], c["start_index"]), reverse=True
        )

        modified_text = text
        for citation_info in sorted_citations:
            try:
                # These indices refer to positions in the *original* text,
                # but since we iterate from the end, they remain valid for insertion
                # relative to the parts of the string already processed.
                end_idx = citation_info["end_index"]
                marker_to_insert = ""
                for segment in citation_info["segments"]:
                    marker_to_insert += f" [{segment['label']}]({segment['short_url']})"
                # Insert the citation marker at the original end_idx position
                modified_text = (
                    modified_text[:end_idx] + marker_to_insert + modified_text[end_idx:]
                )
            except (KeyError, IndexError) as e:
                logger.warning(f"Failed to insert citation marker: {e}")
                continue

        return modified_text
    
    @staticmethod
    def format_citations_for_display(citations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format citations for frontend display
        
        Args:
            citations: List of citation objects
            
        Returns:
            List of formatted citation objects for display
        """
        formatted_citations = []
        
        for citation in citations:
            for segment in citation.get("segments", []):
                formatted_citations.append({
                    "title": segment.get("label", "Source"),
                    "url": segment.get("value", ""),
                    "short_url": segment.get("short_url", ""),
                    "domain": CitationManager._extract_domain(segment.get("value", ""))
                })
        
        # Remove duplicates based on URL
        seen_urls = set()
        unique_citations = []
        for citation in formatted_citations:
            if citation["url"] not in seen_urls:
                seen_urls.add(citation["url"])
                unique_citations.append(citation)
        
        return unique_citations
    
    @staticmethod
    def _extract_domain(url: str) -> str:
        """Extract domain from URL for display"""
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc
        except Exception:
            if "://" in url:
                domain_part = url.split("://")[1]
                return domain_part.split("/")[0]
            return url 