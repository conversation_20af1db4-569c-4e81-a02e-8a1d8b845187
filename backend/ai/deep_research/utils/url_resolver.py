"""
URL解析工具

从deepreaserch模块迁移的URL处理功能
"""

from typing import Any, Dict, List


class URLResolver:
    """URL解析和管理"""
    
    @staticmethod
    def resolve_urls(urls_to_resolve: List[Any], id: int) -> Dict[str, str]:
        """
        Create a map of the vertex ai search urls (very long) to a short url with a unique id for each url.
        Ensures each original URL gets a consistent shortened form while maintaining uniqueness.
        
        Args:
            urls_to_resolve: List of URLs or URL objects to resolve
            id: Unique identifier for this resolution session
            
        Returns:
            Dictionary mapping original URLs to shortened URLs
        """
        prefix = f"https://vertexaisearch.cloud.google.com/id/"
        
        # Extract URLs from different possible object structures
        urls = []
        for site in urls_to_resolve:
            if hasattr(site, 'web') and hasattr(site.web, 'uri'):
                urls.append(site.web.uri)
            elif hasattr(site, 'uri'):
                urls.append(site.uri)
            elif isinstance(site, str):
                urls.append(site)
            else:
                # Try to extract URL from dict-like structure
                if isinstance(site, dict):
                    url = site.get('url') or site.get('uri') or site.get('link')
                    if url:
                        urls.append(url)

        # Create a dictionary that maps each unique URL to its first occurrence index
        resolved_map = {}
        for idx, url in enumerate(urls):
            if url not in resolved_map:
                resolved_map[url] = f"{prefix}{id}-{idx}"

        return resolved_map
    
    @staticmethod
    def create_short_url(original_url: str, session_id: int, url_index: int) -> str:
        """
        Create a short URL for a given original URL
        
        Args:
            original_url: The original long URL
            session_id: Unique session identifier  
            url_index: Index of the URL in the current session
            
        Returns:
            Shortened URL
        """
        return f"https://vertexaisearch.cloud.google.com/id/{session_id}-{url_index}"
    
    @staticmethod
    def extract_domain(url: str) -> str:
        """
        Extract domain from URL for display purposes
        
        Args:
            url: Full URL
            
        Returns:
            Domain name
        """
        try:
            from urllib.parse import urlparse
            parsed = urlparse(url)
            return parsed.netloc
        except Exception:
            # Fallback: simple string extraction
            if "://" in url:
                domain_part = url.split("://")[1]
                return domain_part.split("/")[0]
            return url 