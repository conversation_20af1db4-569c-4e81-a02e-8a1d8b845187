"""
新闻研究适配器

将新闻内容转换为适合深度研究的查询和分析任务
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class NewsResearchAdapter:
    """新闻研究适配器"""
    
    def __init__(self):
        self.market_keywords = {
            'US': ['美股', 'nasdaq', 'dow jones', 'sp500', 'nyse', 'us market'],
            'A_SHARE': ['a股', '上证', '深证', '沪深', '中国股市', 'csi'],
            'HK': ['港股', '恒生', 'hang seng', 'hk market', '香港股市'],
            'CRYPTO': ['加密货币', 'bitcoin', 'crypto', '比特币', '以太坊']
        }
        
        self.industry_keywords = {
            'TECH': ['科技', '技术', '互联网', 'ai', '人工智能', '芯片'],
            'FINANCE': ['金融', '银行', '保险', '证券', '基金'],
            'ENERGY': ['能源', '石油', '新能源', '电力', '煤炭'],
            'HEALTHCARE': ['医药', '生物', '医疗', '健康'],
            'CONSUMER': ['消费', '零售', '食品', '汽车'],
            'REAL_ESTATE': ['房地产', '地产', '房价']
        }
    
    def analyze_news_context(self, news_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析新闻内容，提取关键信息和上下文
        
        Args:
            news_data: 新闻数据
            
        Returns:
            分析后的上下文信息
        """
        title = news_data.get('title', '')
        content = news_data.get('content', '')
        source = news_data.get('source', '')
        
        # 组合文本用于分析
        full_text = f"{title} {content}".lower()
        
        # 检测市场关联
        affected_markets = []
        for market, keywords in self.market_keywords.items():
            if any(keyword.lower() in full_text for keyword in keywords):
                affected_markets.append(market)
        
        # 检测行业关联
        affected_industries = []
        for industry, keywords in self.industry_keywords.items():
            if any(keyword.lower() in full_text for keyword in keywords):
                affected_industries.append(industry)
        
        # 分析新闻类型
        news_type = self._classify_news_type(title, content)
        
        # 评估影响程度
        impact_indicators = self._assess_impact_indicators(title, content)
        
        return {
            'affected_markets': affected_markets,
            'affected_industries': affected_industries,
            'news_type': news_type,
            'impact_indicators': impact_indicators,
            'analysis_priority': self._calculate_priority(
                affected_markets, affected_industries, impact_indicators
            )
        }
    
    def generate_research_topic(self, news_data: Dict[str, Any]) -> str:
        """
        为新闻生成研究主题
        
        Args:
            news_data: 新闻数据
            
        Returns:
            研究主题字符串
        """
        title = news_data.get('title', '')
        context = self.analyze_news_context(news_data)
        
        # 基于新闻类型和影响生成主题
        markets = context['affected_markets']
        industries = context['affected_industries']
        
        research_topic = f"分析新闻'{title}'对金融市场的深度影响"
        
        if markets:
            market_names = {'US': '美股', 'A_SHARE': 'A股', 'HK': '港股', 'CRYPTO': '加密货币市场'}
            market_str = '、'.join([market_names.get(m, m) for m in markets])
            research_topic += f"，重点关注{market_str}"
        
        if industries:
            industry_names = {
                'TECH': '科技行业', 'FINANCE': '金融行业', 'ENERGY': '能源行业',
                'HEALTHCARE': '医疗健康行业', 'CONSUMER': '消费行业', 'REAL_ESTATE': '房地产行业'
            }
            industry_str = '、'.join([industry_names.get(i, i) for i in industries])
            research_topic += f"以及{industry_str}的影响"
        
        return research_topic
    
    def create_research_queries(
        self, 
        news_data: Dict[str, Any], 
        num_queries: int = 2
    ) -> List[Dict[str, str]]:
        """
        为新闻创建专门的研究查询
        
        Args:
            news_data: 新闻数据
            num_queries: 查询数量
            
        Returns:
            查询列表
        """
        title = news_data.get('title', '')
        content = news_data.get('content', '')
        context = self.analyze_news_context(news_data)
        
        queries = []
        
        # 基础市场影响查询
        base_query = f"{title} market impact financial analysis"
        queries.append({
            "query": base_query,
            "rationale": "分析新闻对整体金融市场的直接影响"
        })
        
        # 针对特定市场的查询
        if context['affected_markets'] and len(queries) < num_queries:
            for market in context['affected_markets'][:1]:  # 只取第一个主要市场
                market_query = f"{title} {market} market sector analysis"
                queries.append({
                    "query": market_query,
                    "rationale": f"深入分析对{market}市场的具体影响"
                })
                break
        
        # 行业影响查询
        if context['affected_industries'] and len(queries) < num_queries:
            for industry in context['affected_industries'][:1]:  # 只取第一个主要行业
                industry_query = f"{title} {industry} industry impact investment"
                queries.append({
                    "query": industry_query,
                    "rationale": f"分析对{industry}行业的投资影响"
                })
                break
        
        # 如果查询不够，添加通用分析查询
        while len(queries) < num_queries:
            if len(queries) == 1:
                queries.append({
                    "query": f"{title} stock price reaction analyst opinion",
                    "rationale": "研究分析师观点和股价反应"
                })
            elif len(queries) == 2:
                queries.append({
                    "query": f"{title} long term economic implications",
                    "rationale": "评估长期经济影响"
                })
            else:
                break
        
        return queries[:num_queries]
    
    def format_research_context(self, news_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        格式化研究上下文，用于传递给深度研究引擎
        
        Args:
            news_data: 新闻数据
            
        Returns:
            格式化的研究上下文
        """
        context = self.analyze_news_context(news_data)
        
        return {
            'news_data': news_data,
            'research_topic': self.generate_research_topic(news_data),
            'suggested_queries': self.create_research_queries(news_data),
            'analysis_context': context,
            'research_configuration': {
                'focus_areas': context['affected_markets'] + context['affected_industries'],
                'priority_level': context['analysis_priority'],
                'analysis_depth': 'deep' if context['analysis_priority'] == 'high' else 'standard'
            }
        }
    
    def _classify_news_type(self, title: str, content: str) -> str:
        """分类新闻类型"""
        text = f"{title} {content}".lower()
        
        # 监管政策类
        if any(word in text for word in ['监管', '政策', '法规', '央行', '证监会']):
            return 'regulatory'
        
        # 财报业绩类
        if any(word in text for word in ['财报', '业绩', '营收', '利润', '季报', '年报']):
            return 'earnings'
        
        # 公司事件类
        if any(word in text for word in ['并购', '重组', '投资', '合作', '上市', '退市']):
            return 'corporate_event'
        
        # 宏观经济类
        if any(word in text for word in ['gdp', '通胀', '利率', '汇率', '经济增长']):
            return 'macroeconomic'
        
        # 行业动态类
        if any(word in text for word in ['行业', '板块', '趋势', '发展']):
            return 'industry_trend'
        
        return 'general'
    
    def _assess_impact_indicators(self, title: str, content: str) -> Dict[str, Any]:
        """评估影响指标"""
        text = f"{title} {content}".lower()
        
        # 高影响关键词
        high_impact_keywords = ['暴跌', '大涨', '重大', '突发', '危机', '爆发', '崩盘']
        medium_impact_keywords = ['上涨', '下跌', '增长', '下降', '调整', '变化']
        
        high_impact_count = sum(1 for word in high_impact_keywords if word in text)
        medium_impact_count = sum(1 for word in medium_impact_keywords if word in text)
        
        # 时间敏感性
        time_sensitive_keywords = ['今日', '昨日', '刚刚', '最新', '紧急']
        time_sensitivity = any(word in text for word in time_sensitive_keywords)
        
        return {
            'high_impact_signals': high_impact_count,
            'medium_impact_signals': medium_impact_count,
            'time_sensitive': time_sensitivity,
            'urgency_score': high_impact_count * 3 + medium_impact_count * 1
        }
    
    def _calculate_priority(
        self, 
        markets: List[str], 
        industries: List[str], 
        impact_indicators: Dict[str, Any]
    ) -> str:
        """计算分析优先级"""
        
        # 基础分数
        score = 0
        
        # 市场影响分数
        score += len(markets) * 2
        
        # 行业影响分数
        score += len(industries) * 1
        
        # 影响指标分数
        score += impact_indicators['urgency_score']
        
        # 时间敏感性加分
        if impact_indicators['time_sensitive']:
            score += 3
        
        # 确定优先级
        if score >= 8:
            return 'high'
        elif score >= 4:
            return 'medium'
        else:
            return 'low' 