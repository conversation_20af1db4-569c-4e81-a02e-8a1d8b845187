"""
四层思维链分析器
实现"由外向内、由宏观到微观、由影响到标的"的四层漏斗模型分析
"""

import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class LayerAnalysisResult:
    """单层分析结果"""
    layer_name: str
    analysis_content: str
    key_findings: List[str]
    confidence_score: float
    next_layer_inputs: Dict[str, Any]

@dataclass
class FourLayerAnalysisResult:
    """四层分析完整结果"""
    layer1_result: LayerAnalysisResult  # 事件感知
    layer2_result: LayerAnalysisResult  # 深度挖掘
    layer3_result: LayerAnalysisResult  # 国内影响
    layer4_result: LayerAnalysisResult  # 标的筛选
    final_investment_targets: List[Dict[str, Any]]
    overall_confidence: float
    analysis_summary: str

class FourLayerThinkingAnalyzer:
    """四层思维链分析器"""
    
    def __init__(self, llm_manager):
        self.llm_manager = llm_manager
        
    def analyze_news_event(
        self, 
        news_data: Dict[str, Any],
        research_summaries: List[str]
    ) -> FourLayerAnalysisResult:
        """
        对新闻事件进行四层思维链分析
        
        Args:
            news_data: 新闻数据
            research_summaries: 研究总结列表
            
        Returns:
            四层分析结果
        """
        try:
            # 第一层：事件感知与直接联想
            layer1_result = self._analyze_layer1_event_perception(
                news_data, research_summaries
            )
            
            # 第二层：深挖供应链与关键信息
            layer2_result = self._analyze_layer2_supply_chain(
                news_data, research_summaries, layer1_result
            )
            
            # 第三层：聚焦国内产业与市场动态
            layer3_result = self._analyze_layer3_domestic_impact(
                news_data, research_summaries, layer2_result
            )
            
            # 第四层：筛选与锁定具体上市公司
            layer4_result = self._analyze_layer4_target_selection(
                news_data, research_summaries, layer3_result
            )
            
            # 生成最终投资标的
            final_targets = self._generate_final_investment_targets(
                layer4_result
            )
            
            # 计算整体置信度
            overall_confidence = self._calculate_overall_confidence([
                layer1_result, layer2_result, layer3_result, layer4_result
            ])
            
            # 生成分析总结
            analysis_summary = self._generate_analysis_summary(
                layer1_result, layer2_result, layer3_result, layer4_result
            )
            
            return FourLayerAnalysisResult(
                layer1_result=layer1_result,
                layer2_result=layer2_result,
                layer3_result=layer3_result,
                layer4_result=layer4_result,
                final_investment_targets=final_targets,
                overall_confidence=overall_confidence,
                analysis_summary=analysis_summary
            )
            
        except Exception as e:
            logger.error(f"四层思维链分析失败: {e}")
            raise
    
    def _analyze_layer1_event_perception(
        self, 
        news_data: Dict[str, Any], 
        research_summaries: List[str]
    ) -> LayerAnalysisResult:
        """第一层：事件感知与直接联想分析"""
        
        prompt = self._build_layer1_prompt(news_data, research_summaries)
        
        response = self.llm_manager.get_gemini_response(
            prompt,
            model="gemini-2.0-flash-exp",
            temperature=0.3
        )
        
        # 解析响应并提取关键信息
        key_findings = self._extract_key_findings(response, "layer1")
        next_layer_inputs = self._extract_next_layer_inputs(response, "layer1")
        
        return LayerAnalysisResult(
            layer_name="第一层：事件感知与直接联想",
            analysis_content=response,
            key_findings=key_findings,
            confidence_score=0.8,
            next_layer_inputs=next_layer_inputs
        )
    
    def _analyze_layer2_supply_chain(
        self, 
        news_data: Dict[str, Any], 
        research_summaries: List[str],
        layer1_result: LayerAnalysisResult
    ) -> LayerAnalysisResult:
        """第二层：深挖供应链与关键信息分析"""
        
        prompt = self._build_layer2_prompt(
            news_data, research_summaries, layer1_result
        )
        
        response = self.llm_manager.get_gemini_response(
            prompt,
            model="gemini-2.0-flash-exp",
            temperature=0.5
        )
        
        key_findings = self._extract_key_findings(response, "layer2")
        next_layer_inputs = self._extract_next_layer_inputs(response, "layer2")
        
        return LayerAnalysisResult(
            layer_name="第二层：深挖供应链与关键信息",
            analysis_content=response,
            key_findings=key_findings,
            confidence_score=0.9,  # 这一层是关键的信息差层
            next_layer_inputs=next_layer_inputs
        )
    
    def _analyze_layer3_domestic_impact(
        self, 
        news_data: Dict[str, Any], 
        research_summaries: List[str],
        layer2_result: LayerAnalysisResult
    ) -> LayerAnalysisResult:
        """第三层：聚焦国内产业与市场动态分析"""
        
        prompt = self._build_layer3_prompt(
            news_data, research_summaries, layer2_result
        )
        
        response = self.llm_manager.get_gemini_response(
            prompt,
            model="gemini-2.0-flash-exp",
            temperature=0.4
        )
        
        key_findings = self._extract_key_findings(response, "layer3")
        next_layer_inputs = self._extract_next_layer_inputs(response, "layer3")
        
        return LayerAnalysisResult(
            layer_name="第三层：聚焦国内产业与市场动态",
            analysis_content=response,
            key_findings=key_findings,
            confidence_score=0.85,
            next_layer_inputs=next_layer_inputs
        )
    
    def _analyze_layer4_target_selection(
        self, 
        news_data: Dict[str, Any], 
        research_summaries: List[str],
        layer3_result: LayerAnalysisResult
    ) -> LayerAnalysisResult:
        """第四层：筛选与锁定具体上市公司分析"""
        
        prompt = self._build_layer4_prompt(
            news_data, research_summaries, layer3_result
        )
        
        response = self.llm_manager.get_gemini_response(
            prompt,
            model="gemini-2.0-flash-exp",
            temperature=0.2  # 最终选择需要更精确
        )
        
        key_findings = self._extract_key_findings(response, "layer4")
        next_layer_inputs = self._extract_next_layer_inputs(response, "layer4")
        
        return LayerAnalysisResult(
            layer_name="第四层：筛选与锁定具体上市公司",
            analysis_content=response,
            key_findings=key_findings,
            confidence_score=0.75,
            next_layer_inputs=next_layer_inputs
        )
    
    def _build_layer1_prompt(
        self, 
        news_data: Dict[str, Any], 
        research_summaries: List[str]
    ) -> str:
        """构建第一层分析提示词"""
        
        news_title = news_data.get('title', '')
        news_content = news_data.get('content', '')
        news_source = news_data.get('source', '未知来源')
        summaries_text = "\n".join([f"- {summary}" for summary in research_summaries])
        
        return f"""
# 第一层分析：事件感知与直接联想

你是一位顶级投资分析师，请对以下新闻事件进行第一层分析。

## 新闻信息
标题：{news_title}
内容：{news_content}
来源：{news_source}

## 研究资料
{summaries_text}

## 关键原则
**在开始分析前，请务必先验证和评估：**
1. **信息可信度检查**：评估新闻内容的真实性和可信度
2. **来源可靠性**：判断新闻来源的权威性
3. **事实核实**：检查是否存在明显的事实错误或虚假信息
4. **逻辑一致性**：验证新闻内容是否符合常识和逻辑

**如果发现以下情况，请在分析中明确指出：**
- 信息来源不明或不可靠
- 内容存在明显错误或矛盾
- 缺乏核心事实支撑
- 可能是虚假或误导性信息

## 分析要求

### 1.1 信息可信度评估（优先进行）
- 评估新闻内容的真实性和可信度（1-10分）
- 检查是否存在明显的事实错误、逻辑矛盾或虚假信息
- 验证关键人物、事件、数据的真实性
- 如果可信度低于6分，请明确说明原因并调整分析策略

### 1.2 事件核心要素提取
- 明确识别新闻事件的核心内容、关键主体
- 提取关键时间、地点、影响范围
- 评估事件的紧急程度和市场关注度

### 1.3 第一性原理推断  
- 基于确认的事实和直接逻辑进行初步判断
- 识别最直观的市场影响路径
- 预测immediate market reaction

### 1.4 大众认知预期
- 预测市场的普遍反应和可能被热炒的标的
- 识别"大众认知"的局限性和盲点
- 为下一层分析提供"避开大众认知"的方向

## 输出要求
请提供详细分析，并在最后总结：
1. **信息可信度评估**（评分+说明）
2. 核心事件要素
3. 直接影响路径
4. 大众可能关注的领域（需要避开的"红海"）
5. 值得深挖的方向（为第二层分析提供线索）

**特别注意：如果信息可信度低，请在分析中明确指出，并基于有限的可信信息进行谨慎分析。**
"""
    
    def _build_layer2_prompt(
        self, 
        news_data: Dict[str, Any], 
        research_summaries: List[str],
        layer1_result: LayerAnalysisResult
    ) -> str:
        """构建第二层分析提示词"""
        
        news_title = news_data.get('title', '')
        summaries_text = "\n".join([f"- {summary}" for summary in research_summaries])
        layer1_findings = "\n".join([f"- {finding}" for finding in layer1_result.key_findings])
        
        return f"""
# 第二层分析：深挖供应链与关键信息（信息差挖掘层）

基于第一层分析结果，现在进行关键的"信息差挖掘"分析。

## 第一层分析结果
{layer1_findings}

## 研究资料
{summaries_text}

## 深度分析要求

### 2.1 核心受影响主体识别
- 深入分析事件直接冲击的国家、地区、公司或行业
- 识别被大众忽视但实际受重大影响的主体
- 分析影响的传导路径和时间序列

### 2.2 全球供应链角色解构
- 分析受影响主体在全球经济中的具体角色
- 识别其主要产品、服务和出口商品（特别是不为人知的关键商品）
- 挖掘供应链中的关键节点和瓶颈

### 2.3 贸易伙伴关系追溯
- 识别主要贸易伙伴和依赖关系
- 量化贸易依存度（用具体数据说话）
- 分析供应中断的连锁反应

### 2.4 影响程度量化分析
- 计算供应中断对下游市场的具体影响比例
- 评估替代方案的可行性和成本
- 识别供需缺口的规模和持续时间

## 关键任务
请特别关注挖掘以下"信息差"：
1. 不为大众所知的关键商品或原材料
2. 隐藏的供应链依赖关系
3. 被忽视的贸易数据和依存关系
4. 供应中断的真实影响程度

## 输出要求
请提供详细的供应链分析，重点突出：
1. 关键发现的信息差内容
2. 量化的影响数据
3. 为第三层国内分析提供的具体线索
"""

    def _build_layer3_prompt(
        self,
        news_data: Dict[str, Any],
        research_summaries: List[str],
        layer2_result: LayerAnalysisResult
    ) -> str:
        """构建第三层分析提示词"""

        summaries_text = "\n".join([f"- {summary}" for summary in research_summaries])
        layer2_findings = "\n".join([f"- {finding}" for finding in layer2_result.key_findings])

        return f"""
# 第三层分析：聚焦国内产业与市场动态

基于前两层分析，现在聚焦国内市场的具体影响。

## 第二层关键发现
{layer2_findings}

## 研究资料
{summaries_text}

## 国内影响分析要求

### 3.1 国内受冲击行业识别
- 精确定位受外部冲击影响的国内行业
- 分析影响的直接性和间接性
- 评估行业内部的分化程度

### 3.2 供需变化与价格走势推演
- **供给端分析**：量化供给缺口或过剩情况
- **需求端分析**：评估需求变化的弹性
- **价格预期**：基于供需关系预测价格走势
- **时间框架**：区分短期、中期、长期影响

### 3.3 产业链利益传导分析
- **受益方识别**：谁将从价格变化中获利
- **受损方识别**：谁将承担成本上升压力
- **利润再分配**：分析行业内利润的重新分配
- **竞争格局变化**：评估市场份额的潜在变化

## 关键分析重点
1. 将第二层发现的供应链影响具体化到国内市场
2. 量化分析供需缺口和价格影响
3. 识别真正的受益行业和公司类型
4. 为第四层的公司筛选提供明确方向

## 输出要求
请提供详细的国内市场影响分析，包括：
1. 受影响的具体行业和细分领域
2. 供需变化的量化分析
3. 价格传导机制和预期涨幅
4. 为公司筛选提供的具体标准
"""

    def _build_layer4_prompt(
        self,
        news_data: Dict[str, Any],
        research_summaries: List[str],
        layer3_result: LayerAnalysisResult
    ) -> str:
        """构建第四层分析提示词"""

        summaries_text = "\n".join([f"- {summary}" for summary in research_summaries])
        layer3_findings = "\n".join([f"- {finding}" for finding in layer3_result.key_findings])

        return f"""
# 第四层分析：筛选与锁定具体上市公司

基于前三层分析，现在进行最终的投资标的筛选。

## 第三层关键发现
{layer3_findings}

## 研究资料
{summaries_text}

## 投资标的筛选要求

### 4.1 初步标的筛选
- 基于前三层分析，筛选相关上市公司
- 建立候选标的池（A股、港股、美股）
- 进行初步的业务相关性评估

### 4.2 精准画像与纯度分析
- **业务占比分析**：相关业务在总收入中的占比
- **产能规模评估**：在行业中的地位和市场份额
- **业绩弹性测算**：价格变化对业绩的敏感性分析
- **财务健康度**：债务水平、现金流、盈利能力

### 4.3 投资标的排序
- 建立综合评分体系
- 考虑流动性、估值水平、技术面等因素
- 提供明确的投资优先级排序

### 4.4 风险收益评估
- 量化潜在收益空间
- 识别主要风险因素
- 提供风险调整后的收益预期

## 最终输出要求
请提供：
1. **核心推荐标的**（1-3个最优选择）
   - 公司名称、股票代码
   - 推荐理由和业务相关性
   - 预期收益空间和时间框架

2. **备选标的**（2-3个次优选择）
   - 基本信息和推荐逻辑
   - 与核心标的的差异化分析

3. **风险提示**
   - 主要风险因素
   - 止损策略建议
   - 市场变化的应对预案

请确保推荐的标的具有：
- 高业务相关性（纯度高）
- 明确的受益逻辑
- 合理的风险收益比
- 良好的流动性
"""

    def _extract_key_findings(self, response: str, layer: str) -> List[str]:
        """从分析响应中提取关键发现"""
        try:
            # 简单的关键信息提取逻辑
            # 可以根据需要实现更复杂的NLP提取
            findings = []

            # 按行分割并查找关键信息
            lines = response.split('\n')
            for line in lines:
                line = line.strip()
                if any(keyword in line for keyword in ['关键', '重要', '核心', '主要', '显著']):
                    if len(line) > 10 and len(line) < 200:  # 过滤过短或过长的行
                        findings.append(line)

            # 限制数量
            return findings[:5]

        except Exception as e:
            logger.warning(f"提取关键发现失败: {e}")
            return [f"{layer}分析完成"]

    def _extract_next_layer_inputs(self, response: str, layer: str) -> Dict[str, Any]:
        """提取传递给下一层的输入信息"""
        try:
            # 提取关键数据点传递给下一层
            inputs = {
                "analysis_content": response,
                "layer": layer,
                "timestamp": "2025-06-19"  # 可以使用实际时间戳
            }

            # 根据不同层级提取不同的关键信息
            if layer == "layer1":
                inputs["event_type"] = "市场事件"
                inputs["urgency_level"] = "高"
            elif layer == "layer2":
                inputs["supply_chain_impact"] = "供应链中断"
                inputs["trade_dependency"] = "高依存度"
            elif layer == "layer3":
                inputs["domestic_sectors"] = ["化工", "能源"]
                inputs["price_impact"] = "上涨预期"

            return inputs

        except Exception as e:
            logger.warning(f"提取下层输入失败: {e}")
            return {"layer": layer}

    def _generate_final_investment_targets(
        self,
        layer4_result: LayerAnalysisResult
    ) -> List[Dict[str, Any]]:
        """生成最终投资标的列表"""
        try:
            # 从第四层分析结果中提取投资标的
            # 这里是简化版本，实际可以实现更复杂的解析逻辑

            targets = [
                {
                    "symbol": "示例股票1",
                    "name": "示例公司1",
                    "recommendation_reason": "业务高度相关，受益明确",
                    "expected_return": "15-25%",
                    "time_frame": "3-6个月",
                    "risk_level": "中等",
                    "confidence": 0.8
                },
                {
                    "symbol": "示例股票2",
                    "name": "示例公司2",
                    "recommendation_reason": "行业龙头，弹性较大",
                    "expected_return": "10-20%",
                    "time_frame": "1-3个月",
                    "risk_level": "中高",
                    "confidence": 0.7
                }
            ]

            return targets

        except Exception as e:
            logger.error(f"生成投资标的失败: {e}")
            return []

    def _calculate_overall_confidence(
        self,
        layer_results: List[LayerAnalysisResult]
    ) -> float:
        """计算整体分析置信度"""
        try:
            if not layer_results:
                return 0.5

            # 加权平均，第二层（信息差层）权重最高
            weights = [0.2, 0.4, 0.25, 0.15]  # 对应四层的权重

            total_score = 0
            total_weight = 0

            for i, result in enumerate(layer_results):
                if i < len(weights):
                    total_score += result.confidence_score * weights[i]
                    total_weight += weights[i]

            return total_score / total_weight if total_weight > 0 else 0.5

        except Exception as e:
            logger.warning(f"计算置信度失败: {e}")
            return 0.5

    def _generate_analysis_summary(
        self,
        layer1_result: LayerAnalysisResult,
        layer2_result: LayerAnalysisResult,
        layer3_result: LayerAnalysisResult,
        layer4_result: LayerAnalysisResult
    ) -> str:
        """生成分析总结"""
        try:
            summary = f"""
# 四层思维链分析总结

## 核心投资逻辑
基于四层思维链分析，我们识别出了从宏观事件到微观标的的完整投资机会链条。

## 关键信息差发现
{layer2_result.key_findings[0] if layer2_result.key_findings else '供应链分析完成'}

## 国内市场影响
{layer3_result.key_findings[0] if layer3_result.key_findings else '国内影响分析完成'}

## 投资建议
{layer4_result.key_findings[0] if layer4_result.key_findings else '标的筛选完成'}

## 整体评估
- 分析置信度：{self._calculate_overall_confidence([layer1_result, layer2_result, layer3_result, layer4_result]):.1%}
- 投资时机：当前
- 风险等级：中等
"""
            return summary

        except Exception as e:
            logger.error(f"生成分析总结失败: {e}")
            return "四层思维链分析已完成，请查看详细结果。"
