"""
四层思维链分析示例
演示如何使用新的四层思维链分析功能
"""

import asyncio
import json
from typing import Dict, Any

# 示例新闻数据
EXAMPLE_NEWS_DATA = {
    "title": "印度航空宣布缩减宽体机国际航班数量",
    "content": """
    印度航空公司今日宣布，由于运营成本上升和市场需求变化，
    将在未来6个月内缩减20%的宽体机国际航班数量。
    该决定主要影响飞往欧洲和北美的长途航线。
    公司表示这是重组计划的一部分，旨在提高运营效率和盈利能力。
    """,
    "source": "财联社",
    "publish_time": "2025-06-19 11:24:12",
    "category": "航空运输"
}

async def run_four_layer_analysis_example():
    """运行四层思维链分析示例"""
    
    try:
        # 导入深度研究引擎
        from ..core.deep_research_engine import DeepResearchEngine
        
        # 创建引擎实例
        engine = DeepResearchEngine()
        
        print("🚀 开始四层思维链深度分析示例")
        print(f"📰 分析新闻：{EXAMPLE_NEWS_DATA['title']}")
        print("-" * 60)
        
        # 执行分析
        async for result in engine.analyze_news_deep(
            news_data=EXAMPLE_NEWS_DATA,
            analysis_config={
                "use_four_layer_analysis": True,
                "max_research_loops": 1,
                "initial_search_query_count": 3
            }
        ):
            # 打印分析进度
            result_type = result.get("type", "unknown")
            message = result.get("message", "")
            
            if result_type == "task_started":
                print(f"✅ {message}")
                print(f"   任务ID: {result.get('task_id')}")
                
            elif result_type == "context_analysis":
                print(f"🔍 {message}")
                
            elif result_type == "context_completed":
                print(f"✅ {message}")
                context = result.get("context", "")
                if context:
                    print(f"   上下文: {context[:100]}...")
                    
            elif result_type == "generating_queries":
                print(f"🤖 {message}")
                
            elif result_type == "queries_generated":
                print(f"✅ {message}")
                queries = result.get("queries", [])
                for i, query in enumerate(queries, 1):
                    print(f"   查询{i}: {query.get('query', '')}")
                    
            elif result_type == "web_research_started":
                print(f"🔍 {message}")
                
            elif result_type == "search_progress":
                print(f"   🔍 {message}")
                
            elif result_type == "search_completed":
                print(f"   ✅ {message}")
                
            elif result_type == "four_layer_analysis_started":
                print(f"🧠 {message}")
                print("   开始四层思维链分析...")
                
            elif result_type == "four_layer_completed":
                print(f"✅ {message}")
                print("\n" + "="*60)
                print("📊 四层思维链分析结果")
                print("="*60)
                
                # 显示分析摘要
                summary = result.get("analysis_summary", "")
                if summary:
                    print(f"\n📋 分析摘要:")
                    print(summary)
                
                # 显示投资标的
                targets = result.get("investment_targets", [])
                if targets:
                    print(f"\n🎯 投资标的推荐:")
                    for i, target in enumerate(targets, 1):
                        print(f"   {i}. {target.get('name', '')} ({target.get('symbol', '')})")
                        print(f"      推荐理由: {target.get('recommendation_reason', '')}")
                        print(f"      预期收益: {target.get('expected_return', '')}")
                        print(f"      时间框架: {target.get('time_frame', '')}")
                        print(f"      置信度: {target.get('confidence', 0):.1%}")
                        print()
                
                confidence = result.get("confidence", 0)
                print(f"📈 整体置信度: {confidence:.1%}")
                
            elif result_type == "four_layer_fallback":
                print(f"⚠️  {message}")
                
            elif result_type == "analysis_completed":
                print(f"\n🎉 {message}")
                
                # 显示完整结果
                complete_result = result.get("result", {})
                if complete_result:
                    print(f"\n📊 完整分析结果:")
                    print(f"   任务ID: {complete_result.get('task_id')}")
                    print(f"   研究主题: {complete_result.get('research_topic')}")
                    print(f"   使用查询数: {len(complete_result.get('queries_used', []))}")
                    print(f"   研究结果数: {len(complete_result.get('research_results', []))}")
                    print(f"   完成时间: {complete_result.get('completed_at')}")
                    
                    # 如果有分析ID，显示保存信息
                    analysis_id = complete_result.get('analysis_id')
                    if analysis_id:
                        print(f"   分析ID: {analysis_id}")
                        print("   ✅ 结果已保存到数据库")
                
                break
                
            elif result_type == "error":
                print(f"❌ {message}")
                error = result.get("error", "")
                if error:
                    print(f"   错误详情: {error}")
                break
                
            elif result_type in ["llm_call_starting", "llm_processing", "llm_success"]:
                print(f"   🤖 {message}")
                
            elif result_type in ["rate_limit_info", "llm_wait"]:
                print(f"   ⏱️  {message}")
                
            else:
                # 其他类型的消息
                if message:
                    print(f"   {message}")
        
        print("\n" + "="*60)
        print("✅ 四层思维链分析示例完成")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        import traceback
        traceback.print_exc()

def print_four_layer_concept():
    """打印四层思维链概念说明"""
    
    concept = """
    🧠 四层思维链分析框架
    
    这个思维链可以概括为"由外向内、由宏观到微观、由影响到标的"的四层漏斗模型：
    
    📊 第一层：事件感知与直接联想 (宏观表层)
    ├─ 事件捕捉：快速获取重大、突发性新闻
    ├─ 直接关联：基于常识和最直接的逻辑进行判断  
    └─ 大众认知：预测市场普遍反应和热点标的
    
    🔍 第二层：深挖供应链与关键信息 (宏观深层) - 信息差关键层
    ├─ 核心主体识别：分析事件直接冲击的主体
    ├─ 供应链解构：分析其在全球经济中的角色
    ├─ 贸易关系追溯：识别主要贸易伙伴和依赖关系
    └─ 影响量化：计算供应中断的具体影响程度
    
    🏭 第三层：聚焦国内产业与市场动态 (中观层面)
    ├─ 国内行业识别：精确定位受影响的国内行业
    ├─ 供需变化推演：分析供需关系和价格走势
    └─ 利益传导分析：识别受益方和受损方
    
    🎯 第四层：筛选与锁定具体上市公司 (微观层面)
    ├─ 初步筛选：建立候选标的池
    ├─ 精准画像：分析业务占比、产能规模、业绩弹性
    ├─ 标的排序：建立综合评分体系
    └─ 风险收益评估：量化潜在收益和风险因素
    
    💡 核心优势：
    - 信息差挖掘：第二层是获得超额收益的关键
    - 逻辑完整性：从宏观事件到微观标的的完整链条
    - 避开红海：识别大众认知盲点，寻找蓝海机会
    - 精准筛选：多维度筛选最优投资标的
    """
    
    print(concept)

if __name__ == "__main__":
    print_four_layer_concept()
    print("\n" + "="*60)
    print("🚀 开始运行四层思维链分析示例")
    print("="*60)
    
    # 运行示例
    asyncio.run(run_four_layer_analysis_example())
