"""
深度研究状态管理器

从deepreaserch模块适配的状态管理，专门用于新闻深度分析
"""

from __future__ import annotations
from typing import TypedDict, List, Dict, Any, Optional
from langgraph.graph import add_messages
from typing_extensions import Annotated
import operator


class ResearchState(TypedDict):
    """深度研究主状态"""
    messages: Annotated[list, add_messages]
    news_data: Dict[str, Any]  # 新闻数据
    search_query: Annotated[list, operator.add]
    web_research_result: Annotated[list, operator.add]
    sources_gathered: Annotated[list, operator.add]
    initial_search_query_count: int
    max_research_loops: int
    research_loop_count: int
    reasoning_model: str
    analysis_focus: str  # 分析重点：'market_impact', 'technical_analysis', 'fundamental_analysis'


class ReflectionState(TypedDict):
    """反思状态"""
    is_sufficient: bool
    knowledge_gap: str
    follow_up_queries: Annotated[list, operator.add]
    research_loop_count: int
    number_of_ran_queries: int


class QueryGenerationState(TypedDict):
    """查询生成状态"""
    search_query: List[Dict[str, str]]  # [{"query": "...", "rationale": "..."}]


class WebSearchState(TypedDict):
    """网络搜索状态"""
    search_query: str
    id: str


class NewsAnalysisState(TypedDict):
    """新闻分析专用状态"""
    news_id: Optional[int]
    news_title: str
    news_content: str
    news_source: str
    news_publish_time: str
    analysis_model: str  # 'gemini-2.0-flash', 'gemini-2.5-flash', etc.
    analysis_type: str   # 'quick', 'deep', 'comprehensive'
    market_focus: List[str]  # ['US', 'A_SHARE', 'HK', 'CRYPTO']
    

class ResearchConfiguration:
    """研究配置管理"""
    
    def __init__(self):
        '''由于 api 限制,只能先全部使用 2.0-flash'''
        self.query_generator_model = "gemini-2.5-flash-lite-preview-06-17"
        self.reflection_model = "gemini-2.5-flash-lite-preview-06-17" 
        self.answer_model = "gemini-2.5-flash-lite-preview-06-17"
        self.number_of_initial_queries = 3
        self.max_research_loops = 2
        
        # 新增详细状态配置
        self.enable_detailed_status = True  # 启用详细状态输出
        self.status_output_interval = 1.0   # 状态输出间隔（秒）
        self.llm_call_timeout = 60         # LLM调用超时时间（秒）
        
    def for_news_analysis(self, news_type: str = "financial") -> Dict[str, Any]:
        """为新闻分析定制配置"""
        config = {
            "query_generator_model": self.query_generator_model,
            "reflection_model": self.reflection_model,
            "answer_model": self.answer_model,
            "number_of_initial_queries": 2 if news_type == "financial" else 3,
            "max_research_loops": 1 if news_type == "financial" else 2,
            "enable_detailed_status": self.enable_detailed_status,
            "status_output_interval": self.status_output_interval,
            "llm_call_timeout": self.llm_call_timeout,
        }
        return config
    
    def for_market_analysis(self) -> Dict[str, Any]:
        """为市场分析定制配置"""
        return {
            "query_generator_model": self.query_generator_model,
            "reflection_model": self.reflection_model, 
            "answer_model": self.answer_model,
            "number_of_initial_queries": 3,
            "max_research_loops": 2,
            "analysis_focus": "market_impact",
            "enable_detailed_status": self.enable_detailed_status,
            "status_output_interval": self.status_output_interval,
            "llm_call_timeout": self.llm_call_timeout,
        }


def create_initial_research_state(
    news_data: Dict[str, Any], 
    config: Optional[Dict[str, Any]] = None
) -> ResearchState:
    """创建初始研究状态"""
    
    default_config = ResearchConfiguration().for_news_analysis()
    if config:
        default_config.update(config)
    
    return ResearchState(
        messages=[],
        news_data=news_data,
        search_query=[],
        web_research_result=[],
        sources_gathered=[],
        initial_search_query_count=default_config["number_of_initial_queries"],
        max_research_loops=default_config["max_research_loops"],
        research_loop_count=0,
        reasoning_model=default_config["reflection_model"],
        analysis_focus=default_config.get("analysis_focus", "comprehensive")
    )


def create_news_analysis_state(
    news_data: Dict[str, Any],
    analysis_config: Optional[Dict[str, Any]] = None
) -> NewsAnalysisState:
    """创建新闻分析状态"""
    
    config = analysis_config or {}
    
    return NewsAnalysisState(
        news_id=news_data.get("id"),
        news_title=news_data.get("title", ""),
        news_content=news_data.get("content", ""),
        news_source=news_data.get("source", ""),
        news_publish_time=news_data.get("publish_time", ""),
        analysis_model=config.get("model", "gemini-2.0-flash"),
        analysis_type=config.get("type", "deep"),
        market_focus=config.get("market_focus", ["US", "A_SHARE", "HK"])
    ) 