"""
深度研究数据管理器

管理深度研究分析结果的存储、查询和更新
"""

import json
import sqlite3
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class DeepResearchDataManager:
    """深度研究数据管理器"""
    
    def __init__(self, db_path: str = "data/news_impact_analysis.db"):
        self.db_path = db_path
        
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def save_analysis_result(
        self,
        task_id: str,
        news_data: Dict[str, Any],
        research_topic: str,
        queries: List[Dict[str, str]],
        research_results: List[Dict[str, Any]],
        final_analysis: Dict[str, Any],
        sources: List[Dict[str, Any]],
        analysis_context: Dict[str, Any],
        status: str = "completed",
        processing_time: int = 0,
        error_message: Optional[str] = None
    ) -> int:
        """
        保存分析结果到数据库
        
        Args:
            task_id: 任务ID
            news_data: 新闻数据
            research_topic: 研究主题
            queries: 研究查询列表
            research_results: 研究结果列表
            final_analysis: 最终分析结果
            sources: 来源列表
            analysis_context: 分析上下文
            status: 任务状态
            processing_time: 处理时间（秒）
            error_message: 错误信息
            
        Returns:
            分析记录ID
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 准备数据
                news_id = news_data.get('id')
                news_title = news_data.get('title', '')
                news_content = news_data.get('content', '')
                news_source = news_data.get('source', '')
                news_publish_time = news_data.get('publish_time', '')
                
                # 确定优先级
                priority_level = analysis_context.get('analysis_priority', 'medium')
                analysis_type = analysis_context.get('analysis_depth', 'deep')
                
                # 序列化JSON数据
                queries_json = json.dumps(queries, ensure_ascii=False)
                results_json = json.dumps(research_results, ensure_ascii=False)
                sources_json = json.dumps(sources, ensure_ascii=False)
                context_json = json.dumps(analysis_context, ensure_ascii=False)
                
                # 处理引用数据
                citations = final_analysis.get('citations', [])
                citations_json = json.dumps(citations, ensure_ascii=False)
                
                # 插入记录
                cursor.execute("""
                    INSERT INTO news_deep_research (
                        task_id, news_id, news_title, news_content, news_source,
                        news_publish_time, research_topic, research_queries,
                        research_results, final_analysis, sources_gathered,
                        citations, analysis_context, research_status,
                        priority_level, analysis_type, processing_time_seconds,
                        error_message, completed_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    task_id, news_id, news_title, news_content, news_source,
                    news_publish_time, research_topic, queries_json,
                    results_json, final_analysis.get('analysis', ''),
                    sources_json, citations_json, context_json,
                    status, priority_level, analysis_type, processing_time,
                    error_message, datetime.now().isoformat() if status == 'completed' else None
                ))
                
                analysis_id = cursor.lastrowid
                conn.commit()
                
                # 更新统计信息
                self._update_statistics(status == 'completed', processing_time)
                
                logger.info(f"深度分析结果已保存: task_id={task_id}, analysis_id={analysis_id}")
                return analysis_id
                
        except Exception as e:
            logger.error(f"保存深度分析结果失败: {e}")
            raise
    
    def update_analysis_status(
        self,
        task_id: str,
        status: str,
        error_message: Optional[str] = None
    ) -> bool:
        """
        更新分析状态
        
        Args:
            task_id: 任务ID
            status: 新状态
            error_message: 错误信息
            
        Returns:
            是否更新成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if status == 'completed':
                    cursor.execute("""
                        UPDATE news_deep_research 
                        SET research_status = ?, completed_at = ?
                        WHERE task_id = ?
                    """, (status, datetime.now().isoformat(), task_id))
                else:
                    cursor.execute("""
                        UPDATE news_deep_research 
                        SET research_status = ?, error_message = ?
                        WHERE task_id = ?
                    """, (status, error_message, task_id))
                
                rows_affected = cursor.rowcount
                conn.commit()
                
                return rows_affected > 0
                
        except Exception as e:
            logger.error(f"更新分析状态失败: {e}")
            return False
    
    def get_analysis_by_task_id(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        根据任务ID获取分析结果
        
        Args:
            task_id: 任务ID
            
        Returns:
            分析结果或None
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT * FROM news_deep_research 
                    WHERE task_id = ? 
                    ORDER BY created_at DESC 
                    LIMIT 1
                """, (task_id,))
                
                result = cursor.fetchone()
                
                if result:
                    return self._parse_analysis_result(dict(result))
                
                return None
                
        except Exception as e:
            logger.error(f"获取分析结果失败: {e}")
            return None
    
    def get_recent_analyses(
        self,
        limit: int = 20,
        status: Optional[str] = None,
        priority: Optional[str] = None,
        days_back: int = 7
    ) -> List[Dict[str, Any]]:
        """
        获取最近的分析结果
        
        Args:
            limit: 限制数量
            status: 状态过滤
            priority: 优先级过滤
            days_back: 回溯天数
            
        Returns:
            分析结果列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建查询条件
                where_conditions = [f"created_at >= datetime('now', '-{days_back} days')"]
                params = []
                
                if status:
                    where_conditions.append("research_status = ?")
                    params.append(status)
                
                if priority:
                    where_conditions.append("priority_level = ?")
                    params.append(priority)
                
                where_clause = " AND ".join(where_conditions)
                
                query = f"""
                    SELECT 
                        id, task_id, news_title, research_topic, research_status,
                        priority_level, analysis_type, created_at, completed_at,
                        processing_time_seconds, error_message,
                        json_array_length(sources_gathered) as sources_count,
                        json_array_length(research_queries) as queries_count
                    FROM news_deep_research 
                    WHERE {where_clause}
                    ORDER BY created_at DESC 
                    LIMIT ?
                """
                
                params.append(limit)
                cursor.execute(query, params)
                results = cursor.fetchall()
                
                return [dict(row) for row in results]
                
        except Exception as e:
            logger.error(f"获取最近分析结果失败: {e}")
            return []
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """获取分析统计信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 基本统计
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_analyses,
                        SUM(CASE WHEN research_status = 'completed' THEN 1 ELSE 0 END) as completed,
                        SUM(CASE WHEN research_status = 'failed' THEN 1 ELSE 0 END) as failed,
                        SUM(CASE WHEN research_status = 'running' THEN 1 ELSE 0 END) as running,
                        AVG(CASE WHEN research_status = 'completed' THEN processing_time_seconds ELSE NULL END) as avg_processing_time
                    FROM news_deep_research 
                    WHERE created_at >= datetime('now', '-30 days')
                """)
                
                stats = dict(cursor.fetchone())
                
                # 按优先级统计
                cursor.execute("""
                    SELECT priority_level, COUNT(*) as count
                    FROM news_deep_research 
                    WHERE created_at >= datetime('now', '-30 days')
                    GROUP BY priority_level
                """)
                
                priority_stats = {row['priority_level']: row['count'] for row in cursor.fetchall()}
                
                # 按状态统计
                cursor.execute("""
                    SELECT research_status, COUNT(*) as count
                    FROM news_deep_research 
                    WHERE created_at >= datetime('now', '-30 days')
                    GROUP BY research_status
                """)
                
                status_stats = {row['research_status']: row['count'] for row in cursor.fetchall()}
                
                return {
                    'basic_stats': stats,
                    'priority_distribution': priority_stats,
                    'status_distribution': status_stats,
                    'period': '30 days'
                }
                
        except Exception as e:
            logger.error(f"获取分析统计信息失败: {e}")
            return {}
    
    def cleanup_old_analyses(self, days: int = 30) -> int:
        """
        清理旧的分析数据
        
        Args:
            days: 保留天数
            
        Returns:
            删除的记录数
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 只删除已完成且非高优先级的分析
                cursor.execute("""
                    DELETE FROM news_deep_research 
                    WHERE research_status = 'completed' 
                      AND priority_level != 'high' 
                      AND completed_at < datetime('now', '-{} days')
                """.format(days))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"清理了 {deleted_count} 条旧的深度分析记录")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理旧分析数据失败: {e}")
            return 0
    
    def _parse_analysis_result(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """解析分析结果中的JSON字段"""
        json_fields = [
            'research_queries', 'research_results', 
            'sources_gathered', 'citations', 'analysis_context'
        ]
        
        for field in json_fields:
            if result.get(field):
                try:
                    result[field] = json.loads(result[field])
                except json.JSONDecodeError:
                    result[field] = [] if field != 'analysis_context' else {}
        
        return result
    
    def _update_statistics(self, success: bool, processing_time: int):
        """更新统计信息"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                today = datetime.now().date().isoformat()
                
                # 更新或插入今日统计
                cursor.execute("""
                    INSERT OR IGNORE INTO deep_research_stats 
                    (date, total_analyses, successful_analyses, failed_analyses, avg_processing_time)
                    VALUES (?, 0, 0, 0, 0.0)
                """, (today,))
                
                if success:
                    cursor.execute("""
                        UPDATE deep_research_stats 
                        SET total_analyses = total_analyses + 1,
                            successful_analyses = successful_analyses + 1,
                            avg_processing_time = (avg_processing_time * (successful_analyses - 1) + ?) / successful_analyses
                        WHERE date = ?
                    """, (processing_time, today))
                else:
                    cursor.execute("""
                        UPDATE deep_research_stats 
                        SET total_analyses = total_analyses + 1,
                            failed_analyses = failed_analyses + 1
                        WHERE date = ?
                    """, (today,))
                
                conn.commit()
                
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")


# 全局数据管理器实例
_data_manager_instance = None

def get_deep_research_data_manager() -> DeepResearchDataManager:
    """获取深度研究数据管理器实例"""
    global _data_manager_instance
    if _data_manager_instance is None:
        _data_manager_instance = DeepResearchDataManager()
    return _data_manager_instance 