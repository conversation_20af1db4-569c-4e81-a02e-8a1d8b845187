# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

import logging
from pathlib import Path
from typing import Any, Dict, List

from langchain_openai import ChatOpenAI
from langchain_core.messages import BaseMessage, HumanMessage

from ..config import load_yaml_config
from ..config.agents import LLMType

logger = logging.getLogger(__name__)

# Cache for LLM instances
_llm_cache: dict[LLMType, ChatOpenAI] = {}


def truncate_content(content: str, max_chars: int = 15000) -> str:
    """
    截断过长的内容，考虑API token限制
    
    Args:
        content: 原始内容
        max_chars: 最大字符数 (降低到15000以适应token限制)
        
    Returns:
        截断后的内容
    """
    if len(content) <= max_chars:
        return content
    
    # 更保守的截断策略，保留更多开头内容用于上下文
    begin_portion = int(max_chars * 0.7)  # 70%用于开头
    end_portion = max_chars - begin_portion - 100  # 预留100字符用于截断标识
    
    truncated = (content[:begin_portion] + 
                f"\n\n[...内容过长，已截断约{len(content) - max_chars}个字符...]\n\n" + 
                content[-end_portion:])
    logger.warning(f"内容过长({len(content)}字符)，已截断到{len(truncated)}字符")
    return truncated


def validate_messages(messages: List[BaseMessage]) -> List[BaseMessage]:
    """
    验证消息列表，确保所有消息都有有效的content且长度合理
    
    Args:
        messages: 消息列表
        
    Returns:
        验证后的消息列表
    """
    validated_messages = []
    
    for i, msg in enumerate(messages):
        if hasattr(msg, 'content'):
            if msg.content is None:
                logger.warning(f"Message {i} has null content, replacing with default")
                # 创建同类型的消息，但使用默认内容
                new_msg = type(msg)(content="默认消息内容")
                if hasattr(msg, 'name'):
                    new_msg.name = msg.name
                validated_messages.append(new_msg)
            elif isinstance(msg.content, str) and msg.content.strip() == "":
                logger.warning(f"Message {i} has empty content, replacing with default")
                new_msg = type(msg)(content="默认消息内容")
                if hasattr(msg, 'name'):
                    new_msg.name = msg.name
                validated_messages.append(new_msg)
            else:
                # 检查内容长度并截断过长的内容 (更严格的限制)
                content = msg.content
                if isinstance(content, str) and len(content) > 15000:
                    content = truncate_content(content, max_chars=15000)
                    # 创建新消息对象
                    new_msg = type(msg)(content=content)
                    if hasattr(msg, 'name'):
                        new_msg.name = msg.name
                    validated_messages.append(new_msg)
                else:
                    validated_messages.append(msg)
        else:
            logger.warning(f"Message {i} has no content attribute")
            validated_messages.append(msg)
    
    return validated_messages


class FailoverLLMWrapper(ChatOpenAI):
    """
    支持故障转移的LLM包装器
    当主LLM失败时，自动切换到备用LLM
    """
    
    def __init__(self, primary_llm: ChatOpenAI, backup_llms: List[ChatOpenAI] = None):
        # 使用主LLM的配置初始化
        super().__init__(
            model=primary_llm.model_name,
            api_key=primary_llm.openai_api_key,
            base_url=getattr(primary_llm, 'openai_api_base', None),
            temperature=getattr(primary_llm, 'temperature', 0.7),
        )
        # 使用object.__setattr__绕过Pydantic的字段验证
        object.__setattr__(self, 'primary_llm', primary_llm)
        object.__setattr__(self, 'backup_llms', backup_llms or [])
        object.__setattr__(self, 'current_llm_index', 0)  # 0表示使用主LLM
        
    def _get_current_llm(self) -> ChatOpenAI:
        """获取当前使用的LLM"""
        current_index = getattr(self, 'current_llm_index', 0)
        primary_llm = getattr(self, 'primary_llm', None)
        backup_llms = getattr(self, 'backup_llms', [])
        
        if current_index == 0:
            return primary_llm
        else:
            backup_index = current_index - 1
            if backup_index < len(backup_llms):
                return backup_llms[backup_index]
            else:
                # 如果所有备用都用完了，回到主LLM
                object.__setattr__(self, 'current_llm_index', 0)
                return primary_llm
    
    def _try_next_llm(self):
        """切换到下一个LLM"""
        new_index = getattr(self, 'current_llm_index', 0) + 1
        backup_llms = getattr(self, 'backup_llms', [])
        if new_index > len(backup_llms):
            new_index = 0  # 回到主LLM
        object.__setattr__(self, 'current_llm_index', new_index)
        
        current_llm = self._get_current_llm()
        logger.info(f"切换到LLM: {getattr(current_llm, 'model_name', 'unknown')}")
    
    def invoke(self, input, config=None, **kwargs):
        """带故障转移的invoke方法"""
        # 验证消息内容
        if isinstance(input, list):
            validated_input = validate_messages(input)
        else:
            validated_input = input
        
        # 尝试所有可用的LLM
        backup_llms = getattr(self, 'backup_llms', [])
        max_attempts = len(backup_llms) + 1  # 主LLM + 所有备用LLM
        
        for attempt in range(max_attempts):
            current_llm = self._get_current_llm()
            
            try:
                logger.debug(f"尝试使用LLM: {getattr(current_llm, 'model_name', 'unknown')} (attempt {attempt + 1})")
                result = current_llm.invoke(validated_input, config=config, **kwargs)
                
                # 如果成功，记录并返回结果
                if attempt > 0:
                    logger.info(f"成功使用备用LLM: {getattr(current_llm, 'model_name', 'unknown')}")
                
                return result
                
            except Exception as e:
                logger.warning(f"LLM调用失败 ({getattr(current_llm, 'model_name', 'unknown')}): {e}")
                
                if attempt < max_attempts - 1:  # 不是最后一次尝试
                    self._try_next_llm()
                else:
                    # 所有LLM都失败了，抛出最后的异常
                    logger.error("所有LLM都不可用，调用失败")
                    raise e
    
    def bind_tools(self, tools, **kwargs):
        """支持bind_tools方法"""
        current_llm = self._get_current_llm()
        bound_llm = current_llm.bind_tools(tools, **kwargs)
        
        # 创建新的FailoverLLMWrapper来包装bound后的LLM
        # 对于bind_tools，我们只使用当前LLM，不进行故障转移
        return bound_llm


class SafeLLMWrapper(FailoverLLMWrapper):
    """
    LLM的安全包装器，结合了消息验证和故障转移功能
    继承自FailoverLLMWrapper以确保与LangGraph兼容
    """
    
    def __init__(self, primary_llm: ChatOpenAI, backup_llms: List[ChatOpenAI] = None):
        super().__init__(primary_llm, backup_llms)


def _create_llm_use_conf(llm_type: LLMType, conf: Dict[str, Any]) -> ChatOpenAI:
    llm_type_map = {
        "reasoning": conf.get("REASONING_MODEL"),
        "basic": conf.get("BASIC_MODEL"),
        "vision": conf.get("VISION_MODEL"),
    }
    llm_conf = llm_type_map.get(llm_type)
    if not llm_conf:
        raise ValueError(f"Unknown LLM type: {llm_type}")
    if not isinstance(llm_conf, dict):
        raise ValueError(f"Invalid LLM Conf: {llm_type}")
    return ChatOpenAI(**llm_conf)


def _create_all_llms() -> List[ChatOpenAI]:
    """创建所有可用LLM配置列表，按优先级排序"""
    import os
    
    all_llms = []
    
    # 1. SiliconFlow Qwen模型 (优先级最高)
    siliconflow_api_key = os.getenv("SILICONFLOW_API_KEY", "sk-slylcdnwlypzwrxwzemwrtmaqzeueuymqlhlxonxyuvgxila")
    if siliconflow_api_key:
        all_llms.append(ChatOpenAI(
            model="Qwen/Qwen3-8B",
            api_key=siliconflow_api_key,
            base_url="https://api.siliconflow.cn/v1",
            temperature=0.7,
            max_tokens=2048,  # 降低输出长度以适应总token限制
        ))
    
    # 2. Gemini配置 (备用)
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    if gemini_api_key:
        all_llms.append(ChatOpenAI(
            model=os.getenv("GEMINI_MODEL", "gemini-2.5-flash-lite-preview-06-17"),
            api_key=gemini_api_key,
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
            temperature=0.7,
        ))
    
    # 3. OpenAI配置 (最后备用)
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if openai_api_key:
        all_llms.append(ChatOpenAI(
            model="gpt-4o-mini",
            api_key=openai_api_key,
            base_url=os.getenv("OPENAI_BASE_URL"),
            temperature=0.7,
        ))
    
    return all_llms


def _create_backup_llms() -> List[ChatOpenAI]:
    """创建备用LLM配置列表（兼容性函数）"""
    return _create_all_llms()


def get_llm_by_type(
    llm_type: LLMType,
) -> SafeLLMWrapper:
    """
    Get LLM instance by type with failover support. Returns cached instance if available.
    """
    if llm_type in _llm_cache:
        return _llm_cache[llm_type]

    primary_llm = None
    backup_llms = []
    
    # 获取所有可用的LLM配置
    all_llms = _create_all_llms()
    
    # 尝试从配置文件加载主LLM（仅在没有环境变量LLM时才使用）
    config_llm = None
    try:
        conf = load_yaml_config(
            str((Path(__file__).parent.parent.parent.parent / "conf.yaml").resolve())
        )
        config_llm = _create_llm_use_conf(llm_type, conf)
        logger.info(f"配置文件LLM可用: {getattr(config_llm, 'model_name', 'unknown')}")
    except Exception as e:
        logger.info(f"配置文件加载失败: {e}，将使用环境变量配置")
    
    # 确定主LLM：优先使用环境变量LLM，其次使用配置文件LLM
    if all_llms:
        # 使用第一个环境变量LLM作为主LLM（SiliconFlow优先）
        primary_llm = all_llms.pop(0)
        backup_llms = all_llms  # 剩余的作为备用
        
        # 如果有配置文件LLM，添加到备用列表的最后
        if config_llm:
            backup_llms.append(config_llm)
            
        logger.info(f"使用环境变量LLM作为主LLM: {getattr(primary_llm, 'model_name', 'unknown')}")
    elif config_llm:
        # 只有配置文件LLM可用
        primary_llm = config_llm
        backup_llms = []
        logger.info(f"使用配置文件LLM作为主LLM: {getattr(primary_llm, 'model_name', 'unknown')}")
    else:
        # 没有任何LLM配置，创建默认配置
        logger.warning("创建默认LLM配置")
        primary_llm = ChatOpenAI(
            model="gpt-4o-mini",
            api_key="dummy-key",  # 占位符
            temperature=0.7,
        )
        backup_llms = []
    
    # 用SafeLLMWrapper包装LLM，传入主LLM和备用LLM
    safe_llm = SafeLLMWrapper(primary_llm, backup_llms)
    _llm_cache[llm_type] = safe_llm
    
    logger.info(f"LLM配置完成 - 主LLM: {getattr(primary_llm, 'model_name', 'unknown')}, 备用LLM数量: {len(backup_llms)}")
    
    return safe_llm


# Initialize LLMs for different purposes - now these will be cached
basic_llm = get_llm_by_type("basic")

# In the future, we will use reasoning_llm and vl_llm for different purposes
# reasoning_llm = get_llm_by_type("reasoning")
# vl_llm = get_llm_by_type("vision")


if __name__ == "__main__":
    print(basic_llm.invoke("Hello")) 