version: '3.8'

services:
  cash-flow:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: cash-flow-app
    restart: unless-stopped
    ports:
      - "80:80"
    volumes:
      # 数据持久化 - 挂载数据库文件目录
      - ./backend/core/data/storage/databases:/app/backend/core/data/storage/databases
      # 日志持久化
      - cash-flow-logs:/var/log
      - ./backend/logs:/app/backend/logs
      # 配置文件挂载 (可选，用于动态配置更新)
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
    environment:
      # 从环境文件加载配置
      - APP_ENV=production
      - LOG_LEVEL=INFO
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - cash-flow-network

volumes:
  # 日志数据卷
  cash-flow-logs:
    driver: local

networks:
  # 应用网络
  cash-flow-network:
    driver: bridge 