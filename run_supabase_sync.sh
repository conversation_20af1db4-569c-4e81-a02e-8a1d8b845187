#!/bin/bash

# ================================
# Supabase新闻同步脚本
# ================================

# 设置错误时退出
set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_blue() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

# 检查虚拟环境
check_venv() {
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        log_warn "未检测到虚拟环境，尝试激活..."
        if [ -f "venv/bin/activate" ]; then
            source venv/bin/activate
            log_info "虚拟环境已激活"
        else
            log_error "未找到虚拟环境，请先运行: python -m venv venv"
            exit 1
        fi
    else
        log_info "虚拟环境已激活: $VIRTUAL_ENV"
    fi
}

# 检查依赖
check_dependencies() {
    log_blue "检查Python依赖..."
    
    # 检查关键依赖
    python -c "import supabase, akshare, pandas, dotenv" 2>/dev/null || {
        log_warn "缺少必要依赖，正在安装..."
        pip install -r requirements.txt
        log_info "依赖安装完成"
    }
    
    log_info "依赖检查通过"
}

# 检查环境变量
check_env_vars() {
    log_blue "检查环境变量配置..."
    
    if [ ! -f ".env" ]; then
        log_error "未找到.env文件，请复制env.example为.env并配置Supabase参数"
        exit 1
    fi
    
    # 加载环境变量
    source .env
    
    if [ -z "$SUPABASE_URL" ] || [ -z "$SUPABASE_KEY" ]; then
        log_error "请在.env文件中设置SUPABASE_URL和SUPABASE_KEY"
        exit 1
    fi
    
    log_info "环境变量配置正确"
}

# 运行同步任务
run_sync() {
    log_blue "开始执行Supabase新闻同步任务..."
    
    python -m backend.services.tasks.supabase_news_sync
    
    if [ $? -eq 0 ]; then
        log_info "新闻同步任务执行完成"
    else
        log_error "新闻同步任务执行失败"
        exit 1
    fi
}

# 显示帮助信息
show_help() {
    echo "Supabase新闻同步脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -c, --check    仅检查环境配置，不执行同步"
    echo "  -s, --sync     执行一次性新闻同步（默认）"
    echo ""
    echo "示例:"
    echo "  $0              # 执行新闻同步"
    echo "  $0 --check     # 仅检查环境"
    echo ""
}

# 主函数
main() {
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--check)
            log_blue "执行环境检查模式..."
            check_venv
            check_dependencies
            check_env_vars
            log_info "环境检查完成，所有配置正确"
            exit 0
            ;;
        -s|--sync|"")
            log_blue "执行新闻同步模式..."
            check_venv
            check_dependencies
            check_env_vars
            run_sync
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本开始
echo "================================"
echo "  Supabase新闻同步工具"
echo "================================"
echo ""

# 执行主函数
main "$@"