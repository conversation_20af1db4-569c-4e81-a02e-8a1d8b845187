#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财经新闻系统测试运行器
运行后端和前端的所有测试
"""

import asyncio
import subprocess
import sys
import os
import json
import time
from datetime import datetime
from typing import Dict, Any

def print_banner(title: str):
    """打印测试标题横幅"""
    print("\n" + "=" * 80)
    print(f"🧪 {title}")
    print("=" * 80)

def print_section(title: str):
    """打印测试章节标题"""
    print(f"\n📋 {title}")
    print("-" * 60)

async def run_backend_tests():
    """运行后端测试"""
    print_section("后端功能测试")
    
    try:
        # 运行后端测试脚本
        process = await asyncio.create_subprocess_exec(
            sys.executable, 'backend/tests/test_financial_news_system.py',
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=os.getcwd()
        )
        
        stdout, stderr = await process.communicate()
        
        # 输出测试结果
        if stdout:
            print(stdout.decode('utf-8'))
        
        if stderr:
            print("错误输出:")
            print(stderr.decode('utf-8'))
        
        return {
            'success': process.returncode == 0,
            'return_code': process.returncode,
            'stdout': stdout.decode('utf-8') if stdout else '',
            'stderr': stderr.decode('utf-8') if stderr else ''
        }
        
    except Exception as e:
        print(f"❌ 后端测试运行失败: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def check_server_status():
    """检查服务器状态"""
    print_section("服务器状态检查")
    
    try:
        import requests
        
        # 检查后端服务器
        try:
            response = requests.get('http://localhost:8000/health', timeout=5)
            if response.status_code == 200:
                print("✅ 后端服务器运行正常")
                return True
            else:
                print(f"❌ 后端服务器响应异常: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ 无法连接到后端服务器: {e}")
            print("💡 请确保后端服务器正在运行 (python backend/server.py)")
            return False
            
    except ImportError:
        print("⚠️  requests库未安装，跳过服务器状态检查")
        print("💡 可以运行: pip install requests")
        return True

def run_frontend_tests():
    """运行前端测试"""
    print_section("前端功能测试")
    
    # 检查Node.js是否可用
    try:
        result = subprocess.run(['node', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            print("❌ Node.js不可用，跳过前端测试")
            return {'success': False, 'error': 'Node.js not available'}
        
        print(f"✅ Node.js版本: {result.stdout.strip()}")
        
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ Node.js不可用，跳过前端测试")
        return {'success': False, 'error': 'Node.js not found'}
    
    try:
        # 运行前端测试
        result = subprocess.run([
            'node', 'frontend/tests/test_frontend_features.js'
        ], capture_output=True, text=True, timeout=60, cwd=os.getcwd())
        
        # 输出测试结果
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return {
            'success': result.returncode == 0,
            'return_code': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr
        }
        
    except subprocess.TimeoutExpired:
        print("❌ 前端测试超时")
        return {'success': False, 'error': 'Test timeout'}
    except Exception as e:
        print(f"❌ 前端测试运行失败: {e}")
        return {'success': False, 'error': str(e)}

def run_integration_tests():
    """运行集成测试"""
    print_section("集成测试")
    
    try:
        import requests
        
        # 测试完整的工作流程
        tests = []
        
        # 1. 测试获取新闻列表
        try:
            response = requests.get('http://localhost:8000/financial-news/latest?limit=5', timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('news'):
                    tests.append(('获取新闻列表', True, f"成功获取 {len(data['news'])} 条新闻"))
                else:
                    tests.append(('获取新闻列表', False, '响应格式异常'))
            else:
                tests.append(('获取新闻列表', False, f'HTTP {response.status_code}'))
        except Exception as e:
            tests.append(('获取新闻列表', False, str(e)))
        
        # 2. 测试系统统计
        try:
            response = requests.get('http://localhost:8000/financial-news/statistics', timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('statistics'):
                    stats = data['statistics']
                    tests.append(('系统统计', True, f"总新闻: {stats.get('total_news', 0)}"))
                else:
                    tests.append(('系统统计', False, '响应格式异常'))
            else:
                tests.append(('系统统计', False, f'HTTP {response.status_code}'))
        except Exception as e:
            tests.append(('系统统计', False, str(e)))
        
        # 3. 测试缓存状态
        try:
            response = requests.get('http://localhost:8000/cache/stats', timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('cache_stats'):
                    cache_stats = data['cache_stats']
                    hit_rate = cache_stats.get('performance', {}).get('hit_rate_percent', 0)
                    tests.append(('缓存系统', True, f"命中率: {hit_rate}%"))
                else:
                    tests.append(('缓存系统', False, '响应格式异常'))
            else:
                tests.append(('缓存系统', False, f'HTTP {response.status_code}'))
        except Exception as e:
            tests.append(('缓存系统', False, str(e)))
        
        # 输出集成测试结果
        passed = 0
        failed = 0
        
        for test_name, success, message in tests:
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{status} {test_name}: {message}")
            if success:
                passed += 1
            else:
                failed += 1
        
        total = passed + failed
        success_rate = (passed / total * 100) if total > 0 else 0
        
        print(f"\n集成测试结果: {passed}/{total} 通过 ({success_rate:.1f}%)")
        
        return {
            'success': failed == 0,
            'passed': passed,
            'failed': failed,
            'total': total,
            'success_rate': success_rate
        }
        
    except ImportError:
        print("⚠️  requests库未安装，跳过集成测试")
        return {'success': False, 'error': 'requests not available'}

def generate_test_report(backend_result: Dict[str, Any], 
                        frontend_result: Dict[str, Any],
                        integration_result: Dict[str, Any]):
    """生成测试报告"""
    print_section("测试报告生成")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'backend_tests': backend_result,
        'frontend_tests': frontend_result,
        'integration_tests': integration_result,
        'summary': {
            'backend_success': backend_result.get('success', False),
            'frontend_success': frontend_result.get('success', False),
            'integration_success': integration_result.get('success', False),
            'overall_success': all([
                backend_result.get('success', False),
                frontend_result.get('success', False),
                integration_result.get('success', False)
            ])
        }
    }
    
    # 保存测试报告
    try:
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 测试报告已保存: {report_file}")
        
    except Exception as e:
        print(f"❌ 保存测试报告失败: {e}")
    
    return report

async def main():
    """主函数"""
    print_banner("财经新闻系统 - 完整功能测试套件")
    
    start_time = time.time()
    
    # 1. 检查服务器状态
    server_ok = check_server_status()
    
    # 2. 运行后端测试
    backend_result = await run_backend_tests()
    
    # 3. 运行前端测试（如果服务器正常）
    if server_ok:
        frontend_result = run_frontend_tests()
        integration_result = run_integration_tests()
    else:
        frontend_result = {'success': False, 'error': 'Server not available'}
        integration_result = {'success': False, 'error': 'Server not available'}
    
    # 4. 生成测试报告
    report = generate_test_report(backend_result, frontend_result, integration_result)
    
    # 5. 输出最终结果
    end_time = time.time()
    duration = end_time - start_time
    
    print_banner("测试完成")
    print(f"⏱️  总耗时: {duration:.2f} 秒")
    print(f"🔧 后端测试: {'✅ 通过' if backend_result.get('success') else '❌ 失败'}")
    print(f"🌐 前端测试: {'✅ 通过' if frontend_result.get('success') else '❌ 失败'}")
    print(f"🔗 集成测试: {'✅ 通过' if integration_result.get('success') else '❌ 失败'}")
    print(f"📊 整体结果: {'✅ 全部通过' if report['summary']['overall_success'] else '❌ 存在失败'}")
    
    # 设置退出码
    exit_code = 0 if report['summary']['overall_success'] else 1
    
    if exit_code != 0:
        print("\n💡 故障排除建议:")
        if not backend_result.get('success'):
            print("  - 检查后端依赖是否正确安装")
            print("  - 检查数据库连接和API配置")
        if not frontend_result.get('success'):
            print("  - 确保后端服务器正在运行")
            print("  - 检查API端点是否可访问")
        if not integration_result.get('success'):
            print("  - 检查服务器网络连接")
            print("  - 验证API响应格式")
    
    return exit_code

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行器异常: {e}")
        sys.exit(1)
