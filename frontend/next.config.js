/** @type {import('next').NextConfig} */
const nextConfig = {
  // 静态导出配置 (用于Docker部署)
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  
  // 图片优化配置
  images: {
    unoptimized: true,
  },
  
  // API路由配置
  rewrites: async () => {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:8000/:path*',
      },
    ];
  },
  
  // 构建配置
  eslint: {
    ignoreDuringBuilds: false,
  },
  typescript: {
    ignoreBuildErrors: false,
  },
  
  // 环境变量配置
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000',
  },
  
  // 压缩配置
  compress: true,
  
  // 性能配置
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@radix-ui/react-icons'],
  },
};

export default nextConfig; 