'use client';

import React from 'react';
import { User, Settings, LogOut, Shield, Bell, HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface UserInfo {
  id: string;
  username: string;
  email: string;
  fullName?: string;
  avatar?: string;
  isActive: boolean;
  createdAt: string;
}

interface UserMenuProps {
  user: UserInfo;
  onLogout: () => void;
  onSettings?: () => void;
  onProfile?: () => void;
  className?: string;
}

export default function UserMenu({ 
  user, 
  onLogout, 
  onSettings,
  onProfile,
  className 
}: UserMenuProps) {
  // 生成用户头像的初始字母
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // 格式化注册时间
  const formatJoinDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
    });
  };

  const displayName = user.fullName || user.username;
  const initials = getInitials(displayName);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            'relative h-10 w-10 rounded-full hover:bg-accent/50 transition-colors',
            className
          )}
        >
          <Avatar className="h-10 w-10">
            <AvatarImage src={user.avatar} alt={displayName} />
            <AvatarFallback className="bg-gradient-to-br from-primary-500 to-secondary-500 text-white font-medium">
              {initials}
            </AvatarFallback>
          </Avatar>
          
          {/* 在线状态指示器 */}
          {user.isActive && (
            <div className="absolute bottom-0 right-0 h-3 w-3 bg-success-500 border-2 border-white rounded-full" />
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-64" align="end" forceMount>
        {/* 用户信息头部 */}
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-2">
            <div className="flex items-center space-x-3">
              <Avatar className="h-12 w-12">
                <AvatarImage src={user.avatar} alt={displayName} />
                <AvatarFallback className="bg-gradient-to-br from-primary-500 to-secondary-500 text-white font-medium text-lg">
                  {initials}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <p className="text-body-md font-semibold text-primary truncate">
                    {displayName}
                  </p>
                  {user.isActive && (
                    <Badge variant="secondary" className="text-xs bg-success-100 text-success-700">
                      在线
                    </Badge>
                  )}
                </div>
                <p className="text-body-sm text-secondary truncate">
                  {user.email}
                </p>
                <p className="text-caption text-tertiary">
                  {formatJoinDate(user.createdAt)} 加入
                </p>
              </div>
            </div>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        {/* 菜单项 */}
        <DropdownMenuItem 
          onClick={onProfile}
          className="cursor-pointer hover:bg-accent/50 transition-colors"
        >
          <User className="mr-3 h-4 w-4 text-secondary" />
          <div className="flex-1">
            <div className="text-body-sm font-medium text-primary">个人资料</div>
            <div className="text-caption text-tertiary">查看和编辑个人信息</div>
          </div>
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={onSettings}
          className="cursor-pointer hover:bg-accent/50 transition-colors"
        >
          <Settings className="mr-3 h-4 w-4 text-secondary" />
          <div className="flex-1">
            <div className="text-body-sm font-medium text-primary">账户设置</div>
            <div className="text-caption text-tertiary">管理账户和偏好设置</div>
          </div>
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={() => {
            // TODO: 实现安全设置功能
            console.log('Security settings clicked');
          }}
          className="cursor-pointer hover:bg-accent/50 transition-colors"
        >
          <Shield className="mr-3 h-4 w-4 text-secondary" />
          <div className="flex-1">
            <div className="text-body-sm font-medium text-primary">安全设置</div>
            <div className="text-caption text-tertiary">密码和安全选项</div>
          </div>
        </DropdownMenuItem>
        
        <DropdownMenuItem 
          onClick={() => {
            // TODO: 实现通知设置功能
            console.log('Notification settings clicked');
          }}
          className="cursor-pointer hover:bg-accent/50 transition-colors"
        >
          <Bell className="mr-3 h-4 w-4 text-secondary" />
          <div className="flex-1">
            <div className="text-body-sm font-medium text-primary">通知设置</div>
            <div className="text-caption text-tertiary">管理通知偏好</div>
          </div>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={() => {
            // TODO: 实现帮助功能
            console.log('Help clicked');
          }}
          className="cursor-pointer hover:bg-accent/50 transition-colors"
        >
          <HelpCircle className="mr-3 h-4 w-4 text-secondary" />
          <div className="flex-1">
            <div className="text-body-sm font-medium text-primary">帮助中心</div>
            <div className="text-caption text-tertiary">获取帮助和支持</div>
          </div>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={onLogout}
          className="cursor-pointer hover:bg-destructive/10 transition-colors focus:bg-destructive/10"
        >
          <LogOut className="mr-3 h-4 w-4 text-destructive" />
          <div className="flex-1">
            <div className="text-body-sm font-medium text-destructive">退出登录</div>
            <div className="text-caption text-tertiary">安全退出当前账户</div>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// 简化版用户菜单，用于空间受限的场景
interface CompactUserMenuProps {
  user: UserInfo;
  onLogout: () => void;
  className?: string;
}

export function CompactUserMenu({ user, onLogout, className }: CompactUserMenuProps) {
  const displayName = user.fullName || user.username;
  const initials = getInitials(displayName);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            'relative h-8 w-8 rounded-full hover:bg-accent/50 transition-colors',
            className
          )}
        >
          <Avatar className="h-8 w-8">
            <AvatarImage src={user.avatar} alt={displayName} />
            <AvatarFallback className="bg-gradient-to-br from-primary-500 to-secondary-500 text-white text-xs font-medium">
              {initials}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-48" align="end">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <p className="text-body-sm font-medium text-primary truncate">
              {displayName}
            </p>
            <p className="text-caption text-secondary truncate">
              {user.email}
            </p>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem className="cursor-pointer">
          <User className="mr-2 h-4 w-4" />
          个人资料
        </DropdownMenuItem>
        
        <DropdownMenuItem className="cursor-pointer">
          <Settings className="mr-2 h-4 w-4" />
          设置
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem 
          onClick={onLogout}
          className="cursor-pointer text-destructive focus:text-destructive"
        >
          <LogOut className="mr-2 h-4 w-4" />
          退出登录
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// 辅助函数
function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
} 