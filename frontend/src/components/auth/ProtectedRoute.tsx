'use client';

import React, { useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@/providers/AuthProvider';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
  fallback?: React.ReactNode;
  className?: string;
}

export default function ProtectedRoute({
  children,
  requireAuth = true,
  redirectTo = '/login',
  fallback,
  className,
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    if (!isLoading && requireAuth && !isAuthenticated) {
      // 保存当前路径，登录后可以重定向回来
      const returnUrl = encodeURIComponent(pathname || '');
      router.push(`${redirectTo}?returnUrl=${returnUrl}`);
    }
  }, [isLoading, requireAuth, isAuthenticated, router, redirectTo, pathname]);

  // 如果正在加载认证状态，显示加载组件
  if (isLoading) {
    return fallback || <AuthLoadingSkeleton className={className} />;
  }

  // 如果需要认证但用户未登录，不渲染内容
  if (requireAuth && !isAuthenticated) {
    return null;
  }

  // 如果不需要认证或用户已登录，渲染子组件
  return <>{children}</>;
}

// 角色保护路由组件
interface RoleProtectedRouteProps extends ProtectedRouteProps {
  allowedRoles?: string[];
  userRole?: string;
  onUnauthorized?: () => void;
}

export function RoleProtectedRoute({
  children,
  allowedRoles = [],
  userRole,
  onUnauthorized,
  ...props
}: RoleProtectedRouteProps) {
  const { user, isAuthenticated, isLoading } = useAuth();
  const router = useRouter();

  // 获取用户角色（这里假设用户对象中有role字段，实际需要根据后端API调整）
  const currentUserRole = userRole || (user as any)?.role || 'user';

  useEffect(() => {
    if (!isLoading && isAuthenticated && allowedRoles.length > 0) {
      if (!allowedRoles.includes(currentUserRole)) {
        if (onUnauthorized) {
          onUnauthorized();
        } else {
          // 默认重定向到无权限页面
          router.push('/unauthorized');
        }
      }
    }
  }, [isLoading, isAuthenticated, allowedRoles, currentUserRole, onUnauthorized, router]);

  // 如果用户已认证但没有权限，不渲染内容
  if (isAuthenticated && allowedRoles.length > 0 && !allowedRoles.includes(currentUserRole)) {
    return null;
  }

  return (
    <ProtectedRoute {...props}>
      {children}
    </ProtectedRoute>
  );
}

// 管理员保护路由
interface AdminProtectedRouteProps extends Omit<ProtectedRouteProps, 'requireAuth'> {
  onUnauthorized?: () => void;
}

export function AdminProtectedRoute({ onUnauthorized, ...props }: AdminProtectedRouteProps) {
  return (
    <RoleProtectedRoute
      {...props}
      requireAuth={true}
      allowedRoles={['admin', 'superuser']}
      onUnauthorized={onUnauthorized}
    />
  );
}

// 认证加载骨架屏
function AuthLoadingSkeleton({ className }: { className?: string }) {
  return (
    <div className={`min-h-screen bg-background ${className || ''}`}>
      {/* 顶部导航骨架 */}
      <div className="border-b border-border">
        <div className="flex h-16 items-center px-4">
          <Skeleton className="h-8 w-32" />
          <div className="ml-auto flex items-center space-x-4">
            <Skeleton className="h-8 w-8 rounded-full" />
          </div>
        </div>
      </div>

      {/* 主要内容骨架 */}
      <div className="flex">
        {/* 侧边栏骨架 */}
        <div className="w-64 border-r border-border bg-muted/10">
          <div className="p-4 space-y-4">
            <Skeleton className="h-6 w-24" />
            <div className="space-y-2">
              {Array.from({ length: 6 }).map((_, i) => (
                <Skeleton key={i} className="h-8 w-full" />
              ))}
            </div>
          </div>
        </div>

        {/* 内容区域骨架 */}
        <div className="flex-1 p-6">
          <div className="space-y-6">
            <div className="space-y-2">
              <Skeleton className="h-8 w-48" />
              <Skeleton className="h-4 w-96" />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 6 }).map((_, i) => (
                <Card key={i}>
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <Skeleton className="h-6 w-32" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <div className="flex space-x-2">
                        <Skeleton className="h-8 w-16" />
                        <Skeleton className="h-8 w-16" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 简单的加载指示器
export function SimpleAuthLoading({ message = '正在验证身份...' }: { message?: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
        <p className="text-muted-foreground">{message}</p>
      </div>
    </div>
  );
}

// 无权限页面组件
export function UnauthorizedPage() {
  const router = useRouter();
  const { logout } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-background">
      <Card className="w-full max-w-md">
        <CardContent className="p-6 text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-destructive/10 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          
          <div className="space-y-2">
            <h2 className="text-heading-md font-semibold text-primary">访问被拒绝</h2>
            <p className="text-body-sm text-secondary">
              您没有权限访问此页面。请联系管理员获取相应权限。
            </p>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => router.back()}
              className="flex-1 px-4 py-2 text-sm font-medium text-secondary bg-secondary hover:bg-secondary/80 border border-border rounded-md transition-colors"
            >
              返回上页
            </button>
            <button
              onClick={() => logout()}
              className="flex-1 px-4 py-2 text-sm font-medium text-white bg-destructive hover:bg-destructive/90 rounded-md transition-colors"
            >
              重新登录
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// 高阶组件：为页面添加认证保护
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'> = {}
) {
  const AuthenticatedComponent = (props: P) => {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };

  AuthenticatedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  
  return AuthenticatedComponent;
}

// 高阶组件：为页面添加角色保护
export function withRole<P extends object>(
  Component: React.ComponentType<P>,
  allowedRoles: string[],
  options: Omit<RoleProtectedRouteProps, 'children' | 'allowedRoles'> = {}
) {
  const RoleProtectedComponent = (props: P) => {
    return (
      <RoleProtectedRoute {...options} allowedRoles={allowedRoles}>
        <Component {...props} />
      </RoleProtectedRoute>
    );
  };

  RoleProtectedComponent.displayName = `withRole(${Component.displayName || Component.name})`;
  
  return RoleProtectedComponent;
} 