'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Eye, EyeOff, Mail, Lock, User, Loader2, Check, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { cn } from '@/lib/utils';

// 密码强度验证函数
const validatePasswordStrength = (password: string) => {
  const checks = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };
  
  const score = Object.values(checks).filter(Boolean).length;
  return { checks, score };
};

// 注册表单验证模式
const registerSchema = z.object({
  username: z
    .string()
    .min(3, '用户名至少3位字符')
    .max(20, '用户名不能超过20位字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  email: z
    .string()
    .min(1, '请输入邮箱地址')
    .email('请输入有效的邮箱地址'),
  password: z
    .string()
    .min(8, '密码至少8位字符')
    .max(50, '密码不能超过50位字符')
    .refine(
      (password) => {
        const { score } = validatePasswordStrength(password);
        return score >= 3;
      },
      '密码强度不足，请包含大小写字母、数字或特殊字符'
    ),
  confirmPassword: z
    .string()
    .min(1, '请确认密码'),
  fullName: z
    .string()
    .min(2, '姓名至少2位字符')
    .max(50, '姓名不能超过50位字符')
    .optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
});

type RegisterFormData = z.infer<typeof registerSchema>;

interface RegisterFormProps {
  onSubmit: (data: RegisterFormData) => Promise<void>;
  isLoading?: boolean;
  error?: string | null;
  className?: string;
}

export default function RegisterForm({ 
  onSubmit, 
  isLoading = false, 
  error = null,
  className 
}: RegisterFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({ checks: {}, score: 0 });

  const form = useForm({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      confirmPassword: '',
      fullName: '',
    },
  });

  const watchPassword = form.watch('password');

  // 监听密码变化，更新强度指示器
  React.useEffect(() => {
    if (watchPassword) {
      setPasswordStrength(validatePasswordStrength(watchPassword));
    } else {
      setPasswordStrength({ checks: {}, score: 0 });
    }
  }, [watchPassword]);

  const handleSubmit = async (data: RegisterFormData) => {
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Register form submission error:', error);
    }
  };

  const getPasswordStrengthColor = (score: number) => {
    if (score < 2) return 'bg-error';
    if (score < 4) return 'bg-warning';
    return 'bg-success';
  };

  const getPasswordStrengthText = (score: number) => {
    if (score < 2) return '弱';
    if (score < 4) return '中等';
    return '强';
  };

  return (
    <Card className={cn('w-full max-w-md mx-auto', className)}>
      <CardHeader className="space-y-1 text-center">
        <CardTitle className="text-display-lg text-primary">
          创建账户
        </CardTitle>
        <CardDescription className="text-body-md text-secondary">
          填写以下信息创建您的新账户
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            {/* 全局错误显示 */}
            {error && (
              <div className="p-3 rounded-md bg-error text-white text-sm">
                {error}
              </div>
            )}

            {/* 用户名输入 */}
            <FormField
              control={form.control}
              name="username"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-body-sm font-medium text-primary">
                    用户名
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-tertiary" />
                      <Input
                        {...field}
                        type="text"
                        placeholder="输入用户名"
                        className="pl-10 input-base"
                        disabled={isLoading}
                        autoComplete="username"
                      />
                    </div>
                  </FormControl>
                  <FormMessage className="text-error text-xs" />
                </FormItem>
              )}
            />

            {/* 邮箱输入 */}
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-body-sm font-medium text-primary">
                    邮箱地址
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-tertiary" />
                      <Input
                        {...field}
                        type="email"
                        placeholder="输入邮箱地址"
                        className="pl-10 input-base"
                        disabled={isLoading}
                        autoComplete="email"
                      />
                    </div>
                  </FormControl>
                  <FormMessage className="text-error text-xs" />
                </FormItem>
              )}
            />

            {/* 姓名输入（可选） */}
            <FormField
              control={form.control}
              name="fullName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-body-sm font-medium text-primary">
                    姓名 <span className="text-tertiary">(可选)</span>
                  </FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="text"
                      placeholder="输入您的姓名"
                      className="input-base"
                      disabled={isLoading}
                      autoComplete="name"
                    />
                  </FormControl>
                  <FormMessage className="text-error text-xs" />
                </FormItem>
              )}
            />

            {/* 密码输入 */}
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-body-sm font-medium text-primary">
                    密码
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-tertiary" />
                      <Input
                        {...field}
                        type={showPassword ? 'text' : 'password'}
                        placeholder="输入密码"
                        className="pl-10 pr-10 input-base"
                        disabled={isLoading}
                        autoComplete="new-password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-tertiary hover:text-secondary transition-colors"
                        disabled={isLoading}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  
                  {/* 密码强度指示器 */}
                  {watchPassword && (
                    <div className="mt-2 space-y-2">
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 h-2 bg-tertiary rounded-full overflow-hidden">
                          <div 
                            className={cn(
                              'h-full transition-all duration-300',
                              getPasswordStrengthColor(passwordStrength.score)
                            )}
                            style={{ width: `${(passwordStrength.score / 5) * 100}%` }}
                          />
                        </div>
                        <span className="text-xs text-secondary">
                          {getPasswordStrengthText(passwordStrength.score)}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-2 gap-1 text-xs">
                        {Object.entries({
                          length: '至少8位字符',
                          lowercase: '包含小写字母',
                          uppercase: '包含大写字母',
                          number: '包含数字',
                          special: '包含特殊字符',
                        }).map(([key, label]) => (
                          <div key={key} className="flex items-center space-x-1">
                            {passwordStrength.checks[key as keyof typeof passwordStrength.checks] ? (
                              <Check className="h-3 w-3 text-success" />
                            ) : (
                              <X className="h-3 w-3 text-error" />
                            )}
                            <span className={cn(
                              passwordStrength.checks[key as keyof typeof passwordStrength.checks] 
                                ? 'text-success' 
                                : 'text-tertiary'
                            )}>
                              {label}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <FormMessage className="text-error text-xs" />
                </FormItem>
              )}
            />

            {/* 确认密码输入 */}
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-body-sm font-medium text-primary">
                    确认密码
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-tertiary" />
                      <Input
                        {...field}
                        type={showConfirmPassword ? 'text' : 'password'}
                        placeholder="再次输入密码"
                        className="pl-10 pr-10 input-base"
                        disabled={isLoading}
                        autoComplete="new-password"
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-tertiary hover:text-secondary transition-colors"
                        disabled={isLoading}
                      >
                        {showConfirmPassword ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage className="text-error text-xs" />
                </FormItem>
              )}
            />

            {/* 注册按钮 */}
            <Button
              type="submit"
              className="w-full btn-primary btn-md"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  注册中...
                </>
              ) : (
                '创建账户'
              )}
            </Button>

            {/* 服务条款提示 */}
            <div className="text-center text-xs text-tertiary">
              注册即表示您同意我们的
              <button
                type="button"
                className="text-brand-primary hover:text-brand-secondary underline-offset-4 hover:underline mx-1"
                onClick={() => {
                  // TODO: 显示服务条款
                  console.log('Terms of service clicked');
                }}
              >
                服务条款
              </button>
              和
              <button
                type="button"
                className="text-brand-primary hover:text-brand-secondary underline-offset-4 hover:underline mx-1"
                onClick={() => {
                  // TODO: 显示隐私政策
                  console.log('Privacy policy clicked');
                }}
              >
                隐私政策
              </button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
} 