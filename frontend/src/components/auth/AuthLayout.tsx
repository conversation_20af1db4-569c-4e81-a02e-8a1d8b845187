'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface AuthLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  className?: string;
}

export default function AuthLayout({ 
  children, 
  title = "AI金融分析系统",
  subtitle = "智能投资决策平台",
  className 
}: AuthLayoutProps) {
  return (
    <div className={cn(
      'min-h-screen bg-gradient-to-br from-primary-50 via-background to-secondary-50',
      'dark:from-gray-900 dark:via-gray-800 dark:to-gray-900',
      className
    )}>
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000" />
        <div className="absolute top-40 left-40 w-80 h-80 bg-info-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000" />
      </div>

      {/* 主要内容区域 */}
      <div className="relative z-10 flex min-h-screen">
        {/* 左侧品牌区域 - 在大屏幕上显示 */}
        <div className="hidden lg:flex lg:flex-1 lg:flex-col lg:justify-center lg:px-8 xl:px-12">
          <div className="mx-auto max-w-md">
            {/* 品牌标识 */}
            <div className="mb-8">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-xl flex items-center justify-center">
                  <svg 
                    className="w-7 h-7 text-white" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
                    />
                  </svg>
                </div>
                <div>
                  <h1 className="text-heading-lg font-bold text-primary">
                    {title}
                  </h1>
                  <p className="text-body-sm text-secondary">
                    {subtitle}
                  </p>
                </div>
              </div>
            </div>

            {/* 功能特点 */}
            <div className="space-y-6">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-body-md font-semibold text-primary">AI智能分析</h3>
                  <p className="text-body-sm text-secondary">
                    基于先进的机器学习算法，提供精准的市场分析和投资建议
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-secondary-100 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-body-md font-semibold text-primary">实时数据</h3>
                  <p className="text-body-sm text-secondary">
                    整合多源金融数据，实时更新市场动态和新闻资讯
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 w-8 h-8 bg-success-100 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-body-md font-semibold text-primary">安全可靠</h3>
                  <p className="text-body-sm text-secondary">
                    企业级安全保障，保护您的数据隐私和投资信息
                  </p>
                </div>
              </div>
            </div>

            {/* 统计数据 */}
            <div className="mt-12 grid grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-heading-md font-bold text-primary">10K+</div>
                <div className="text-caption text-secondary">活跃用户</div>
              </div>
              <div className="text-center">
                <div className="text-heading-md font-bold text-primary">99.9%</div>
                <div className="text-caption text-secondary">系统稳定性</div>
              </div>
              <div className="text-center">
                <div className="text-heading-md font-bold text-primary">24/7</div>
                <div className="text-caption text-secondary">服务支持</div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧表单区域 */}
        <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:px-8 xl:px-12">
          <div className="mx-auto w-full max-w-md">
            {/* 移动端品牌标识 */}
            <div className="lg:hidden mb-8 text-center">
              <div className="inline-flex items-center space-x-2 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                  <svg 
                    className="w-6 h-6 text-white" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
                    />
                  </svg>
                </div>
                <div>
                  <h1 className="text-heading-md font-bold text-primary">
                    {title}
                  </h1>
                </div>
              </div>
              <p className="text-body-sm text-secondary">
                {subtitle}
              </p>
            </div>

            {/* 表单内容 */}
            <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 dark:border-gray-700/20 p-8">
              {children}
            </div>

            {/* 页脚信息 */}
            <div className="mt-8 text-center">
              <p className="text-caption text-tertiary">
                © 2024 AI金融分析系统. 保留所有权利.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// 添加动画样式到全局CSS中
const styles = `
  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }
  
  .animate-blob {
    animation: blob 7s infinite;
  }
  
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  
  .animation-delay-4000 {
    animation-delay: 4s;
  }
`;

// 如果需要，可以将样式注入到页面中
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
} 