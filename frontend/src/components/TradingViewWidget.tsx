'use client';

import React, { useEffect, memo, useMemo } from 'react';
import { useTradingViewOptimization } from '@/hooks/useTradingViewOptimization';

interface TradingViewWidgetProps {
  symbol?: string;
  height?: string | number;
  theme?: 'light' | 'dark';
  locale?: string;
  className?: string;
  onSymbolChange?: (symbol: string) => void;
}

function TradingViewWidget({ 
  symbol = "NASDAQ:AAPL",
  height = "100%",
  theme = "dark",
  locale = "zh",
  className = "",
  onSymbolChange
}: TradingViewWidgetProps) {
  const {
    containerRef,
    isLoading,
    loadedSymbol,
    cleanup
  } = useTradingViewOptimization({
    symbol,
    theme,
    locale,
    debounceMs: 300
  });

  // Memoized container style to prevent unnecessary re-renders
  const containerStyle = useMemo(() => ({
    height: typeof height === 'number' ? `${height}px` : height,
    width: "100%"
  }), [height]);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  // Handle symbol change callback
  useEffect(() => {
    if (onSymbolChange && loadedSymbol && loadedSymbol !== symbol) {
      onSymbolChange(loadedSymbol);
    }
  }, [loadedSymbol, symbol, onSymbolChange]);

  return (
    <div className={`tradingview-widget-container ${className} ${isLoading ? 'loading' : ''}`} style={containerStyle}>
      <div 
        className="tradingview-widget-container__widget" 
        ref={containerRef} 
        style={{ height: "calc(100% - 32px)", width: "100%" }}
      />
      {isLoading && (
        <div className="tradingview-loading-overlay">
          <div className="tradingview-loading-spinner">
            <div className="spinner"></div>
            <span>加载图表中...</span>
          </div>
        </div>
      )}
      <div className="tradingview-widget-copyright">
        <a 
          href="https://www.tradingview.com/" 
          rel="noopener nofollow" 
          target="_blank"
          className="text-xs text-gray-500 hover:text-gray-700"
        >
          <span className="blue-text">Track all markets on TradingView</span>
        </a>
      </div>
    </div>
  );
}

export default memo(TradingViewWidget); 