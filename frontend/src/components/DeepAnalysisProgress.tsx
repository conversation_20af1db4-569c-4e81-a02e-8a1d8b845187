'use client';

import React, { useState, useEffect } from 'react';
import LoadingSpinner from './LoadingSpinner';

interface DeepAnalysisProgressProps {
  taskId?: string;
  newsData: {
    id: number;
    title: string;
    content: string;
    source: string;
    publish_time: string;
  };
  onComplete: (result: any) => void;
  onError: (error: string) => void;
  onClose: () => void;
  useDirectStream?: boolean;
  useFourLayerAnalysis?: boolean;
}

interface ProgressMessage {
  type: string;
  message: string;
  task_id?: string;
  context?: any;
  queries?: any[];
  current_query?: any;
  result_preview?: string;
  result?: any;
  error?: string;
  timestamp?: string;
}

const DeepAnalysisProgress: React.FC<DeepAnalysisProgressProps> = ({
  taskId,
  newsData,
  onComplete,
  onError,
  onClose,
  useDirectStream = false,
  useFourLayerAnalysis = false
}) => {
  const [messages, setMessages] = useState<ProgressMessage[]>([]);
  const [currentStatus, setCurrentStatus] = useState<string>('starting');
  const [finalResult, setFinalResult] = useState<any>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [actualTaskId, setActualTaskId] = useState<string | null>(taskId || null);

  useEffect(() => {
    // 根据模式选择连接方式
    if (useDirectStream) {
      // 直接流式模式：创建新的深度分析任务
      startDirectStreamAnalysis();
    } else if (taskId) {
      // 任务ID模式：监听现有任务的进度
      connectToTaskStream();
    } else {
      onError('缺少任务ID或未启用直接流式模式');
    }
  }, [taskId, useDirectStream]);

  // 直接流式分析
  const startDirectStreamAnalysis = async () => {
    try {
      setCurrentStatus('正在连接分析服务...');
      
      const response = await fetch('http://localhost:8000/news/deep-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          news_id: newsData.id,
          news_title: newsData.title,
          news_content: newsData.content,
          news_source: newsData.source,
          news_publish_time: newsData.publish_time,
          analysis_type: 'deep',
          max_research_loops: 1,
          priority: 'medium',
          use_four_layer_analysis: useFourLayerAnalysis
        })
      });

      if (!response.ok) {
        let errorMessage = '';
        switch (response.status) {
          case 400:
            errorMessage = '请求参数错误，请检查新闻数据';
            break;
          case 404:
            errorMessage = '分析服务不可用，请联系管理员';
            break;
          case 500:
            errorMessage = '服务器内部错误，请稍后重试';
            break;
          case 503:
            errorMessage = 'AI分析服务暂时不可用，请稍后重试';
            break;
          default:
            errorMessage = `服务错误 (${response.status}): ${response.statusText}`;
        }
        
        // 尝试获取详细错误信息
        try {
          const errorData = await response.json();
          if (errorData.detail) {
            errorMessage += ` - ${errorData.detail}`;
          }
        } catch {
          // 忽略解析错误
        }
        
        throw new Error(errorMessage);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流，请刷新页面重试');
      }

      setCurrentStatus('开始接收分析数据...');
      const decoder = new TextDecoder();
      let buffer = '';
      let messageCount = 0;
      let lastMessageTime = Date.now();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) {
            console.log(`流式数据接收完成，共处理 ${messageCount} 条消息`);
            break;
          }

          // 更新最后接收时间
          lastMessageTime = Date.now();

          // 解码数据
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // 按行分割数据
          const lines = buffer.split('\n');
          buffer = lines.pop() || ''; // 保留未完整的行

          // 处理每一行数据
          for (const line of lines) {
            const trimmedLine = line.trim();
            if (!trimmedLine) continue; // 跳过空行

            if (trimmedLine.startsWith('data: ')) {
              try {
                const jsonData = trimmedLine.slice(6).trim();
                if (!jsonData) continue; // 跳过空数据

                const data = JSON.parse(jsonData);
                messageCount++;

                // 验证数据格式
                if (typeof data === 'object' && data !== null) {
                  handleStreamMessage(data);
                } else {
                  console.warn('接收到非对象格式的数据:', data);
                }
              } catch (parseErr) {
                console.error('解析流式数据失败:', {
                  error: parseErr,
                  rawLine: trimmedLine,
                  jsonData: trimmedLine.slice(6).trim(),
                  messageCount
                });

                // 尝试修复常见的JSON格式问题
                try {
                  const jsonData = trimmedLine.slice(6).trim();
                  // 移除可能的控制字符
                  const cleanedData = jsonData.replace(/[\x00-\x1F\x7F]/g, '');
                  const data = JSON.parse(cleanedData);
                  handleStreamMessage(data);
                  console.log('数据修复成功');
                } catch (retryErr) {
                  console.error('数据修复失败，跳过此消息:', retryErr);
                  // 继续处理，不因为单条数据解析失败而中断整个流
                }
              }
            } else if (trimmedLine.startsWith('event:') || trimmedLine.startsWith('id:')) {
              // 处理SSE事件头，暂时忽略
              console.debug('SSE事件头:', trimmedLine);
            } else {
              console.debug('未识别的行格式:', trimmedLine);
            }
          }

          // 检查是否长时间没有收到数据（可能的连接问题）
          const timeSinceLastMessage = Date.now() - lastMessageTime;
          if (timeSinceLastMessage > 60000) { // 60秒超时
            console.warn('长时间未收到数据，可能存在连接问题');
            setCurrentStatus('⚠️ 连接可能不稳定，正在等待数据...');
          }
        }

        // 处理缓冲区中剩余的数据
        if (buffer.trim()) {
          console.log('处理缓冲区剩余数据:', buffer);
          const trimmedBuffer = buffer.trim();
          if (trimmedBuffer.startsWith('data: ')) {
            try {
              const jsonData = trimmedBuffer.slice(6).trim();
              const data = JSON.parse(jsonData);
              handleStreamMessage(data);
            } catch (err) {
              console.error('处理缓冲区数据失败:', err);
            }
          }
        }

        // 如果流结束但没有收到完成信号，标记为完成
        if (!isCompleted && !error) {
          console.log('流式数据接收完成，等待最终状态确认...');
          setCurrentStatus('分析数据接收完成，正在处理...');

          // 给一些时间让最后的消息处理完成
          setTimeout(() => {
            if (!isCompleted && !error) {
              setCurrentStatus('分析可能已完成，请检查结果');
            }
          }, 2000);
        }

      } catch (streamErr) {
        console.error('流式数据处理过程中出错:', streamErr);
        throw streamErr; // 重新抛出错误，让外层catch处理
      }
      
    } catch (err) {
      console.error('直接流式分析失败:', err);

      // 详细的错误分析和处理
      let errorMessage = '';
      let errorDetails = {
        type: 'unknown',
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        onlineStatus: navigator.onLine
      };

      if (err instanceof TypeError) {
        if (err.message.includes('fetch')) {
          errorMessage = '无法连接到分析服务，请检查网络连接和服务器状态';
          errorDetails.type = 'network';
        } else if (err.message.includes('NetworkError')) {
          errorMessage = '网络错误，请检查网络连接';
          errorDetails.type = 'network';
        } else {
          errorMessage = `类型错误: ${err.message}`;
          errorDetails.type = 'type_error';
        }
      } else if (err instanceof Error) {
        errorMessage = err.message;
        errorDetails.type = 'generic_error';

        // 检查是否是CORS错误
        if (err.message.includes('CORS') || err.message.includes('cross-origin')) {
          errorMessage = '跨域请求被阻止，请联系技术支持';
          errorDetails.type = 'cors';
        }
      } else {
        errorMessage = '分析服务连接失败，请稍后重试';
        errorDetails.type = 'unknown';
      }

      // 检查网络状态
      if (!navigator.onLine) {
        errorMessage = '网络连接已断开，请检查网络设置后重试';
        errorDetails.type = 'offline';
      }

      console.error('错误详情:', errorDetails);

      const fullErrorMessage = `${errorMessage}

错误详情：
- 错误类型: ${errorDetails.type}
- 网络状态: ${navigator.onLine ? '在线' : '离线'}
- 时间戳: ${new Date().toLocaleString()}

建议解决方案：
- 检查网络连接
- 刷新页面重试
- 如问题持续，请联系技术支持`;

      setError(fullErrorMessage);
      setCurrentStatus('❌ 连接失败');
      onError(fullErrorMessage);
    }
  };

  // 连接到任务流
  const connectToTaskStream = () => {
    if (!taskId) return;

    // 首先尝试获取已完成的结果
    fetchFinalResult();

    // 创建EventSource监听流式响应
    const connectToStream = () => {
      const eventSource = new EventSource(`http://localhost:8000/news/deep-analysis/${taskId}/stream`);

      eventSource.onopen = () => {
        console.log('EventSource连接已建立');
        setError(null);
        setCurrentStatus('连接已建立');
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleStreamMessage(data);
        } catch (err) {
          console.error('解析进度消息失败:', err, '原始数据:', event.data);
          const errorMsg = '数据解析失败，请刷新页面重试';
          setError(errorMsg);
          onError(errorMsg);
        }
      };

      eventSource.onerror = (err) => {
        console.error('EventSource连接错误:', err);

        // 获取详细的错误信息
        const errorDetails = {
          readyState: eventSource?.readyState,
          url: eventSource?.url,
          withCredentials: eventSource?.withCredentials,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          onlineStatus: navigator.onLine
        };

        console.error('EventSource详细状态:', errorDetails);

        // 根据readyState提供更具体的错误信息
        let errorMessage = '';
        switch (eventSource?.readyState) {
          case EventSource.CONNECTING:
            errorMessage = '正在尝试连接到服务器...';
            break;
          case EventSource.OPEN:
            errorMessage = '连接已建立但出现错误';
            break;
          case EventSource.CLOSED:
            errorMessage = '连接已关闭';
            break;
          default:
            errorMessage = '连接状态未知';
        }

        // 检查网络连接状态
        if (navigator.onLine === false) {
          console.error('网络连接已断开');
          const offlineMessage = '网络连接已断开，请检查网络设置后重试';
          setError(offlineMessage);
          setCurrentStatus('❌ 网络连接断开');
          onError(offlineMessage);
          return;
        }

        // 检查是否是CORS或权限问题
        if (err && typeof err === 'object') {
          console.error('错误对象详情:', {
            type: err.type,
            target: err.target,
            currentTarget: err.currentTarget,
            eventPhase: err.eventPhase,
            bubbles: err.bubbles,
            cancelable: err.cancelable
          });
        }

        eventSource?.close();

        if (retryCount < maxRetries && !isCompleted && !error) {
          retryCount++;
          const retryMessage = `连接断开，正在重连 (${retryCount}/${maxRetries})... 请稍候`;
          console.log(`重连尝试 ${retryCount}/${maxRetries}，错误详情:`, errorDetails);
          setCurrentStatus(retryMessage);

          // 递增延迟重连
          const retryDelay = Math.min(3000 * retryCount, 15000); // 最大延迟15秒
          setTimeout(connectWithRetry, retryDelay);
        } else if (!isCompleted && !error) {
          console.log('重连失败，尝试获取最终结果...');
          setCurrentStatus('连接失败，正在获取最终结果...');
          fetchFinalResult();
        } else {
          const finalErrorMessage = `EventSource连接失败，已重试 ${maxRetries} 次。

错误详情：
- 连接状态: ${errorMessage}
- 网络状态: ${navigator.onLine ? '在线' : '离线'}
- 时间戳: ${new Date().toLocaleString()}

可能的原因：
1. 后端服务异常或重启
2. 网络连接不稳定
3. 分析任务超时或被中断
4. 浏览器安全策略限制

建议解决方案：
- 刷新页面重试
- 检查网络连接
- 联系技术支持`;

          setCurrentStatus('❌ 连接失败');
          setError(finalErrorMessage);
          onError(finalErrorMessage);
        }
      };

      return eventSource;
    };

    let eventSource: EventSource | null = null;
    let retryCount = 0;
    const maxRetries = 5;  // 增加重试次数

    const connectWithRetry = () => {
      eventSource = connectToStream();
      // 错误处理已在connectToStream中实现，这里不需要重复
    };
    
    connectWithRetry();

    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  };

  // 统一的消息处理函数
  const handleStreamMessage = (data: ProgressMessage) => {
    try {
      // 验证消息格式
      if (!data || typeof data !== 'object') {
        console.warn('收到无效的消息格式:', data);
        return;
      }

      console.log('收到消息:', {
        type: data.type,
        message: data.message?.substring(0, 100) + (data.message?.length > 100 ? '...' : ''),
        timestamp: data.timestamp
      });

      // 添加消息到历史记录（限制数量避免内存泄漏）
      setMessages(prev => {
        const newMessages = [...prev, data];
        // 保留最近100条消息
        return newMessages.slice(-100);
      });

      // 提取任务ID（如果有）
      if (data.task_id && !actualTaskId) {
        console.log('提取到任务ID:', data.task_id);
        setActualTaskId(data.task_id);
      }

      // 更新当前状态
      if (data.type) {
        setCurrentStatus(data.type);
      }

      // 更新消息内容状态
      if (data.message) {
        setCurrentStatus(data.message);
      }

    } catch (err) {
      console.error('处理流式消息时出错:', err, '原始数据:', data);
    }

    // 处理不同类型的消息
    switch (data.type) {
      case 'task_started':
        setCurrentStatus('🚀 研究开始');
        break;
      case 'context_analysis':
        setCurrentStatus('📝 分析上下文');
        break;
      case 'context_completed':
        setCurrentStatus('✅ 上下文分析完成');
        break;
      case 'content_quality_warning':
        setCurrentStatus(`⚠️ ${data.message}`);
        break;
      case 'rate_limit_info':
        setCurrentStatus('⏱️ API速率限制：每次请求间隔10秒，确保连接稳定性，请耐心等待...');
        break;
      case 'generating_queries':
        setCurrentStatus('🤖 准备生成研究查询');
        break;
      case 'llm_call_starting':
        setCurrentStatus(`🤖 ${data.message}`);
        break;
      case 'llm_processing':
        setCurrentStatus(`🔄 ${data.message}`);
        break;
      case 'llm_success':
        setCurrentStatus(`✅ ${data.message}`);
        break;
      case 'llm_retry':
        setCurrentStatus(`⚠️ ${data.message}`);
        break;
      case 'llm_wait':
        setCurrentStatus(`⏱️ ${data.message}`);
        break;
      case 'llm_error':
        setCurrentStatus(`❌ ${data.message}`);
        break;
      case 'llm_fallback':
        setCurrentStatus(`⚠️ ${data.message}`);
        break;
      case 'queries_generated':
        setCurrentStatus('✅ 查询生成完成');
        break;
      case 'web_research_started':
        setCurrentStatus('🔍 网络搜索研究');
        break;
      case 'search_progress':
        setCurrentStatus(`🔍 ${data.message}`);
        break;
      case 'search_completed':
        setCurrentStatus(`✅ ${data.message}`);
        break;
      case 'reflection_started':
        setCurrentStatus('🤔 评估研究完整性');
        break;
      case 'additional_research':
        setCurrentStatus('🔍 补充研究');
        break;
      case 'four_layer_analysis_started':
        setCurrentStatus('🧠 启动四层思维链分析');
        break;
      case 'four_layer_l1_perception':
        setCurrentStatus('🌏 第一层：事件感知分析');
        break;
      case 'four_layer_l2_deep_dig':
        setCurrentStatus('🔍 第二层：深度挖掘分析');
        break;
      case 'four_layer_l3_domestic_impact':
        setCurrentStatus('🏠 第三层：国内影响分析');
        break;
      case 'four_layer_l4_target_screening':
        setCurrentStatus('🎯 第四层：标的筛选分析');
        break;
      case 'four_layer_synthesis':
        setCurrentStatus('🔗 四层分析结果综合');
        break;
      case 'finalizing_analysis':
        setCurrentStatus('📊 生成最终分析');
        break;
      case 'saving_results':
        setCurrentStatus('💾 保存分析结果');
        break;
      case 'results_saved':
        setCurrentStatus('✅ 分析结果已保存');
        break;
      case 'save_warning':
        setCurrentStatus(`⚠️ ${data.message}`);
        break;
      case 'status_update':
        // 处理状态更新
        setCurrentStatus(data.message || '分析进行中...');
        break;
      case 'analysis_completed':
        setCurrentStatus('🎉 分析完成！');
        console.log('收到分析完成消息，结果数据:', data.result);
        setFinalResult(data.result);
        setIsCompleted(true);
        // 不要立即调用onComplete，让用户先查看结果
        // onComplete(data.result);
        break;
      case 'error':
        setError(data.message || '分析失败');
        setCurrentStatus('❌ 分析失败');
        onError(data.message || '分析失败');
        break;
      case 'timeout':
        setError('分析超时');
        setCurrentStatus('⏰ 分析超时');
        onError('分析超时');
        break;
    }
  };

  // 获取最终结果的备用方法
  const fetchFinalResult = async () => {
    try {
      const response = await fetch(`http://localhost:8000/news/deep-analysis/${taskId}`);
      const data = await response.json();
      
      console.log('获取的API响应数据:', data);
      
      if (data.success && data.data.research_status === 'completed') {
        console.log('任务已完成，设置最终结果:', data.data);
        setFinalResult(data.data);
        setIsCompleted(true);
        setCurrentStatus('🎉 分析完成！');
        // 不要立即调用onComplete，让用户先查看结果
        // onComplete(data.data);
      } else if (data.data && data.data.research_status === 'failed') {
        setError(data.data.error_message || '分析失败');
        setCurrentStatus('❌ 分析失败');
        onError(data.data.error_message || '分析失败');
      } else {
        console.log('任务状态:', data.data?.research_status);
        setCurrentStatus(`当前状态: ${data.data?.research_status || '未知'}`);
      }
    } catch (err) {
      console.error('获取最终结果失败:', err);
      setError('获取分析结果失败，请刷新页面重试');
      setCurrentStatus('❌ 获取结果失败');
    }
  };

  const getStatusIcon = (status: string) => {
    if (error) {
      return (
        <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      );
    }

    if (isCompleted) {
      return (
        <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      );
    }

    return <LoadingSpinner size="sm" color="primary" />;
  };

  // 增强的Markdown格式化函数
  const formatMarkdownContent = (content: string): string => {
    if (!content) return '<p class="text-gray-500 italic">暂无分析内容</p>';

    try {
      let html = content
        // 转义HTML特殊字符
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;');

      // 处理代码块（必须在其他处理之前）
      html = html.replace(/```(\w+)?\n?([\s\S]*?)```/g, (_match, lang, code) => {
        const language = lang || 'text';
        return `<pre class="bg-gray-100 border border-gray-200 rounded-lg p-4 my-4 overflow-x-auto"><code class="text-sm font-mono language-${language}">${code.trim()}</code></pre>`;
      });

      // 处理行内代码
      html = html.replace(/`([^`\n]+)`/g, '<code class="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono">$1</code>');

      // 处理标题（按层级从高到低处理）
      html = html.replace(/^#### (.*$)/gm, '<h4 class="text-base font-semibold text-gray-900 mt-5 mb-2 border-l-4 border-blue-400 pl-3">$1</h4>');
      html = html.replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold text-gray-900 mt-6 mb-3 border-l-4 border-blue-500 pl-3">$1</h3>');
      html = html.replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold text-gray-900 mt-8 mb-4 border-b-2 border-gray-200 pb-2">$1</h2>');
      html = html.replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold text-gray-900 mt-8 mb-4 border-b-2 border-gray-300 pb-2">$1</h1>');

      // 处理粗体和斜体
      html = html.replace(/\*\*\*(.*?)\*\*\*/g, '<strong class="font-bold text-gray-900"><em class="italic">$1</em></strong>');
      html = html.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>');
      html = html.replace(/\*(.*?)\*/g, '<em class="italic text-gray-800">$1</em>');

      // 处理链接
      html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:text-blue-800 underline decoration-2 underline-offset-2">$1 <svg class="inline w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path></svg></a>');

      // 处理表格（简单实现）
      html = html.replace(/\|(.+)\|/g, (_match, content) => {
        const cells = content.split('|').map((cell: string) => cell.trim());
        const cellsHtml = cells.map((cell: string) => `<td class="border border-gray-300 px-3 py-2">${cell}</td>`).join('');
        return `<tr>${cellsHtml}</tr>`;
      });

      // 处理引用块
      html = html.replace(/^> (.*$)/gm, '<blockquote class="border-l-4 border-gray-300 pl-4 py-2 my-4 bg-gray-50 italic text-gray-700">$1</blockquote>');

      // 处理列表项
      let inList = false;
      const lines = html.split('\n');
      const processedLines = [];

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const isListItem = /^(\s*)[-*+] (.*)$/.test(line) || /^(\s*)(\d+)\. (.*)$/.test(line);

        if (isListItem) {
          if (!inList) {
            processedLines.push('<ul class="list-none space-y-2 my-4">');
            inList = true;
          }

          const match = line.match(/^(\s*)[-*+] (.*)$/) || line.match(/^(\s*)(\d+)\. (.*)$/);
          if (match) {
            const indent = match[1].length;
            const content = match[2] || match[3];
            const marginClass = indent > 0 ? `ml-${Math.min(indent, 8)}` : '';
            processedLines.push(`<li class="flex items-start space-x-2 ${marginClass}"><span class="text-blue-500 font-bold mt-1">•</span><span>${content}</span></li>`);
          }
        } else {
          if (inList) {
            processedLines.push('</ul>');
            inList = false;
          }
          processedLines.push(line);
        }
      }

      if (inList) {
        processedLines.push('</ul>');
      }

      html = processedLines.join('\n');

      // 处理段落和换行
      html = html.replace(/\n\s*\n/g, '</p><p class="mb-4 text-gray-700 leading-relaxed">');
      html = html.replace(/\n/g, '<br>');

      // 包装在段落中
      if (html && !html.startsWith('<h') && !html.startsWith('<p') && !html.startsWith('<ul') && !html.startsWith('<blockquote')) {
        html = '<p class="mb-4 text-gray-700 leading-relaxed">' + html + '</p>';
      }

      return html;

    } catch (err) {
      console.error('Markdown格式化失败:', err);
      // 降级处理：返回纯文本
      return `<div class="whitespace-pre-wrap text-gray-700 leading-relaxed">${content}</div>`;
    }
  };

  const formatAnalysisResult = (result: any) => {
    if (!result) {
      console.warn('formatAnalysisResult: 结果为空');
      return null;
    }

    console.log('格式化分析结果，输入数据:', result);

    // 更全面的数据结构处理
    let analysis = '';

    // 尝试多种可能的数据路径
    const possiblePaths = [
      // 直接字符串格式
      () => typeof result.final_analysis === 'string' ? result.final_analysis : null,
      // 嵌套对象格式
      () => result.final_analysis?.analysis,
      // 简单analysis字段
      () => typeof result.analysis === 'string' ? result.analysis : null,
      // API包装格式
      () => result.data?.final_analysis,
      () => result.data?.analysis,
      // 四层分析格式
      () => result.final_analysis?.layer4_analysis,
      () => result.final_analysis?.analysis_summary,
      // 其他可能的格式
      () => result.content,
      () => result.text,
      // 如果是对象，尝试序列化
      () => {
        if (result.final_analysis && typeof result.final_analysis === 'object') {
          return JSON.stringify(result.final_analysis, null, 2);
        }
        return null;
      }
    ];

    // 按优先级尝试提取分析内容
    for (const pathExtractor of possiblePaths) {
      try {
        const extracted = pathExtractor();
        if (extracted && typeof extracted === 'string' && extracted.trim().length > 0) {
          analysis = extracted.trim();
          break;
        }
      } catch (err) {
        console.warn('提取分析内容时出错:', err);
      }
    }

    console.log('提取的分析内容:', analysis ? `${analysis.substring(0, 100)}...` : '无内容');

    if (!analysis) {
      console.error('无法提取分析内容，原始数据:', result);
      return (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div className="flex items-center space-x-3">
            <svg className="w-5 h-5 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <div>
              <h3 className="text-lg font-medium text-yellow-800">分析结果格式异常</h3>
              <p className="text-sm text-yellow-600 mt-1">
                分析已完成，但结果格式不符合预期。请联系技术支持或刷新页面重试。
              </p>
              <details className="mt-2">
                <summary className="text-xs text-yellow-700 cursor-pointer">查看原始数据</summary>
                <pre className="text-xs text-yellow-600 mt-1 bg-yellow-100 p-2 rounded overflow-auto max-h-40">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </details>
            </div>
          </div>
        </div>
      );
    }
    
    // 更全面的来源数据解析
    let sources = [];

    const sourcePaths = [
      () => result.sources_gathered,
      () => result.sources,
      () => result.data?.sources_gathered,
      () => result.data?.sources,
      () => result.research_results?.map((r: any) => r.sources).flat(),
      () => result.citations
    ];

    for (const pathExtractor of sourcePaths) {
      try {
        const extracted = pathExtractor();
        if (extracted) {
          if (Array.isArray(extracted)) {
            sources = extracted;
            break;
          } else if (typeof extracted === 'string') {
            // 尝试解析JSON字符串
            try {
              const parsed = JSON.parse(extracted);
              if (Array.isArray(parsed)) {
                sources = parsed;
                break;
              }
            } catch (parseErr) {
              console.warn('JSON解析失败:', parseErr);
            }
          }
        }
      } catch (err) {
        console.warn('提取来源数据时出错:', err);
      }
    }

    // 确保sources是数组并过滤无效项
    if (!Array.isArray(sources)) {
      sources = [];
    }

    // 过滤和标准化来源数据
    sources = sources.filter(source => {
      return source && (
        typeof source === 'string' ||
        (typeof source === 'object' && (source.url || source.value || source.title || source.label))
      );
    }).map((source, index) => {
      if (typeof source === 'string') {
        return {
          label: `来源 ${index + 1}`,
          value: source,
          title: source
        };
      }
      return {
        label: source.label || source.title || `来源 ${index + 1}`,
        value: source.value || source.url || source.link,
        title: source.title || source.label,
        domain: source.domain
      };
    });

    console.log('处理后的来源数据:', sources);

    return (
      <div className="space-y-6">
        {/* 分析结果 */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            深度分析报告
          </h3>
          <div className="prose max-w-none">
            <div className="text-gray-700 leading-relaxed">
              {/* 渲染Markdown格式的分析内容 */}
              <div
                className="markdown-content"
                dangerouslySetInnerHTML={{
                  __html: formatMarkdownContent(analysis)
                }}
              />
            </div>
          </div>
        </div>

        {/* 研究来源 */}
        {sources.length > 0 && (
          <div className="bg-gray-50 rounded-lg border p-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              研究来源 ({sources.length})
            </h4>
            <div className="space-y-2">
              {sources.map((source: any, index: number) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-white rounded border">
                  <div className="flex-shrink-0">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {source.label || source.title || '研究来源'}
                    </p>
                    {source.value && (
                      <a
                        href={source.value}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 hover:text-blue-800 truncate block"
                      >
                        {source.domain || source.value}
                      </a>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 研究统计 */}
        <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              <span className="text-blue-700">
                <strong>研究查询:</strong> {
                  (() => {
                    // 尝试解析JSON格式的查询数据
                    try {
                      if (result.research_queries) {
                        if (Array.isArray(result.research_queries)) {
                          return result.research_queries.length;
                        } else if (typeof result.research_queries === 'string') {
                          const parsed = JSON.parse(result.research_queries);
                          return Array.isArray(parsed) ? parsed.length : 0;
                        }
                      }
                      return result.queries_used?.length || 0;
                    } catch {
                      return 0;
                    }
                  })()
                }
              </span>
              <span className="text-blue-700">
                <strong>来源数量:</strong> {sources.length}
              </span>
              <span className="text-blue-700">
                <strong>分析时间:</strong> {
                  result.completed_at ? new Date(result.completed_at).toLocaleString() : 
                  result.processing_time_seconds ? `${result.processing_time_seconds}秒` : ''
                }
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              {getStatusIcon(currentStatus)}
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  🔍 AI深度分析
                </h1>
                <p className="text-sm text-gray-600">
                  基于Gemini的智能新闻深度研究
                </p>
              </div>
            </div>
            <button
              onClick={() => {
                // 如果分析已完成且有结果，调用onComplete传递结果
                if (isCompleted && finalResult) {
                  onComplete(finalResult);
                }
                onClose();
              }}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors flex items-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span>关闭</span>
            </button>
          </div>

          {/* 新闻信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">{newsData.title}</h3>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>{newsData.source}</span>
              <span>{new Date(newsData.publish_time).toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* 进度显示 */}
        {!isCompleted && !error && (
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">分析进度</h2>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>实时更新</span>
              </div>
            </div>

            {/* 当前状态指示器 */}
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center space-x-3">
                <LoadingSpinner size="sm" color="primary" />
                <div className="flex-1">
                  <p className="text-sm font-medium text-blue-900">当前状态</p>
                  <p className="text-sm text-blue-700">{currentStatus}</p>
                </div>
                <div className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                  进行中
                </div>
              </div>
            </div>

            {/* 进度时间线 */}
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {messages.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <svg className="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <p className="text-sm">等待分析开始...</p>
                </div>
              ) : (
                messages.map((message, index) => {
                  const isLatest = index === messages.length - 1;
                  const messageType = message.type || 'info';

                  // 根据消息类型确定图标和颜色
                  let iconColor = 'bg-blue-400';
                  let icon = '•';

                  if (messageType.includes('error') || messageType.includes('failed')) {
                    iconColor = 'bg-red-400';
                    icon = '✗';
                  } else if (messageType.includes('success') || messageType.includes('completed')) {
                    iconColor = 'bg-green-400';
                    icon = '✓';
                  } else if (messageType.includes('warning')) {
                    iconColor = 'bg-yellow-400';
                    icon = '⚠';
                  } else if (messageType.includes('llm') || messageType.includes('ai')) {
                    iconColor = 'bg-purple-400';
                    icon = '🤖';
                  } else if (messageType.includes('search') || messageType.includes('web')) {
                    iconColor = 'bg-indigo-400';
                    icon = '🔍';
                  }

                  return (
                    <div key={index} className={`flex items-start space-x-3 p-3 rounded-lg transition-all duration-300 ${
                      isLatest ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50'
                    }`}>
                      <div className="flex-shrink-0 mt-0.5">
                        <div className={`w-6 h-6 ${iconColor} rounded-full flex items-center justify-center text-white text-xs font-bold`}>
                          {icon}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className={`text-sm ${isLatest ? 'text-blue-900 font-medium' : 'text-gray-900'}`}>
                          {message.message}
                        </p>
                        <div className="flex items-center justify-between mt-1">
                          {message.timestamp && (
                            <p className="text-xs text-gray-500">
                              {new Date(message.timestamp).toLocaleTimeString()}
                            </p>
                          )}
                          {isLatest && (
                            <div className="flex items-center space-x-1 text-xs text-blue-600">
                              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                              <span>最新</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>

            {/* 底部统计信息 */}
            <div className="mt-4 pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>已处理 {messages.length} 个步骤</span>
                <span>开始时间: {new Date().toLocaleTimeString()}</span>
              </div>
            </div>
          </div>
        )}

        {/* 错误显示 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <div className="flex items-center space-x-3">
              <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <div>
                <h3 className="text-lg font-medium text-red-800">分析失败</h3>
                <p className="text-sm text-red-600 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 最终结果 */}
        {isCompleted && finalResult && (
          <div>
            {formatAnalysisResult(finalResult)}
            
            {/* 操作按钮 */}
            <div className="mt-6 flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-green-800">深度分析完成</h3>
                  <p className="text-xs text-green-600">分析报告已生成，您可以继续查看或返回主页</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => {
                    // 获取分析内容
                    let analysisText = '';
                    if (typeof finalResult.final_analysis === 'string') {
                      analysisText = finalResult.final_analysis;
                    } else if (finalResult.final_analysis?.analysis) {
                      analysisText = finalResult.final_analysis.analysis;
                    } else if (finalResult.analysis) {
                      analysisText = finalResult.analysis;
                    } else {
                      analysisText = '深度分析报告';
                    }

                    if (navigator.share) {
                      navigator.share({
                        title: `${newsData.title} - AI深度分析报告`,
                        text: analysisText,
                        url: window.location.href
                      });
                    } else {
                      // 复制到剪贴板
                      navigator.clipboard.writeText(analysisText).then(() => {
                        alert('分析报告已复制到剪贴板');
                      }).catch(() => {
                        // 降级处理
                        const textArea = document.createElement('textarea');
                        textArea.value = analysisText;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        alert('分析报告已复制到剪贴板');
                      });
                    }
                  }}
                  className="px-3 py-1.5 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors flex items-center space-x-1"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <span>分享</span>
                </button>
                <button
                  onClick={() => {
                    onComplete(finalResult);
                    onClose();
                  }}
                  className="px-4 py-1.5 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors flex items-center space-x-1"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span>返回主页</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DeepAnalysisProgress; 