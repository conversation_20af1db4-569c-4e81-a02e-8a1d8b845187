'use client';

import React, { useState, useEffect } from 'react';
import LoadingSpinner from './LoadingSpinner';

interface DeepAnalysisProgressProps {
  taskId?: string;
  newsData: {
    id: number;
    title: string;
    content: string;
    source: string;
    publish_time: string;
  };
  onComplete: (result: any) => void;
  onError: (error: string) => void;
  onClose: () => void;
  useDirectStream?: boolean;
  useFourLayerAnalysis?: boolean;
}

interface ProgressMessage {
  type: string;
  message: string;
  task_id?: string;
  context?: any;
  queries?: any[];
  current_query?: any;
  result_preview?: string;
  result?: any;
  error?: string;
  timestamp?: string;
}

const DeepAnalysisProgress: React.FC<DeepAnalysisProgressProps> = ({
  taskId,
  newsData,
  onComplete,
  onError,
  onClose,
  useDirectStream = false,
  useFourLayerAnalysis = false
}) => {
  const [messages, setMessages] = useState<ProgressMessage[]>([]);
  const [currentStatus, setCurrentStatus] = useState<string>('starting');
  const [finalResult, setFinalResult] = useState<any>(null);
  const [isCompleted, setIsCompleted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [actualTaskId, setActualTaskId] = useState<string | null>(taskId || null);

  useEffect(() => {
    // 根据模式选择连接方式
    if (useDirectStream) {
      // 直接流式模式：创建新的深度分析任务
      startDirectStreamAnalysis();
    } else if (taskId) {
      // 任务ID模式：监听现有任务的进度
      connectToTaskStream();
    } else {
      onError('缺少任务ID或未启用直接流式模式');
    }
  }, [taskId, useDirectStream]);

  // 直接流式分析
  const startDirectStreamAnalysis = async () => {
    try {
      setCurrentStatus('正在连接分析服务...');
      
      const response = await fetch('http://localhost:8000/news/deep-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          news_id: newsData.id,
          news_title: newsData.title,
          news_content: newsData.content,
          news_source: newsData.source,
          news_publish_time: newsData.publish_time,
          analysis_type: 'deep',
          max_research_loops: 1,
          priority: 'medium',
          use_four_layer_analysis: useFourLayerAnalysis
        })
      });

      if (!response.ok) {
        let errorMessage = '';
        switch (response.status) {
          case 400:
            errorMessage = '请求参数错误，请检查新闻数据';
            break;
          case 404:
            errorMessage = '分析服务不可用，请联系管理员';
            break;
          case 500:
            errorMessage = '服务器内部错误，请稍后重试';
            break;
          case 503:
            errorMessage = 'AI分析服务暂时不可用，请稍后重试';
            break;
          default:
            errorMessage = `服务错误 (${response.status}): ${response.statusText}`;
        }
        
        // 尝试获取详细错误信息
        try {
          const errorData = await response.json();
          if (errorData.detail) {
            errorMessage += ` - ${errorData.detail}`;
          }
        } catch {
          // 忽略解析错误
        }
        
        throw new Error(errorMessage);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流，请刷新页面重试');
      }

      setCurrentStatus('开始接收分析数据...');
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // 保留未完整的行

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              handleStreamMessage(data);
            } catch (err) {
              console.error('解析流式数据失败:', err, '原始数据:', line);
              // 继续处理，不因为单条数据解析失败而中断
            }
          }
        }
      }
      
      // 如果流结束但没有收到完成信号，标记为完成
      if (!isCompleted && !error) {
        setCurrentStatus('分析数据接收完成');
      }
      
    } catch (err) {
      console.error('直接流式分析失败:', err);
      let errorMessage = '';
      
      if (err instanceof TypeError && err.message.includes('fetch')) {
        errorMessage = '无法连接到分析服务，请检查网络连接';
      } else if (err instanceof Error) {
        errorMessage = err.message;
      } else {
        errorMessage = '分析服务连接失败，请稍后重试';
      }
      
      setError(errorMessage);
      setCurrentStatus('连接失败');
      onError(errorMessage);
    }
  };

  // 连接到任务流
  const connectToTaskStream = () => {
    if (!taskId) return;

    // 首先尝试获取已完成的结果
    fetchFinalResult();

    // 创建EventSource监听流式响应
    const connectToStream = () => {
      const eventSource = new EventSource(`http://localhost:8000/news/deep-analysis/${taskId}/stream`);

      eventSource.onopen = () => {
        console.log('EventSource连接已建立');
        setError(null);
        setCurrentStatus('连接已建立');
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          handleStreamMessage(data);
        } catch (err) {
          console.error('解析进度消息失败:', err, '原始数据:', event.data);
          const errorMsg = '数据解析失败，请刷新页面重试';
          setError(errorMsg);
          onError(errorMsg);
        }
      };

      eventSource.onerror = (err) => {
        console.error('EventSource连接错误:', err);
        console.error('EventSource状态:', {
          readyState: eventSource?.readyState,
          url: eventSource?.url,
          withCredentials: eventSource?.withCredentials
        });
        
        // 检查网络连接状态
        if (navigator.onLine === false) {
          console.error('网络连接已断开');
          setError('网络连接已断开，请检查网络设置');
          onError('网络连接已断开，请检查网络设置');
          return;
        }
        
        eventSource?.close();
        
        if (retryCount < maxRetries && !isCompleted && !error) {
          retryCount++;
          console.log(`重连尝试 ${retryCount}/${maxRetries}`);
          setCurrentStatus(`连接断开，正在重连 (${retryCount}/${maxRetries})... 请稍候`);
          setTimeout(connectWithRetry, 3000 * retryCount); // 增加递增延迟
        } else if (!isCompleted && !error) {
          console.log('重连失败，尝试获取最终结果...');
          setCurrentStatus('连接失败，正在获取最终结果...');
          fetchFinalResult();
        } else {
          const finalErrorMessage = `连接失败，已重试 ${maxRetries} 次。可能的原因：
          1. 后端服务异常
          2. 网络连接不稳定  
          3. 分析任务超时
          请刷新页面重试`;
          setCurrentStatus('连接失败');
          setError(finalErrorMessage);
          onError(finalErrorMessage);
        }
      };

      return eventSource;
    };

    let eventSource: EventSource | null = null;
    let retryCount = 0;
    const maxRetries = 5;  // 增加重试次数
    
    const connectWithRetry = () => {
      eventSource = connectToStream();
      
      eventSource.onerror = (err) => {
        console.error('EventSource错误:', err);
        eventSource?.close();
        
        if (retryCount < maxRetries && !isCompleted && !error) {
          retryCount++;
          console.log(`重连尝试 ${retryCount}/${maxRetries}`);
          setCurrentStatus(`连接断开，正在重连 (${retryCount}/${maxRetries})... 请稍候`);
          setTimeout(connectWithRetry, 3000 * retryCount); // 增加递增延迟
        } else if (!isCompleted && !error) {
          console.log('重连失败，尝试获取最终结果...');
          setCurrentStatus('连接失败，正在获取最终结果...');
          fetchFinalResult();
        } else {
          setCurrentStatus('连接失败');
          setError(`连接失败，已重试 ${maxRetries} 次`);
          onError(`连接失败，已重试 ${maxRetries} 次`);
        }
      };
    };
    
    connectWithRetry();

    return () => {
      if (eventSource) {
        eventSource.close();
      }
    };
  };

  // 统一的消息处理函数
  const handleStreamMessage = (data: ProgressMessage) => {
    console.log('收到消息:', data);
    setMessages(prev => [...prev, data]);
    
    // 提取任务ID（如果有）
    if (data.task_id && !actualTaskId) {
      setActualTaskId(data.task_id);
    }
    
    // 更新当前状态
    if (data.type) {
      setCurrentStatus(data.type);
    }

    // 处理不同类型的消息
    switch (data.type) {
      case 'task_started':
        setCurrentStatus('🚀 研究开始');
        break;
      case 'context_analysis':
        setCurrentStatus('📝 分析上下文');
        break;
      case 'context_completed':
        setCurrentStatus('✅ 上下文分析完成');
        break;
      case 'content_quality_warning':
        setCurrentStatus(`⚠️ ${data.message}`);
        break;
      case 'rate_limit_info':
        setCurrentStatus('⏱️ API速率限制：每次请求间隔10秒，确保连接稳定性，请耐心等待...');
        break;
      case 'generating_queries':
        setCurrentStatus('🤖 准备生成研究查询');
        break;
      case 'llm_call_starting':
        setCurrentStatus(`🤖 ${data.message}`);
        break;
      case 'llm_processing':
        setCurrentStatus(`🔄 ${data.message}`);
        break;
      case 'llm_success':
        setCurrentStatus(`✅ ${data.message}`);
        break;
      case 'llm_retry':
        setCurrentStatus(`⚠️ ${data.message}`);
        break;
      case 'llm_wait':
        setCurrentStatus(`⏱️ ${data.message}`);
        break;
      case 'llm_error':
        setCurrentStatus(`❌ ${data.message}`);
        break;
      case 'llm_fallback':
        setCurrentStatus(`⚠️ ${data.message}`);
        break;
      case 'queries_generated':
        setCurrentStatus('✅ 查询生成完成');
        break;
      case 'web_research_started':
        setCurrentStatus('🔍 网络搜索研究');
        break;
      case 'search_progress':
        setCurrentStatus(`🔍 ${data.message}`);
        break;
      case 'search_completed':
        setCurrentStatus(`✅ ${data.message}`);
        break;
      case 'reflection_started':
        setCurrentStatus('🤔 评估研究完整性');
        break;
      case 'additional_research':
        setCurrentStatus('🔍 补充研究');
        break;
      case 'four_layer_analysis_started':
        setCurrentStatus('🧠 启动四层思维链分析');
        break;
      case 'four_layer_l1_perception':
        setCurrentStatus('🌏 第一层：事件感知分析');
        break;
      case 'four_layer_l2_deep_dig':
        setCurrentStatus('🔍 第二层：深度挖掘分析');
        break;
      case 'four_layer_l3_domestic_impact':
        setCurrentStatus('🏠 第三层：国内影响分析');
        break;
      case 'four_layer_l4_target_screening':
        setCurrentStatus('🎯 第四层：标的筛选分析');
        break;
      case 'four_layer_synthesis':
        setCurrentStatus('🔗 四层分析结果综合');
        break;
      case 'finalizing_analysis':
        setCurrentStatus('📊 生成最终分析');
        break;
      case 'saving_results':
        setCurrentStatus('💾 保存分析结果');
        break;
      case 'results_saved':
        setCurrentStatus('✅ 分析结果已保存');
        break;
      case 'save_warning':
        setCurrentStatus(`⚠️ ${data.message}`);
        break;
      case 'status_update':
        // 处理状态更新
        setCurrentStatus(data.message || '分析进行中...');
        break;
      case 'analysis_completed':
        setCurrentStatus('🎉 分析完成！');
        console.log('收到分析完成消息，结果数据:', data.result);
        setFinalResult(data.result);
        setIsCompleted(true);
        // 不要立即调用onComplete，让用户先查看结果
        // onComplete(data.result);
        break;
      case 'error':
        setError(data.message || '分析失败');
        setCurrentStatus('❌ 分析失败');
        onError(data.message || '分析失败');
        break;
      case 'timeout':
        setError('分析超时');
        setCurrentStatus('⏰ 分析超时');
        onError('分析超时');
        break;
    }
  };

  // 获取最终结果的备用方法
  const fetchFinalResult = async () => {
    try {
      const response = await fetch(`http://localhost:8000/news/deep-analysis/${taskId}`);
      const data = await response.json();
      
      console.log('获取的API响应数据:', data);
      
      if (data.success && data.data.research_status === 'completed') {
        console.log('任务已完成，设置最终结果:', data.data);
        setFinalResult(data.data);
        setIsCompleted(true);
        setCurrentStatus('🎉 分析完成！');
        // 不要立即调用onComplete，让用户先查看结果
        // onComplete(data.data);
      } else if (data.data && data.data.research_status === 'failed') {
        setError(data.data.error_message || '分析失败');
        setCurrentStatus('❌ 分析失败');
        onError(data.data.error_message || '分析失败');
      } else {
        console.log('任务状态:', data.data?.research_status);
        setCurrentStatus(`当前状态: ${data.data?.research_status || '未知'}`);
      }
    } catch (err) {
      console.error('获取最终结果失败:', err);
      setError('获取分析结果失败，请刷新页面重试');
      setCurrentStatus('❌ 获取结果失败');
    }
  };

  const getStatusIcon = (status: string) => {
    if (error) {
      return (
        <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
      );
    }
    
    if (isCompleted) {
      return (
        <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
        </svg>
      );
    }
    
    return <LoadingSpinner size="sm" color="primary" />;
  };

  const formatAnalysisResult = (result: any) => {
    if (!result) return null;
    
    console.log('格式化分析结果，输入数据:', result);
    
    // 处理不同的数据结构格式
    let analysis = '';
    if (typeof result.final_analysis === 'string') {
      analysis = result.final_analysis;
    } else if (result.final_analysis && result.final_analysis.analysis) {
      analysis = result.final_analysis.analysis;
    } else if (typeof result.analysis === 'string') {
      analysis = result.analysis;
    } else if (result.data && typeof result.data.final_analysis === 'string') {
      // 处理API包装的数据格式
      analysis = result.data.final_analysis;
    }
    
    console.log('提取的分析内容:', analysis);
    
    if (!analysis) return null;
    
    // 解析来源数据，处理JSON字符串格式
    let sources = [];
    try {
      if (result.sources_gathered) {
        if (Array.isArray(result.sources_gathered)) {
          sources = result.sources_gathered;
        } else if (typeof result.sources_gathered === 'string') {
          sources = JSON.parse(result.sources_gathered);
        }
      } else if (result.sources) {
        sources = Array.isArray(result.sources) ? result.sources : [];
      } else if (result.data?.sources_gathered) {
        sources = Array.isArray(result.data.sources_gathered) ? result.data.sources_gathered : [];
      }
    } catch (err) {
      console.error('解析来源数据失败:', err);
      sources = [];
    }
    
    // 确保sources是数组
    if (!Array.isArray(sources)) {
      sources = [];
    }

    return (
      <div className="space-y-6">
        {/* 分析结果 */}
        <div className="bg-white rounded-lg border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg className="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            深度分析报告
          </h3>
          <div className="prose max-w-none">
            <div className="text-gray-700 leading-relaxed whitespace-pre-wrap">
              {analysis}
            </div>
          </div>
        </div>

        {/* 研究来源 */}
        {sources.length > 0 && (
          <div className="bg-gray-50 rounded-lg border p-6">
            <h4 className="text-md font-semibold text-gray-900 mb-4 flex items-center">
              <svg className="w-5 h-5 text-gray-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              研究来源 ({sources.length})
            </h4>
            <div className="space-y-2">
              {sources.map((source: any, index: number) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-white rounded border">
                  <div className="flex-shrink-0">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {source.label || source.title || '研究来源'}
                    </p>
                    {source.value && (
                      <a
                        href={source.value}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-blue-600 hover:text-blue-800 truncate block"
                      >
                        {source.domain || source.value}
                      </a>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 研究统计 */}
        <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-4">
              <span className="text-blue-700">
                <strong>研究查询:</strong> {
                  (() => {
                    // 尝试解析JSON格式的查询数据
                    try {
                      if (result.research_queries) {
                        if (Array.isArray(result.research_queries)) {
                          return result.research_queries.length;
                        } else if (typeof result.research_queries === 'string') {
                          const parsed = JSON.parse(result.research_queries);
                          return Array.isArray(parsed) ? parsed.length : 0;
                        }
                      }
                      return result.queries_used?.length || 0;
                    } catch {
                      return 0;
                    }
                  })()
                }
              </span>
              <span className="text-blue-700">
                <strong>来源数量:</strong> {sources.length}
              </span>
              <span className="text-blue-700">
                <strong>分析时间:</strong> {
                  result.completed_at ? new Date(result.completed_at).toLocaleString() : 
                  result.processing_time_seconds ? `${result.processing_time_seconds}秒` : ''
                }
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              {getStatusIcon(currentStatus)}
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  🔍 AI深度分析
                </h1>
                <p className="text-sm text-gray-600">
                  基于Gemini的智能新闻深度研究
                </p>
              </div>
            </div>
            <button
              onClick={() => {
                // 如果分析已完成且有结果，调用onComplete传递结果
                if (isCompleted && finalResult) {
                  onComplete(finalResult);
                }
                onClose();
              }}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors flex items-center space-x-2"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span>关闭</span>
            </button>
          </div>

          {/* 新闻信息 */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-2">{newsData.title}</h3>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>{newsData.source}</span>
              <span>{new Date(newsData.publish_time).toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* 进度显示 */}
        {!isCompleted && !error && (
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">分析进度</h2>
            <div className="space-y-3">
              {messages.map((message, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 mt-0.5">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{message.message}</p>
                    {message.timestamp && (
                      <p className="text-xs text-gray-500 mt-1">
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 flex items-center space-x-3">
              <LoadingSpinner size="sm" color="primary" />
              <span className="text-sm text-gray-600">当前状态: {currentStatus}</span>
            </div>
          </div>
        )}

        {/* 错误显示 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <div className="flex items-center space-x-3">
              <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <div>
                <h3 className="text-lg font-medium text-red-800">分析失败</h3>
                <p className="text-sm text-red-600 mt-1">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 最终结果 */}
        {isCompleted && finalResult && (
          <div>
            {formatAnalysisResult(finalResult)}
            
            {/* 操作按钮 */}
            <div className="mt-6 flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div>
                  <h3 className="text-sm font-medium text-green-800">深度分析完成</h3>
                  <p className="text-xs text-green-600">分析报告已生成，您可以继续查看或返回主页</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => {
                    // 获取分析内容
                    let analysisText = '';
                    if (typeof finalResult.final_analysis === 'string') {
                      analysisText = finalResult.final_analysis;
                    } else if (finalResult.final_analysis?.analysis) {
                      analysisText = finalResult.final_analysis.analysis;
                    } else if (finalResult.analysis) {
                      analysisText = finalResult.analysis;
                    } else {
                      analysisText = '深度分析报告';
                    }

                    if (navigator.share) {
                      navigator.share({
                        title: `${newsData.title} - AI深度分析报告`,
                        text: analysisText,
                        url: window.location.href
                      });
                    } else {
                      // 复制到剪贴板
                      navigator.clipboard.writeText(analysisText).then(() => {
                        alert('分析报告已复制到剪贴板');
                      }).catch(() => {
                        // 降级处理
                        const textArea = document.createElement('textarea');
                        textArea.value = analysisText;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        alert('分析报告已复制到剪贴板');
                      });
                    }
                  }}
                  className="px-3 py-1.5 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors flex items-center space-x-1"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <span>分享</span>
                </button>
                <button
                  onClick={() => {
                    onComplete(finalResult);
                    onClose();
                  }}
                  className="px-4 py-1.5 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors flex items-center space-x-1"
                >
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  <span>返回主页</span>
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DeepAnalysisProgress; 