'use client';

import React, { useState, useMemo, memo } from 'react';
import Chart<PERSON>enderer from './ChartRenderer';
import EnhancedButton from './EnhancedButton';

interface ChartMetadata {
  symbol: string;
  company_name?: string;
  current_price?: number;
  price_change?: number;
  price_change_percent?: number;
  last_update?: string;
  market?: string;
  volume?: number;
  market_cap?: string;
}

interface EnhancedChartContainerProps {
  chartData: any;
  metadata?: ChartMetadata;
  title?: string;
  subtitle?: string;
  height?: number;
  showControls?: boolean;
  showMetadata?: boolean;
  className?: string;
  chartType?: 'echarts' | 'tradingview';
  onFullscreen?: () => void;
  onDownload?: () => void;
  onShare?: () => void;
}

// Memoized ChartRenderer to prevent unnecessary re-renders
const MemoizedChartRenderer = memo(ChartRenderer);

const EnhancedChartContainer: React.FC<EnhancedChartContainerProps> = ({
  chartData,
  metadata,
  title,
  subtitle,
  height = 400,
  showControls = true,
  showMetadata = true,
  className = '',
  chartType = 'tradingview',
  onFullscreen,
  onDownload,
  onShare
}) => {
  const [isHovered, setIsHovered] = useState(false);

  // Format price change
  const formatPriceChange = (change: number, percent: number) => {
    const isPositive = change >= 0;
    const changeText = `${isPositive ? '+' : ''}${change.toFixed(2)}`;
    const percentText = `${isPositive ? '+' : ''}${percent.toFixed(2)}%`;
    
    return {
      text: `${changeText} (${percentText})`,
      color: isPositive ? 'text-success-600' : 'text-error-600',
      bgColor: isPositive ? 'bg-success-50' : 'bg-error-50',
      borderColor: isPositive ? 'border-success-200' : 'border-error-200'
    };
  };

  // Memoized price change calculation to prevent unnecessary re-renders
  const priceChange = useMemo(() => {
    return metadata?.price_change !== undefined && metadata?.price_change_percent !== undefined
      ? formatPriceChange(metadata.price_change, metadata.price_change_percent)
      : null;
  }, [metadata?.price_change, metadata?.price_change_percent]);

  // Memoized chart props to ensure stable references
  const chartProps = useMemo(() => ({
    chartData,
    height,
    className: "relative z-10",
    chartType
  }), [chartData, height, chartType]);

  return (
    <div 
      className={`
        relative bg-white rounded-2xl shadow-lg border border-gray-200
        hover:shadow-xl transition-all duration-300
        ${className}
      `}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Header */}
      <div className="relative p-6 pb-4 border-b border-gray-100">
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-32 h-32 opacity-5 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full transform rotate-12 scale-150" />
        </div>

        <div className="relative flex items-start justify-between">
          {/* Title and metadata */}
          <div className="flex-1">
            {/* Title */}
            <div className="flex items-center space-x-3 mb-2">
              <h3 className="text-xl font-bold text-gray-900">
                {title || metadata?.symbol || '股票图表'}
              </h3>
              {metadata?.symbol && (
                <span className="px-2 py-1 bg-primary-100 text-primary-700 text-sm font-medium rounded-lg">
                  {metadata.symbol}
                </span>
              )}
            </div>

            {/* Subtitle */}
            {(subtitle || metadata?.company_name) && (
              <p className="text-gray-600 mb-3">
                {subtitle || metadata?.company_name}
              </p>
            )}

            {/* Price information */}
            {showMetadata && metadata && (
              <div className="flex flex-wrap items-center gap-4">
                {/* Current price */}
                {metadata.current_price && (
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl font-bold text-gray-900">
                      ¥{metadata.current_price.toFixed(2)}
                    </span>
                    {priceChange && (
                      <span className={`
                        px-2 py-1 rounded-lg text-sm font-medium border
                        ${priceChange.color} ${priceChange.bgColor} ${priceChange.borderColor}
                      `}>
                        {priceChange.text}
                      </span>
                    )}
                  </div>
                )}

                {/* Additional metadata */}
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  {metadata.volume && (
                    <span>成交量: {(metadata.volume / 10000).toFixed(1)}万</span>
                  )}
                  {metadata.market_cap && (
                    <span>市值: {metadata.market_cap}</span>
                  )}
                  {metadata.last_update && (
                    <span>更新: {new Date(metadata.last_update).toLocaleTimeString('zh-CN')}</span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Controls - hover state isolated to UI only */}
          {showControls && (
            <div className={`
              flex items-center space-x-2 transition-all duration-200
              ${isHovered ? 'opacity-100 translate-x-0' : 'opacity-70 translate-x-2'}
            `}>
                             {onShare && (
                 <EnhancedButton
                   variant="ghost"
                   size="sm"
                   icon="📤"
                   onClick={onShare}
                   tooltip="分享图表"
                 >
                   分享
                 </EnhancedButton>
               )}
               {onDownload && (
                 <EnhancedButton
                   variant="ghost"
                   size="sm"
                   icon="💾"
                   onClick={onDownload}
                   tooltip="下载图表"
                 >
                   下载
                 </EnhancedButton>
               )}
            </div>
          )}
        </div>
      </div>

      {/* Chart area */}
      <div className="relative p-6">
        {/* Chart container with enhanced styling */}
        <div className="relative rounded-xl overflow-hidden bg-gradient-to-br from-gray-50 to-white border border-gray-100">
          {/* Chart loading overlay - hover effect isolated */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-secondary-500/5 opacity-0 hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
          
          {/* Chart component - using memoized version with stable props */}
          <MemoizedChartRenderer {...chartProps} />

          {/* Chart decorations */}
          <div className="absolute top-4 right-4 opacity-20">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-400 to-secondary-400 rounded-full" />
          </div>
        </div>

        {/* Chart footer */}
        <div className="mt-4 flex items-center justify-between text-xs text-gray-500">
          <span>数据来源: {chartType === 'tradingview' ? 'TradingView' : '实时行情'}</span>
          <span>图表类型: {chartType === 'tradingview' ? 'TradingView专业图表' : 'K线图'}</span>
        </div>
      </div>
    </div>
  );
};

export default EnhancedChartContainer; 