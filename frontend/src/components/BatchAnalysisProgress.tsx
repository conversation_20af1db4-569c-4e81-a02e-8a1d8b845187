'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>ircle, XCircle, Clock, BarChart3, Zap } from 'lucide-react';

interface BatchAnalysisProgressProps {
  taskId: string;
  onComplete?: (results: any) => void;
  onError?: (error: string) => void;
  className?: string;
}

interface TaskStatus {
  task_id: string;
  status: string;
  progress: number;
  total_items: number;
  completed_items: number;
  failed_items: number;
  model: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
}

const BatchAnalysisProgress: React.FC<BatchAnalysisProgressProps> = ({
  taskId,
  onComplete,
  onError,
  className = ''
}) => {
  const [taskStatus, setTaskStatus] = useState<TaskStatus | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!taskId || !isPolling) return;

    const pollStatus = async () => {
      try {
        const response = await fetch(`http://localhost:8000/news/batch-analysis/${taskId}/status`);
        const data = await response.json();

        if (data.success) {
          const status = data.task_status;
          setTaskStatus(status);

          // 检查任务是否完成
          if (status.status === 'completed') {
            setIsPolling(false);
            if (onComplete) {
              onComplete(status);
            }
          } else if (status.status === 'failed') {
            setIsPolling(false);
            const errorMsg = status.error_message || '批量分析失败';
            setError(errorMsg);
            if (onError) {
              onError(errorMsg);
            }
          }
        } else {
          throw new Error(data.error || '获取任务状态失败');
        }
      } catch (err) {
        console.error('轮询任务状态失败:', err);
        setError(err instanceof Error ? err.message : '网络错误');
        setIsPolling(false);
        if (onError) {
          onError(err instanceof Error ? err.message : '网络错误');
        }
      }
    };

    // 立即执行一次
    pollStatus();

    // 设置轮询
    const interval = setInterval(pollStatus, 2000); // 每2秒轮询一次

    return () => {
      clearInterval(interval);
    };
  }, [taskId, isPolling, onComplete, onError]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'running':
        return <Zap className="w-5 h-5 text-blue-500 animate-pulse" />;
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'failed':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '等待开始';
      case 'running':
        return '分析中';
      case 'completed':
        return '分析完成';
      case 'failed':
        return '分析失败';
      default:
        return '未知状态';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'running':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const formatDuration = (startTime?: string, endTime?: string) => {
    if (!startTime) return '未开始';
    
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = Math.round((end.getTime() - start.getTime()) / 1000);
    
    if (duration < 60) {
      return `${duration}秒`;
    } else if (duration < 3600) {
      return `${Math.round(duration / 60)}分钟`;
    } else {
      return `${Math.round(duration / 3600)}小时`;
    }
  };

  if (error) {
    return (
      <div className={`batch-analysis-progress ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <XCircle className="w-5 h-5 text-red-500" />
            <span className="text-red-700 font-medium">批量分析失败</span>
          </div>
          <p className="text-red-600 text-sm mt-2">{error}</p>
        </div>
      </div>
    );
  }

  if (!taskStatus) {
    return (
      <div className={`batch-analysis-progress ${className}`}>
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <div className="w-5 h-5 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
            <span className="text-gray-700">加载任务状态...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`batch-analysis-progress ${className}`}>
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
        {/* 任务状态头部 */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            {getStatusIcon(taskStatus.status)}
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                批量AI分析任务
              </h3>
              <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(taskStatus.status)}`}>
                {getStatusText(taskStatus.status)}
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">模型</div>
            <div className="font-medium text-gray-900">
              {taskStatus.model.toUpperCase()}
            </div>
          </div>
        </div>

        {/* 进度条 */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">分析进度</span>
            <span className="text-sm text-gray-500">
              {Math.round(taskStatus.progress)}%
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                taskStatus.status === 'completed' 
                  ? 'bg-green-500' 
                  : taskStatus.status === 'failed'
                  ? 'bg-red-500'
                  : 'bg-blue-500'
              }`}
              style={{ width: `${taskStatus.progress}%` }}
            />
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-3 gap-4 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">
              {taskStatus.total_items}
            </div>
            <div className="text-sm text-gray-500">总数</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {taskStatus.completed_items}
            </div>
            <div className="text-sm text-gray-500">成功</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {taskStatus.failed_items}
            </div>
            <div className="text-sm text-gray-500">失败</div>
          </div>
        </div>

        {/* 时间信息 */}
        <div className="border-t border-gray-200 pt-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-500">创建时间:</span>
              <div className="font-medium text-gray-900">
                {new Date(taskStatus.created_at).toLocaleString('zh-CN')}
              </div>
            </div>
            <div>
              <span className="text-gray-500">耗时:</span>
              <div className="font-medium text-gray-900">
                {formatDuration(taskStatus.started_at, taskStatus.completed_at)}
              </div>
            </div>
          </div>
        </div>

        {/* 错误信息 */}
        {taskStatus.error_message && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <div className="text-sm text-red-700">
              <strong>错误信息:</strong> {taskStatus.error_message}
            </div>
          </div>
        )}

        {/* 实时状态指示器 */}
        {taskStatus.status === 'running' && (
          <div className="mt-4 flex items-center justify-center space-x-2 text-blue-600">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
            <span className="text-sm font-medium ml-2">AI正在分析新闻内容...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default BatchAnalysisProgress;
