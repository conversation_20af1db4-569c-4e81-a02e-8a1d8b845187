/**
 * Tests for color conversion utilities
 */

import {
  oklchToRgb,
  rgbToHex,
  oklchStringToHex,
  getCompatibleColor,
  COLOR_MAPPINGS,
  type OKLCHColor,
  type RGBColor
} from '../colorConverter';

describe('Color Conversion Utilities', () => {
  describe('oklchToRgb', () => {
    test('converts achromatic colors correctly', () => {
      const white: OKLCHColor = { l: 1, c: 0, h: 0 };
      const whiteRgb = oklchToRgb(white);
      expect(whiteRgb).toEqual({ r: 255, g: 255, b: 255, alpha: 1 });

      const black: OKLCHColor = { l: 0, c: 0, h: 0 };
      const blackRgb = oklchToRgb(black);
      expect(blackRgb).toEqual({ r: 0, g: 0, b: 0, alpha: 1 });

      const gray: OKLCHColor = { l: 0.5, c: 0, h: 0 };
      const grayRgb = oklchToRgb(gray);
      expect(grayRgb.r).toBe(128);
      expect(grayRgb.g).toBe(128);
      expect(grayRgb.b).toBe(128);
    });

    test('handles alpha channel correctly', () => {
      const semiTransparent: OKLCHColor = { l: 1, c: 0, h: 0, alpha: 0.5 };
      const result = oklchToRgb(semiTransparent);
      expect(result.alpha).toBe(0.5);
    });

    test('clamps RGB values to valid range', () => {
      // Test with extreme values that might produce out-of-range RGB
      const extreme: OKLCHColor = { l: 2, c: 2, h: 25 };
      const result = oklchToRgb(extreme);
      
      expect(result.r).toBeGreaterThanOrEqual(0);
      expect(result.r).toBeLessThanOrEqual(255);
      expect(result.g).toBeGreaterThanOrEqual(0);
      expect(result.g).toBeLessThanOrEqual(255);
      expect(result.b).toBeGreaterThanOrEqual(0);
      expect(result.b).toBeLessThanOrEqual(255);
    });
  });

  describe('rgbToHex', () => {
    test('converts RGB to hex correctly', () => {
      const white: RGBColor = { r: 255, g: 255, b: 255 };
      expect(rgbToHex(white)).toBe('#ffffff');

      const black: RGBColor = { r: 0, g: 0, b: 0 };
      expect(rgbToHex(black)).toBe('#000000');

      const red: RGBColor = { r: 255, g: 0, b: 0 };
      expect(rgbToHex(red)).toBe('#ff0000');
    });

    test('handles single digit hex values correctly', () => {
      const darkColor: RGBColor = { r: 15, g: 10, b: 5 };
      expect(rgbToHex(darkColor)).toBe('#0f0a05');
    });

    test('includes alpha in hex when provided', () => {
      const semiTransparent: RGBColor = { r: 255, g: 0, b: 0, alpha: 0.5 };
      const hex = rgbToHex(semiTransparent);
      expect(hex).toMatch(/^#[0-9a-f]{8}$/);
      expect(hex.endsWith('80')).toBe(true); // 0.5 * 255 = 127.5 ≈ 128 = 0x80
    });
  });

  describe('oklchStringToHex', () => {
    test('parses and converts basic OKLCH strings', () => {
      expect(oklchStringToHex('oklch(1 0 0)')).toBe('#ffffff');
      expect(oklchStringToHex('oklch(0 0 0)')).toBe('#000000');
    });

    test('handles OKLCH strings with alpha', () => {
      const result = oklchStringToHex('oklch(1 0 0 / 0.5)');
      expect(result).toMatch(/^#[0-9a-f]{8}$/);
    });

    test('handles OKLCH strings with percentage alpha', () => {
      const result = oklchStringToHex('oklch(1 0 0 / 50%)');
      expect(result).toMatch(/^#[0-9a-f]{8}$/);
    });

    test('throws error for invalid OKLCH format', () => {
      expect(() => oklchStringToHex('invalid-color')).toThrow();
      expect(() => oklchStringToHex('rgb(255, 0, 0)')).toThrow();
    });

    test('handles whitespace in OKLCH strings', () => {
      expect(() => oklchStringToHex('oklch( 1   0   0 )')).not.toThrow();
      expect(() => oklchStringToHex('oklch(1  0  0  /  0.5)')).not.toThrow();
    });
  });

  describe('getCompatibleColor', () => {
    test('returns mapped colors for known OKLCH values', () => {
      expect(getCompatibleColor('oklch(1 0 0)')).toBe('#ffffff');
      expect(getCompatibleColor('oklch(0.145 0 0)')).toBe('#252525');
    });

    test('falls back to conversion for unmapped colors', () => {
      const result = getCompatibleColor('oklch(0.5 0 0)');
      expect(result).toMatch(/^#[0-9a-f]{6}$/);
    });

    test('returns fallback for invalid colors', () => {
      const result = getCompatibleColor('invalid-color');
      expect(result).toBe('#000000');
    });

    test('handles all predefined color mappings', () => {
      Object.keys(COLOR_MAPPINGS).forEach(oklchValue => {
        const result = getCompatibleColor(oklchValue);
        expect(result).toBe(COLOR_MAPPINGS[oklchValue]);
      });
    });
  });

  describe('COLOR_MAPPINGS', () => {
    test('contains all expected color mappings', () => {
      // Test some key mappings
      expect(COLOR_MAPPINGS['oklch(1 0 0)']).toBe('#ffffff');
      expect(COLOR_MAPPINGS['oklch(0.145 0 0)']).toBe('#252525');
      expect(COLOR_MAPPINGS['oklch(0.577 0.245 27.325)']).toBe('#dc2626');
    });

    test('all mapped colors are valid hex colors', () => {
      Object.values(COLOR_MAPPINGS).forEach(hexColor => {
        if (hexColor.startsWith('#')) {
          expect(hexColor).toMatch(/^#[0-9a-f]{6}([0-9a-f]{2})?$/i);
        } else if (hexColor.startsWith('rgba')) {
          expect(hexColor).toMatch(/^rgba\(\d+,\s*\d+,\s*\d+,\s*[\d.]+\)$/);
        }
      });
    });
  });

  describe('Edge cases and error handling', () => {
    test('handles NaN and Infinity values gracefully', () => {
      const invalidOklch: OKLCHColor = { l: NaN, c: Infinity, h: -Infinity };
      const result = oklchToRgb(invalidOklch);
      
      expect(result.r).toBeGreaterThanOrEqual(0);
      expect(result.r).toBeLessThanOrEqual(255);
      expect(result.g).toBeGreaterThanOrEqual(0);
      expect(result.g).toBeLessThanOrEqual(255);
      expect(result.b).toBeGreaterThanOrEqual(0);
      expect(result.b).toBeLessThanOrEqual(255);
    });

    test('handles negative RGB values', () => {
      const negativeRgb: RGBColor = { r: -10, g: -5, b: -1 };
      const hex = rgbToHex(negativeRgb);
      expect(hex).toBe('#000000');
    });

    test('handles RGB values over 255', () => {
      const largeRgb: RGBColor = { r: 300, g: 400, b: 500 };
      const hex = rgbToHex(largeRgb);
      expect(hex).toBe('#ffffff');
    });
  });
}); 