/**
 * PDF Style Compatibility utilities
 * Handles style preprocessing for html2canvas and PDF generation
 */

import { COLOR_MAPPINGS, getCompatibleColor } from './colorConverter';

export interface StyleCompatibilityOptions {
  enableColorConversion?: boolean;
  enableFontFallbacks?: boolean;
  enableLayoutOptimization?: boolean;
}

/**
 * Apply PDF-compatible styles to an element before rendering
 */
export function applyPdfCompatibleStyles(
  element: HTMLElement,
  options: StyleCompatibilityOptions = {}
): () => void {
  const {
    enableColorConversion = true,
    enableFontFallbacks = true,
    enableLayoutOptimization = true
  } = options;

  const originalStyles = new Map<HTMLElement, string>();
  const elementsToRestore: HTMLElement[] = [];

  try {
    // Process all elements recursively
    processElementStyles(element, originalStyles, elementsToRestore, {
      enableColorConversion,
      enableFontFallbacks,
      enableLayoutOptimization
    });

    // Return cleanup function
    return () => {
      restoreOriginalStyles(originalStyles);
    };

  } catch (error) {
    console.error('Failed to apply PDF compatible styles:', error);
    // Return no-op cleanup function
    return () => {};
  }
}

/**
 * Process styles for an element and its children
 */
function processElementStyles(
  element: HTMLElement,
  originalStyles: Map<HTMLElement, string>,
  elementsToRestore: HTMLElement[],
  options: Required<StyleCompatibilityOptions>
): void {
  // Store original style
  originalStyles.set(element, element.style.cssText);
  elementsToRestore.push(element);

  const computedStyle = window.getComputedStyle(element);
  const newStyles: Record<string, string> = {};

  // Convert OKLCH colors to compatible formats
  if (options.enableColorConversion) {
    convertColorsInStyle(computedStyle, newStyles);
  }

  // Apply font fallbacks for better PDF rendering
  if (options.enableFontFallbacks) {
    applyFontFallbacks(computedStyle, newStyles);
  }

  // Optimize layout for PDF
  if (options.enableLayoutOptimization) {
    optimizeLayoutForPdf(computedStyle, newStyles);
  }

  // Apply new styles
  Object.entries(newStyles).forEach(([property, value]) => {
    element.style.setProperty(property, value, 'important');
  });

  // Process children
  Array.from(element.children).forEach(child => {
    if (child instanceof HTMLElement) {
      processElementStyles(child, originalStyles, elementsToRestore, options);
    }
  });
}

/**
 * Convert OKLCH and other incompatible colors
 */
function convertColorsInStyle(
  computedStyle: CSSStyleDeclaration,
  newStyles: Record<string, string>
): void {
  const colorProperties = [
    'color',
    'background-color',
    'border-color',
    'border-top-color',
    'border-right-color',
    'border-bottom-color',
    'border-left-color',
    'outline-color',
    'text-decoration-color',
    'fill',
    'stroke'
  ];

  colorProperties.forEach(property => {
    const value = computedStyle.getPropertyValue(property);
    if (value && value.includes('oklch')) {
      const compatibleColor = getCompatibleColor(value);
      newStyles[property] = compatibleColor;
    }
  });

  // Handle CSS custom properties (variables)
  for (let i = 0; i < computedStyle.length; i++) {
    const property = computedStyle[i];
    if (property.startsWith('--')) {
      const value = computedStyle.getPropertyValue(property);
      if (value && value.includes('oklch')) {
        const compatibleColor = getCompatibleColor(value);
        newStyles[property] = compatibleColor;
      }
    }
  }
}

/**
 * Apply font fallbacks for better PDF rendering
 */
function applyFontFallbacks(
  computedStyle: CSSStyleDeclaration,
  newStyles: Record<string, string>
): void {
  const fontFamily = computedStyle.fontFamily;
  
  if (fontFamily) {
    // Add safe fallbacks for common fonts
    const fallbackMappings: Record<string, string> = {
      'Inter': 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      'Geist': 'Geist, "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif',
      'system-ui': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'
    };

    Object.entries(fallbackMappings).forEach(([fontName, fallback]) => {
      if (fontFamily.includes(fontName)) {
        newStyles['font-family'] = fallback;
      }
    });
  }
}

/**
 * Optimize layout properties for PDF rendering
 */
function optimizeLayoutForPdf(
  computedStyle: CSSStyleDeclaration,
  newStyles: Record<string, string>
): void {
  // Force RGB color space for better compatibility
  const backgroundColor = computedStyle.backgroundColor;
  if (backgroundColor && backgroundColor !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'transparent') {
    // Ensure background color is explicitly set for PDF
    newStyles['background-color'] = backgroundColor;
  }

  // Improve text rendering
  newStyles['-webkit-font-smoothing'] = 'antialiased';
  newStyles['-moz-osx-font-smoothing'] = 'grayscale';
  
  // Optimize for print media
  newStyles['color-adjust'] = 'exact';
  newStyles['-webkit-print-color-adjust'] = 'exact';
  
  // Ensure borders are visible
  const borderWidth = computedStyle.borderWidth;
  if (borderWidth && borderWidth !== '0px') {
    newStyles['border-style'] = 'solid';
  }
}

/**
 * Restore original styles
 */
function restoreOriginalStyles(originalStyles: Map<HTMLElement, string>): void {
  originalStyles.forEach((originalCssText, element) => {
    try {
      element.style.cssText = originalCssText;
    } catch (error) {
      console.warn('Failed to restore style for element:', element, error);
    }
  });
}

/**
 * Pre-process CSS text to replace OKLCH colors
 */
export function preprocessCssText(cssText: string): string {
  let processedCss = cssText;
  
  // Replace OKLCH color functions with compatible alternatives
  Object.entries(COLOR_MAPPINGS).forEach(([oklch, hex]) => {
    const escapedOklch = oklch.replace(/[()]/g, '\\$&');
    const regex = new RegExp(escapedOklch, 'gi');
    processedCss = processedCss.replace(regex, hex);
  });
  
  return processedCss;
}

/**
 * Create a style element with PDF-compatible CSS
 */
export function createPdfCompatibleStyleElement(cssContent: string): HTMLStyleElement {
  const style = document.createElement('style');
  style.type = 'text/css';
  style.textContent = preprocessCssText(cssContent);
  return style;
}

/**
 * Apply temporary PDF-compatible styles to document
 */
export function applyTemporaryPdfStyles(): () => void {
  const styleElement = createPdfCompatibleStyleElement(`
    * {
      -webkit-print-color-adjust: exact !important;
      color-adjust: exact !important;
    }
    
    .pdf-optimized {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
      -webkit-font-smoothing: antialiased !important;
      -moz-osx-font-smoothing: grayscale !important;
    }
    
    .pdf-header {
      display: block !important;
      page-break-after: avoid !important;
    }
    
    .pdf-hide {
      display: none !important;
    }
  `);
  
  document.head.appendChild(styleElement);
  
  // Return cleanup function
  return () => {
    if (styleElement.parentNode) {
      styleElement.parentNode.removeChild(styleElement);
    }
  };
} 