/**
 * Test script for PDF export functionality
 * Tests the OKLCH color compatibility fixes
 */

import { applyPdfCompatibleStyles, applyTemporaryPdfStyles } from './pdfStyleCompat';
import { oklchToRgb, rgbToHex, getCompatibleColor } from './colorConverter';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

export interface TestResult {
  success: boolean;
  error?: string;
  details?: string;
}

/**
 * Test color conversion functionality
 */
export function testColorConversion(): TestResult {
  try {
    // Test OKLCH to RGB conversion
    const whiteOklch = { l: 1, c: 0, h: 0 };
    const whiteRgb = oklchToRgb(whiteOklch);
    
    if (whiteRgb.r !== 255 || whiteRgb.g !== 255 || whiteRgb.b !== 255) {
      return {
        success: false,
        error: 'OKLCH white conversion failed',
        details: `Expected rgb(255,255,255), got rgb(${whiteRgb.r},${whiteRgb.g},${whiteRgb.b})`
      };
    }

    // Test RGB to HEX conversion
    const whiteHex = rgbToHex(whiteRgb);
    if (whiteHex !== '#ffffff') {
      return {
        success: false,
        error: 'RGB to HEX conversion failed',
        details: `Expected #ffffff, got ${whiteHex}`
      };
    }

    // Test getCompatibleColor function
    const compatibleColor = getCompatibleColor('oklch(1 0 0)');
    if (!compatibleColor.startsWith('#') && !compatibleColor.startsWith('rgb')) {
      return {
        success: false,
        error: 'getCompatibleColor failed',
        details: `Expected valid color format, got ${compatibleColor}`
      };
    }

    return { success: true, details: 'All color conversion tests passed' };

  } catch (error) {
    return {
      success: false,
      error: 'Color conversion test failed',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test PDF style compatibility functions
 */
export function testPdfStyleCompatibility(): TestResult {
  try {
    // Create a test element with OKLCH colors
    const testElement = document.createElement('div');
    testElement.innerHTML = `
      <div style="color: oklch(0.5 0 0); background-color: oklch(1 0 0);">
        Test content with OKLCH colors
      </div>
    `;
    document.body.appendChild(testElement);

    // Apply PDF compatible styles
    const cleanup = applyPdfCompatibleStyles(testElement);
    
    // Check if styles were applied
    const childElement = testElement.firstElementChild as HTMLElement;
    const computedStyle = window.getComputedStyle(childElement);
    
    // Verify that OKLCH colors have been converted
    const color = computedStyle.color;
    const backgroundColor = computedStyle.backgroundColor;
    
    // Clean up
    cleanup();
    document.body.removeChild(testElement);

    if (color.includes('oklch') || backgroundColor.includes('oklch')) {
      return {
        success: false,
        error: 'OKLCH colors not converted',
        details: `Color: ${color}, Background: ${backgroundColor}`
      };
    }

    return { success: true, details: 'PDF style compatibility test passed' };

  } catch (error) {
    return {
      success: false,
      error: 'PDF style compatibility test failed',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test html2canvas with compatible styles
 */
export async function testHtml2CanvasCompatibility(): Promise<TestResult> {
  try {
    // Create a test element
    const testElement = document.createElement('div');
    testElement.style.width = '200px';
    testElement.style.height = '100px';
    testElement.style.backgroundColor = '#ffffff';
    testElement.style.color = '#000000';
    testElement.innerHTML = 'Test content for html2canvas';
    document.body.appendChild(testElement);

    // Apply PDF compatible styles
    const cleanup = applyPdfCompatibleStyles(testElement);
    
    // Test html2canvas rendering
    const canvas = await html2canvas(testElement, {
      width: 200,
      height: 100,
      backgroundColor: '#ffffff'
    });

    // Clean up
    cleanup();
    document.body.removeChild(testElement);

    if (!canvas || canvas.width === 0 || canvas.height === 0) {
      return {
        success: false,
        error: 'html2canvas failed to generate canvas',
        details: `Canvas dimensions: ${canvas?.width || 0}x${canvas?.height || 0}`
      };
    }

    return { success: true, details: 'html2canvas compatibility test passed' };

  } catch (error) {
    return {
      success: false,
      error: 'html2canvas compatibility test failed',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Test complete PDF generation workflow
 */
export async function testPdfGeneration(): Promise<TestResult> {
  try {
    // Create a test element with complex content
    const testElement = document.createElement('div');
    testElement.innerHTML = `
      <div style="padding: 20px; font-family: Arial, sans-serif;">
        <h1 style="color: #333333;">PDF Export Test</h1>
        <p style="color: #666666;">This is a test of the PDF export functionality.</p>
        <div style="background-color: #f0f0f0; padding: 10px; margin: 10px 0;">
          Content with background color
        </div>
      </div>
    `;
    document.body.appendChild(testElement);

    // Apply temporary global PDF styles
    const cleanupGlobal = applyTemporaryPdfStyles();
    
    // Apply element-specific PDF styles
    const cleanupElement = applyPdfCompatibleStyles(testElement);

    // Generate canvas
    const canvas = await html2canvas(testElement, {
      scale: 1,
      backgroundColor: '#ffffff',
      width: testElement.scrollWidth,
      height: testElement.scrollHeight
    });

    // Create PDF
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4'
    });

    const imgData = canvas.toDataURL('image/png');
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = pdf.internal.pageSize.getHeight();
    
    // Calculate dimensions maintaining aspect ratio
    const imgWidth = canvas.width;
    const imgHeight = canvas.height;
    const ratio = Math.min(pdfWidth / imgWidth, pdfHeight / imgHeight);
    const scaledWidth = imgWidth * ratio;
    const scaledHeight = imgHeight * ratio;

    pdf.addImage(imgData, 'PNG', 0, 0, scaledWidth, scaledHeight);

    // Clean up
    cleanupElement();
    cleanupGlobal();
    document.body.removeChild(testElement);

    // Verify PDF was created (basic check)
    const pdfBlob = pdf.output('blob');
    if (!pdfBlob || pdfBlob.size === 0) {
      return {
        success: false,
        error: 'PDF generation failed',
        details: 'Generated PDF is empty'
      };
    }

    return { 
      success: true, 
      details: `PDF generated successfully (${pdfBlob.size} bytes)` 
    };

  } catch (error) {
    return {
      success: false,
      error: 'PDF generation test failed',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Run all PDF export tests
 */
export async function runAllTests(): Promise<{
  colorConversion: TestResult;
  pdfStyleCompatibility: TestResult;
  html2CanvasCompatibility: TestResult;
  pdfGeneration: TestResult;
  overall: TestResult;
}> {
  console.log('Running PDF export tests...');

  const colorConversion = testColorConversion();
  const pdfStyleCompatibility = testPdfStyleCompatibility();
  const html2CanvasCompatibility = await testHtml2CanvasCompatibility();
  const pdfGeneration = await testPdfGeneration();

  const allPassed = [
    colorConversion,
    pdfStyleCompatibility,
    html2CanvasCompatibility,
    pdfGeneration
  ].every(result => result.success);

  const overall: TestResult = {
    success: allPassed,
    details: allPassed 
      ? 'All PDF export tests passed successfully' 
      : 'Some PDF export tests failed'
  };

  return {
    colorConversion,
    pdfStyleCompatibility,
    html2CanvasCompatibility,
    pdfGeneration,
    overall
  };
}

// Export for use in browser console or testing
if (typeof window !== 'undefined') {
  (window as any).testPdfExport = {
    testColorConversion,
    testPdfStyleCompatibility,
    testHtml2CanvasCompatibility,
    testPdfGeneration,
    runAllTests
  };
} 