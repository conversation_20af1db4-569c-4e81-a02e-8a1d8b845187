/**
 * 股票代码转换工具
 * 将各种格式的股票代码转换为TradingView支持的格式
 */

export interface MarketInfo {
  market: 'US' | 'CN' | 'HK' | 'UNKNOWN';
  exchange: string;
  originalSymbol: string;
  tradingViewSymbol: string;
}

/**
 * 识别股票代码所属市场
 */
export function identifyMarket(symbol: string): MarketInfo['market'] {
  const cleanSymbol = symbol.trim().toUpperCase();
  
  // A股代码模式
  const aStockPatterns = [
    /^\d{6}$/,                    // 6位数字 (如 000001, 600519)
    /^\d{6}\.(SH|SZ)$/,          // 带交易所后缀 (如 000001.SZ, 600519.SH)
  ];
  
  // 美股代码模式
  const usStockPatterns = [
    /^[A-Z]{1,5}$/,              // 1-5位字母 (如 AAPL, TSLA, GOOGL)
    /^[A-Z]{1,5}\.(US|NASDAQ|NYSE)$/,  // 带交易所后缀
  ];
  
  // 港股代码模式
  const hkStockPatterns = [
    /^\d{4}$/,                   // 4位数字 (如 0700, 0005)
    /^\d{4}\.HK$/,              // 带HK后缀 (如 0700.HK)
  ];
  
  if (aStockPatterns.some(pattern => pattern.test(cleanSymbol))) {
    return 'CN';
  }
  
  if (hkStockPatterns.some(pattern => pattern.test(cleanSymbol))) {
    return 'HK';
  }
  
  if (usStockPatterns.some(pattern => pattern.test(cleanSymbol))) {
    return 'US';
  }
  
  return 'UNKNOWN';
}

/**
 * 获取A股交易所代码
 */
function getChineseExchange(symbol: string): string {
  const cleanSymbol = symbol.replace(/\.(SH|SZ)$/, '');
  
  // 明确指定了交易所
  if (symbol.endsWith('.SH')) return 'SSE';
  if (symbol.endsWith('.SZ')) return 'SZSE';
  
  // 根据代码规则推断交易所
  const code = cleanSymbol;
  
  // 上交所代码规则
  if (code.startsWith('6') || code.startsWith('9')) {
    return 'SSE';
  }
  
  // 深交所代码规则
  if (code.startsWith('0') || code.startsWith('2') || code.startsWith('3')) {
    return 'SZSE';
  }
  
  // 默认深交所
  return 'SZSE';
}

/**
 * 获取美股交易所代码
 */
function getUSExchange(symbol: string): string {
  const cleanSymbol = symbol.replace(/\.(US|NASDAQ|NYSE)$/, '');
  
  // 明确指定了交易所
  if (symbol.endsWith('.NASDAQ')) return 'NASDAQ';
  if (symbol.endsWith('.NYSE')) return 'NYSE';
  if (symbol.endsWith('.US')) return 'NASDAQ'; // 默认NASDAQ
  
  // 常见NASDAQ股票
  const nasdaqStocks = [
    'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'TSLA', 'META', 'NVDA',
    'NFLX', 'ADBE', 'CRM', 'ORCL', 'INTC', 'AMD', 'QCOM', 'AVGO'
  ];
  
  // 常见NYSE股票
  const nyseStocks = [
    'JPM', 'JNJ', 'V', 'PG', 'UNH', 'HD', 'MA', 'DIS', 'BAC', 'XOM',
    'WMT', 'CVX', 'LLY', 'ABBV', 'PFE', 'TMO', 'COST', 'MRK', 'AVGO'
  ];
  
  if (nasdaqStocks.includes(cleanSymbol)) {
    return 'NASDAQ';
  }
  
  if (nyseStocks.includes(cleanSymbol)) {
    return 'NYSE';
  }
  
  // 默认NASDAQ
  return 'NASDAQ';
}

/**
 * 转换股票代码为TradingView格式
 */
export function convertToTradingViewSymbol(symbol: string): MarketInfo {
  const cleanSymbol = symbol.trim().toUpperCase();
  const market = identifyMarket(cleanSymbol);
  
  let exchange: string;
  let tradingViewSymbol: string;
  const originalSymbol = cleanSymbol;
  
  switch (market) {
    case 'CN':
      exchange = getChineseExchange(cleanSymbol);
      const cnCode = cleanSymbol.replace(/\.(SH|SZ)$/, '');
      tradingViewSymbol = `${exchange}:${cnCode}`;
      break;
      
    case 'US':
      exchange = getUSExchange(cleanSymbol);
      const usCode = cleanSymbol.replace(/\.(US|NASDAQ|NYSE)$/, '');
      tradingViewSymbol = `${exchange}:${usCode}`;
      break;
      
    case 'HK':
      exchange = 'HKEX';
      const hkCode = cleanSymbol.replace(/\.HK$/, '');
      // 港股代码需要补齐到4位
      const paddedHkCode = hkCode.padStart(4, '0');
      tradingViewSymbol = `${exchange}:${paddedHkCode}`;
      break;
      
    default:
      // 未识别的格式，默认作为美股NASDAQ处理
      exchange = 'NASDAQ';
      tradingViewSymbol = `NASDAQ:${cleanSymbol}`;
      break;
  }
  
  return {
    market,
    exchange,
    originalSymbol,
    tradingViewSymbol
  };
}

/**
 * 批量转换股票代码
 */
export function convertMultipleSymbols(symbols: string[]): MarketInfo[] {
  return symbols.map(symbol => convertToTradingViewSymbol(symbol));
}

/**
 * 验证TradingView符号格式
 */
export function isValidTradingViewSymbol(symbol: string): boolean {
  const pattern = /^[A-Z]+:[A-Z0-9]+$/;
  return pattern.test(symbol.toUpperCase());
}

/**
 * 从TradingView符号提取原始代码
 */
export function extractOriginalSymbol(tradingViewSymbol: string): string {
  const parts = tradingViewSymbol.split(':');
  return parts.length === 2 ? parts[1] : tradingViewSymbol;
} 