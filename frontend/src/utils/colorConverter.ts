/**
 * Color conversion utilities for converting OKLCH colors to RGB/HEX formats
 * to ensure compatibility with html2canvas and PDF generation
 */

export interface OKLCHColor {
  l: number; // Lightness (0-1)
  c: number; // Chroma (0-1)
  h: number; // Hue (0-360)
  alpha?: number; // Alpha (0-1)
}

export interface RGBColor {
  r: number; // Red (0-255)
  g: number; // Green (0-255)
  b: number; // Blue (0-255)
  alpha?: number; // Alpha (0-1)
}

/**
 * Convert OKLCH to RGB
 * Simplified conversion for the specific OKLCH values used in our CSS
 */
export function oklchToRgb(oklch: OKLCHColor): RGBColor {
  const { l, c, h, alpha = 1 } = oklch;
  
  // For achromatic colors (c = 0), convert lightness directly to RGB
  if (c === 0) {
    const value = Math.round(l * 255);
    return { r: value, g: value, b: value, alpha };
  }
  
  // For specific hues in our CSS, use pre-calculated RGB values
  const hueRounded = Math.round(h);
  
  // Convert based on lightness and chroma values
  let r, g, b;
  
  if (hueRounded >= 20 && hueRounded <= 30) { // Reds/oranges
    r = Math.round(l * 255 * (1 + c * 0.3));
    g = Math.round(l * 255 * (1 - c * 0.2));
    b = Math.round(l * 255 * (1 - c * 0.8));
  } else if (hueRounded >= 70 && hueRounded <= 90) { // Yellows/greens
    r = Math.round(l * 255 * (1 + c * 0.2));
    g = Math.round(l * 255 * (1 + c * 0.3));
    b = Math.round(l * 255 * (1 - c * 0.5));
  } else if (hueRounded >= 160 && hueRounded <= 190) { // Cyans/blues
    r = Math.round(l * 255 * (1 - c * 0.5));
    g = Math.round(l * 255 * (1 + c * 0.2));
    b = Math.round(l * 255 * (1 + c * 0.3));
  } else if (hueRounded >= 220 && hueRounded <= 240) { // Blues
    r = Math.round(l * 255 * (1 - c * 0.8));
    g = Math.round(l * 255 * (1 - c * 0.2));
    b = Math.round(l * 255 * (1 + c * 0.3));
  } else if (hueRounded >= 260 && hueRounded <= 280) { // Purples
    r = Math.round(l * 255 * (1 + c * 0.1));
    g = Math.round(l * 255 * (1 - c * 0.3));
    b = Math.round(l * 255 * (1 + c * 0.3));
  } else if (hueRounded >= 300 && hueRounded <= 320) { // Magentas
    r = Math.round(l * 255 * (1 + c * 0.3));
    g = Math.round(l * 255 * (1 - c * 0.3));
    b = Math.round(l * 255 * (1 + c * 0.1));
  } else {
    // Default conversion for other hues
    r = Math.round(l * 255);
    g = Math.round(l * 255);
    b = Math.round(l * 255);
  }
  
  // Clamp values to 0-255 range
  r = Math.max(0, Math.min(255, r));
  g = Math.max(0, Math.min(255, g));
  b = Math.max(0, Math.min(255, b));
  
  return { r, g, b, alpha };
}

/**
 * Convert RGB to HEX format
 */
export function rgbToHex(rgb: RGBColor): string {
  const { r, g, b, alpha = 1 } = rgb;
  
  const toHex = (value: number) => {
    const hex = Math.round(value).toString(16);
    return hex.length === 1 ? '0' + hex : hex;
  };
  
  const hexColor = `#${toHex(r)}${toHex(g)}${toHex(b)}`;
  
  if (alpha < 1) {
    const alphaHex = toHex(alpha * 255);
    return `${hexColor}${alphaHex}`;
  }
  
  return hexColor;
}

/**
 * Convert OKLCH string to HEX
 */
export function oklchStringToHex(oklchString: string): string {
  // Parse oklch(l c h) or oklch(l c h / alpha) format
  const match = oklchString.match(/oklch\(\s*([\d.]+)\s+([\d.]+)\s+([\d.]+)(?:\s*\/\s*([\d.%]+))?\s*\)/);
  
  if (!match) {
    throw new Error(`Invalid OKLCH format: ${oklchString}`);
  }
  
  const l = parseFloat(match[1]);
  const c = parseFloat(match[2]);
  const h = parseFloat(match[3]);
  let alpha = 1;
  
  if (match[4]) {
    if (match[4].includes('%')) {
      alpha = parseFloat(match[4]) / 100;
    } else {
      alpha = parseFloat(match[4]);
    }
  }
  
  const rgb = oklchToRgb({ l, c, h, alpha });
  return rgbToHex(rgb);
}

/**
 * Pre-calculated color mappings for our specific OKLCH values
 */
export const COLOR_MAPPINGS: Record<string, string> = {
  // Light mode colors
  'oklch(1 0 0)': '#ffffff',                    // Pure white
  'oklch(0.145 0 0)': '#252525',               // Very dark gray
  'oklch(0.205 0 0)': '#343434',               // Dark gray
  'oklch(0.985 0 0)': '#fbfbfb',               // Near white
  'oklch(0.97 0 0)': '#f7f7f7',                // Light gray
  'oklch(0.556 0 0)': '#8e8e8e',               // Medium gray
  'oklch(0.922 0 0)': '#ebebeb',               // Very light gray
  'oklch(0.708 0 0)': '#b5b5b5',               // Light medium gray
  'oklch(0.577 0.245 27.325)': '#dc2626',      // Red (destructive)
  'oklch(0.646 0.222 41.116)': '#ea580c',      // Orange (chart-1)
  'oklch(0.6 0.118 184.704)': '#0891b2',       // Cyan (chart-2)
  'oklch(0.398 0.07 227.392)': '#1e40af',      // Blue (chart-3)
  'oklch(0.828 0.189 84.429)': '#65a30d',      // Green (chart-4)
  'oklch(0.769 0.188 70.08)': '#ca8a04',       // Yellow (chart-5)
  
  // Dark mode colors
  'oklch(0.269 0 0)': '#444444',               // Dark gray
  'oklch(0.704 0.191 22.216)': '#ef4444',      // Red (destructive dark)
  'oklch(0.488 0.243 264.376)': '#8b5cf6',     // Purple (chart-1 dark)
  'oklch(0.696 0.17 162.48)': '#10b981',       // Green (chart-2 dark)
  'oklch(0.627 0.265 303.9)': '#f59e0b',       // Orange (chart-4 dark)
  'oklch(0.645 0.246 16.439)': '#f97316',      // Orange (chart-5 dark)
  
  // Alpha variants
  'oklch(1 0 0 / 10%)': 'rgba(255, 255, 255, 0.1)',
  'oklch(1 0 0 / 15%)': 'rgba(255, 255, 255, 0.15)',
};

/**
 * Get compatible color value for PDF export
 */
export function getCompatibleColor(oklchValue: string): string {
  // Check if we have a pre-calculated mapping
  if (COLOR_MAPPINGS[oklchValue]) {
    return COLOR_MAPPINGS[oklchValue];
  }
  
  // Try to convert dynamically
  try {
    return oklchStringToHex(oklchValue);
  } catch (error) {
    console.warn(`Failed to convert OKLCH color: ${oklchValue}`, error);
    // Fallback to a safe default
    return '#000000';
  }
} 