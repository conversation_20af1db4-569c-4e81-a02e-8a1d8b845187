'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';

// 用户信息接口
export interface User {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  avatar?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// 认证状态接口
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 认证上下文接口
interface AuthContextType extends AuthState {
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateUser: (userData: Partial<User>) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  clearError: () => void;
}

// 注册数据接口
interface RegisterData {
  username: string;
  email: string;
  password: string;
  fullName?: string;
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 认证提供者组件
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  // 获取存储的令牌
  const getStoredTokens = useCallback(() => {
    if (typeof window === 'undefined') return { accessToken: null, refreshToken: null };
    
    const accessToken = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    const refreshToken = localStorage.getItem('refresh_token') || sessionStorage.getItem('refresh_token');
    
    return { accessToken, refreshToken };
  }, []);

  // 存储令牌
  const storeTokens = useCallback((accessToken: string, refreshToken: string, remember: boolean = false) => {
    if (typeof window === 'undefined') return;
    
    if (remember) {
      localStorage.setItem('auth_token', accessToken);
      localStorage.setItem('refresh_token', refreshToken);
      // 清除sessionStorage中的令牌
      sessionStorage.removeItem('auth_token');
      sessionStorage.removeItem('refresh_token');
    } else {
      sessionStorage.setItem('auth_token', accessToken);
      sessionStorage.setItem('refresh_token', refreshToken);
      // 清除localStorage中的令牌
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
    }
  }, []);

  // 清除令牌
  const clearTokens = useCallback(() => {
    if (typeof window === 'undefined') return;
    
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    sessionStorage.removeItem('auth_token');
    sessionStorage.removeItem('refresh_token');
  }, []);

  // API请求辅助函数
  const apiRequest = useCallback(async (url: string, options: RequestInit = {}) => {
    const { accessToken } = getStoredTokens();
    
    const headers = {
      'Content-Type': 'application/json',
      ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
      ...options.headers,
    };

    const response = await fetch(`http://127.0.0.1:8000${url}`, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ detail: '请求失败' }));
      throw new Error(errorData.detail || `HTTP ${response.status}`);
    }

    return response.json();
  }, [getStoredTokens]);

  // 获取当前用户信息
  const fetchCurrentUser = useCallback(async () => {
    try {
      const userData = await apiRequest('/auth/me');
      setAuthState(prev => ({
        ...prev,
        user: userData,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      }));
    } catch (error) {
      console.error('Failed to fetch current user:', error);
      clearTokens();
      setAuthState(prev => ({
        ...prev,
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      }));
    }
  }, [apiRequest, clearTokens]);

  // 刷新令牌
  const refreshToken = useCallback(async () => {
    try {
      const { refreshToken: storedRefreshToken } = getStoredTokens();
      
      if (!storedRefreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch('http://127.0.0.1:8000/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${storedRefreshToken}`,
        },
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const { access_token, refresh_token } = await response.json();
      
      // 保持原有的存储方式（localStorage或sessionStorage）
      const isRemembered = !!localStorage.getItem('auth_token');
      storeTokens(access_token, refresh_token, isRemembered);
      
      return access_token;
    } catch (error) {
      console.error('Token refresh failed:', error);
      clearTokens();
      setAuthState(prev => ({
        ...prev,
        user: null,
        isAuthenticated: false,
        error: '登录已过期，请重新登录',
      }));
      throw error;
    }
  }, [getStoredTokens, storeTokens, clearTokens]);

  // 登录
  const login = useCallback(async (email: string, password: string, rememberMe: boolean = false) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await fetch('http://127.0.0.1:8000/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: email,
          password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '登录失败');
      }

      const { access_token, refresh_token, user } = await response.json();
      
      storeTokens(access_token, refresh_token, rememberMe);
      
      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });

    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : '登录失败',
      }));
      throw error;
    }
  }, [storeTokens]);

  // 注册
  const register = useCallback(async (userData: RegisterData) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await fetch('http://127.0.0.1:8000/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: userData.username,
          email: userData.email,
          password: userData.password,
          full_name: userData.fullName || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '注册失败');
      }

      // 注册成功后自动登录
      await login(userData.email, userData.password, false);

    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : '注册失败',
      }));
      throw error;
    }
  }, [login]);

  // 登出
  const logout = useCallback(async () => {
    try {
      await apiRequest('/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      clearTokens();
      setAuthState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
      router.push('/login');
    }
  }, [apiRequest, clearTokens, router]);

  // 更新用户信息
  const updateUser = useCallback(async (userData: Partial<User>) => {
    try {
      const updatedUser = await apiRequest('/auth/me', {
        method: 'PUT',
        body: JSON.stringify(userData),
      });

      setAuthState(prev => ({
        ...prev,
        user: updatedUser,
        error: null,
      }));
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '更新用户信息失败',
      }));
      throw error;
    }
  }, [apiRequest]);

  // 修改密码
  const changePassword = useCallback(async (currentPassword: string, newPassword: string) => {
    try {
      await apiRequest('/auth/password', {
        method: 'PUT',
        body: JSON.stringify({
          current_password: currentPassword,
          new_password: newPassword,
        }),
      });

      setAuthState(prev => ({ ...prev, error: null }));
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : '修改密码失败',
      }));
      throw error;
    }
  }, [apiRequest]);

  // 清除错误
  const clearError = useCallback(() => {
    setAuthState(prev => ({ ...prev, error: null }));
  }, []);

  // 初始化认证状态
  useEffect(() => {
    const initializeAuth = async () => {
      const { accessToken } = getStoredTokens();
      
      if (accessToken) {
        try {
          await fetchCurrentUser();
        } catch (error) {
          // 如果获取用户信息失败，尝试刷新令牌
          try {
            await refreshToken();
            await fetchCurrentUser();
          } catch (refreshError) {
            // 刷新失败，清除认证状态
            setAuthState(prev => ({
              ...prev,
              isLoading: false,
            }));
          }
        }
      } else {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
        }));
      }
    };

    initializeAuth();
  }, [getStoredTokens, fetchCurrentUser, refreshToken]);

  // 设置令牌刷新定时器
  useEffect(() => {
    if (!authState.isAuthenticated) return;

    // 每15分钟尝试刷新令牌
    const interval = setInterval(async () => {
      try {
        await refreshToken();
      } catch (error) {
        console.error('Automatic token refresh failed:', error);
      }
    }, 15 * 60 * 1000); // 15分钟

    return () => clearInterval(interval);
  }, [authState.isAuthenticated, refreshToken]);

  const contextValue: AuthContextType = {
    ...authState,
    login,
    register,
    logout,
    refreshToken,
    updateUser,
    changePassword,
    clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// 使用认证上下文的Hook
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 