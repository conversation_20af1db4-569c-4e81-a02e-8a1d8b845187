/**
 * Color constants in traditional RGB/HEX formats
 * Compatible with html2canvas and PDF generation
 */

export const COLORS = {
  // Light mode colors
  LIGHT: {
    background: '#ffffff',
    foreground: '#252525',
    card: '#ffffff',
    cardForeground: '#252525',
    popover: '#ffffff',
    popoverForeground: '#252525',
    primary: '#343434',
    primaryForeground: '#fbfbfb',
    secondary: '#f7f7f7',
    secondaryForeground: '#343434',
    muted: '#f7f7f7',
    mutedForeground: '#8e8e8e',
    accent: '#f7f7f7',
    accentForeground: '#343434',
    destructive: '#dc2626',
    border: '#ebebeb',
    input: '#ebebeb',
    ring: '#b5b5b5',
    chart1: '#ea580c',
    chart2: '#0891b2',
    chart3: '#1e40af',
    chart4: '#65a30d',
    chart5: '#ca8a04',
    sidebar: '#fbfbfb',
    sidebarForeground: '#252525',
    sidebarPrimary: '#343434',
    sidebarPrimaryForeground: '#fbfbfb',
    sidebarAccent: '#f7f7f7',
    sidebarAccentForeground: '#343434',
    sidebarBorder: '#ebebeb',
    sidebarRing: '#b5b5b5',
  },
  
  // Dark mode colors
  DARK: {
    background: '#252525',
    foreground: '#fbfbfb',
    card: '#343434',
    cardForeground: '#fbfbfb',
    popover: '#343434',
    popoverForeground: '#fbfbfb',
    primary: '#ebebeb',
    primaryForeground: '#343434',
    secondary: '#444444',
    secondaryForeground: '#fbfbfb',
    muted: '#444444',
    mutedForeground: '#b5b5b5',
    accent: '#444444',
    accentForeground: '#fbfbfb',
    destructive: '#ef4444',
    border: 'rgba(255, 255, 255, 0.1)',
    input: 'rgba(255, 255, 255, 0.15)',
    ring: '#8e8e8e',
    chart1: '#8b5cf6',
    chart2: '#10b981',
    chart3: '#ca8a04',
    chart4: '#f59e0b',
    chart5: '#f97316',
    sidebar: '#343434',
    sidebarForeground: '#fbfbfb',
    sidebarPrimary: '#8b5cf6',
    sidebarPrimaryForeground: '#fbfbfb',
    sidebarAccent: '#444444',
    sidebarAccentForeground: '#fbfbfb',
    sidebarBorder: 'rgba(255, 255, 255, 0.1)',
    sidebarRing: '#8e8e8e',
  }
} as const;

export type ColorMode = keyof typeof COLORS;
export type ColorKey = keyof typeof COLORS.LIGHT;

/**
 * Get color value by mode and key
 */
export function getColor(mode: ColorMode, key: ColorKey): string {
  return COLORS[mode][key];
}

/**
 * Get current theme colors (detects system preference)
 */
export function getCurrentThemeColors(): typeof COLORS.LIGHT | typeof COLORS.DARK {
  if (typeof window !== 'undefined') {
    const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    return isDark ? COLORS.DARK : COLORS.LIGHT;
  }
  return COLORS.LIGHT;
}

/**
 * CSS custom properties mapping for traditional colors
 */
export const CSS_VARIABLES = {
  LIGHT: {
    '--background': COLORS.LIGHT.background,
    '--foreground': COLORS.LIGHT.foreground,
    '--card': COLORS.LIGHT.card,
    '--card-foreground': COLORS.LIGHT.cardForeground,
    '--popover': COLORS.LIGHT.popover,
    '--popover-foreground': COLORS.LIGHT.popoverForeground,
    '--primary': COLORS.LIGHT.primary,
    '--primary-foreground': COLORS.LIGHT.primaryForeground,
    '--secondary': COLORS.LIGHT.secondary,
    '--secondary-foreground': COLORS.LIGHT.secondaryForeground,
    '--muted': COLORS.LIGHT.muted,
    '--muted-foreground': COLORS.LIGHT.mutedForeground,
    '--accent': COLORS.LIGHT.accent,
    '--accent-foreground': COLORS.LIGHT.accentForeground,
    '--destructive': COLORS.LIGHT.destructive,
    '--border': COLORS.LIGHT.border,
    '--input': COLORS.LIGHT.input,
    '--ring': COLORS.LIGHT.ring,
    '--chart-1': COLORS.LIGHT.chart1,
    '--chart-2': COLORS.LIGHT.chart2,
    '--chart-3': COLORS.LIGHT.chart3,
    '--chart-4': COLORS.LIGHT.chart4,
    '--chart-5': COLORS.LIGHT.chart5,
    '--sidebar': COLORS.LIGHT.sidebar,
    '--sidebar-foreground': COLORS.LIGHT.sidebarForeground,
    '--sidebar-primary': COLORS.LIGHT.sidebarPrimary,
    '--sidebar-primary-foreground': COLORS.LIGHT.sidebarPrimaryForeground,
    '--sidebar-accent': COLORS.LIGHT.sidebarAccent,
    '--sidebar-accent-foreground': COLORS.LIGHT.sidebarAccentForeground,
    '--sidebar-border': COLORS.LIGHT.sidebarBorder,
    '--sidebar-ring': COLORS.LIGHT.sidebarRing,
  },
  DARK: {
    '--background': COLORS.DARK.background,
    '--foreground': COLORS.DARK.foreground,
    '--card': COLORS.DARK.card,
    '--card-foreground': COLORS.DARK.cardForeground,
    '--popover': COLORS.DARK.popover,
    '--popover-foreground': COLORS.DARK.popoverForeground,
    '--primary': COLORS.DARK.primary,
    '--primary-foreground': COLORS.DARK.primaryForeground,
    '--secondary': COLORS.DARK.secondary,
    '--secondary-foreground': COLORS.DARK.secondaryForeground,
    '--muted': COLORS.DARK.muted,
    '--muted-foreground': COLORS.DARK.mutedForeground,
    '--accent': COLORS.DARK.accent,
    '--accent-foreground': COLORS.DARK.accentForeground,
    '--destructive': COLORS.DARK.destructive,
    '--border': COLORS.DARK.border,
    '--input': COLORS.DARK.input,
    '--ring': COLORS.DARK.ring,
    '--chart-1': COLORS.DARK.chart1,
    '--chart-2': COLORS.DARK.chart2,
    '--chart-3': COLORS.DARK.chart3,
    '--chart-4': COLORS.DARK.chart4,
    '--chart-5': COLORS.DARK.chart5,
    '--sidebar': COLORS.DARK.sidebar,
    '--sidebar-foreground': COLORS.DARK.sidebarForeground,
    '--sidebar-primary': COLORS.DARK.sidebarPrimary,
    '--sidebar-primary-foreground': COLORS.DARK.sidebarPrimaryForeground,
    '--sidebar-accent': COLORS.DARK.sidebarAccent,
    '--sidebar-accent-foreground': COLORS.DARK.sidebarAccentForeground,
    '--sidebar-border': COLORS.DARK.sidebarBorder,
    '--sidebar-ring': COLORS.DARK.sidebarRing,
  }
} as const; 