/* TradingView Widget Styles */
.tradingview-widget-container {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 0.5rem;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.tradingview-widget-container__widget {
  position: relative;
  width: 100%;
  height: 100%;
}

.tradingview-widget-copyright {
  padding: 8px 12px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  text-align: center;
}

.tradingview-widget-copyright a {
  color: #64748b;
  text-decoration: none;
  font-size: 0.75rem;
  transition: color 0.2s ease;
}

.tradingview-widget-copyright a:hover {
  color: #475569;
}

/* Loading overlay styles */
.tradingview-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 32px; /* Account for copyright area */
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(2px);
}

.tradingview-loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.tradingview-loading-spinner .spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.tradingview-loading-spinner span {
  color: #64748b;
  font-size: 0.875rem;
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark theme support */
[data-theme="dark"] .tradingview-widget-container {
  background: #1e293b;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .tradingview-widget-copyright {
  background: #0f172a;
  border-top-color: #334155;
}

[data-theme="dark"] .tradingview-widget-copyright a {
  color: #94a3b8;
}

[data-theme="dark"] .tradingview-widget-copyright a:hover {
  color: #cbd5e1;
}

[data-theme="dark"] .tradingview-loading-overlay {
  background: rgba(30, 41, 59, 0.9);
}

[data-theme="dark"] .tradingview-loading-spinner .spinner {
  border-color: #334155;
  border-top-color: #60a5fa;
}

[data-theme="dark"] .tradingview-loading-spinner span {
  color: #94a3b8;
}

/* Responsive design */
@media (max-width: 768px) {
  .tradingview-widget-container {
    border-radius: 0.375rem;
  }
  
  .tradingview-widget-copyright {
    padding: 6px 8px;
  }
  
  .tradingview-widget-copyright a {
    font-size: 0.6875rem;
  }

  .tradingview-loading-spinner .spinner {
    width: 24px;
    height: 24px;
    border-width: 2px;
  }

  .tradingview-loading-spinner span {
    font-size: 0.8125rem;
  }
}

/* Loading state - optimized for performance */
.tradingview-widget-container.loading {
  background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

[data-theme="dark"] .tradingview-widget-container.loading {
  background: linear-gradient(90deg, #1e293b 25%, #334155 50%, #1e293b 75%);
  background-size: 200% 100%;
}

/* Integration with existing chart styles */
.chart-renderer .tradingview-widget-container {
  border: none;
  box-shadow: none;
  background: transparent;
}

.chart-renderer .tradingview-widget-copyright {
  background: transparent;
  border-top: 1px solid #e5e7eb;
  margin-top: 0.5rem;
}

[data-theme="dark"] .chart-renderer .tradingview-widget-copyright {
  border-top-color: #374151;
}

/* Enhanced container integration */
.enhanced-chart-container .tradingview-widget-container {
  border-radius: 0.75rem;
}

.enhanced-chart-container .tradingview-widget-container__widget {
  border-radius: 0.75rem 0.75rem 0 0;
}

/* Fullscreen support */
.chart-message.fullscreen .tradingview-widget-container {
  height: 80vh;
}

.chart-message.fullscreen .tradingview-widget-container__widget {
  height: calc(100% - 32px);
}

/* Accessibility improvements */
.tradingview-widget-container:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

[data-theme="dark"] .tradingview-widget-container:focus-within {
  outline-color: #60a5fa;
}

/* Performance optimizations */
.tradingview-widget-container {
  will-change: transform;
  transform: translateZ(0);
}

.tradingview-widget-container__widget {
  contain: layout style paint;
}

/* Prevent unnecessary repaints on hover */
.tradingview-widget-container:hover {
  transform: translateZ(0);
}

/* Print styles */
@media print {
  .tradingview-widget-copyright {
    display: none;
  }
  
  .tradingview-loading-overlay {
    display: none;
  }
} 