'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Mail, ArrowLeft, CheckCircle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import AuthLayout from '@/components/auth/AuthLayout';
import { cn } from '@/lib/utils';

// 忘记密码表单验证模式
const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, '请输入邮箱地址')
    .email('请输入有效的邮箱地址'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [emailSent, setEmailSent] = useState<string>('');

  const form = useForm({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: {
      email: '',
    },
  });

  const handleSubmit = async (data: ForgotPasswordFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      // TODO: 实现实际的忘记密码API调用
      const response = await fetch('http://127.0.0.1:8000/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: data.email,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '发送重置邮件失败');
      }

      setEmailSent(data.email);
      setIsSuccess(true);
      
    } catch (error) {
      console.error('Forgot password error:', error);
      setError(error instanceof Error ? error.message : '发送重置邮件失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  if (isSuccess) {
    return (
      <AuthLayout>
        <SuccessMessage 
          email={emailSent} 
          onResend={() => {
            setIsSuccess(false);
            setEmailSent('');
            form.reset();
          }}
          onBackToLogin={() => router.push('/login')}
        />
      </AuthLayout>
    );
  }

  return (
    <AuthLayout>
      <div className="space-y-6">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-display-lg text-primary">
              重置密码
            </CardTitle>
            <CardDescription className="text-body-md text-secondary">
              输入您的邮箱地址，我们将发送密码重置链接
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
                {/* 全局错误显示 */}
                {error && (
                  <div className="p-3 rounded-md bg-error text-white text-sm">
                    {error}
                  </div>
                )}

                {/* 邮箱输入 */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-body-sm font-medium text-primary">
                        邮箱地址
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-tertiary" />
                          <Input
                            {...field}
                            type="email"
                            placeholder="输入您的邮箱地址"
                            className="pl-10 input-base"
                            disabled={isLoading}
                            autoComplete="email"
                          />
                        </div>
                      </FormControl>
                      <FormMessage className="text-error text-xs" />
                    </FormItem>
                  )}
                />

                {/* 发送重置邮件按钮 */}
                <Button
                  type="submit"
                  className="w-full btn-primary btn-md"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      发送中...
                    </>
                  ) : (
                    '发送重置邮件'
                  )}
                </Button>

                {/* 返回登录链接 */}
                <div className="text-center">
                  <Link 
                    href="/login" 
                    className="inline-flex items-center text-body-sm text-brand-primary hover:text-brand-secondary font-medium underline-offset-4 hover:underline transition-colors"
                  >
                    <ArrowLeft className="mr-1 h-4 w-4" />
                    返回登录
                  </Link>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* 帮助信息 */}
        <div className="bg-info-50 border border-info-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-info-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="text-body-sm font-medium text-info-800 mb-1">
                密码重置说明
              </h3>
              <ul className="text-body-sm text-info-700 space-y-1">
                <li>• 重置邮件将在几分钟内发送到您的邮箱</li>
                <li>• 请检查垃圾邮件文件夹</li>
                <li>• 重置链接有效期为24小时</li>
                <li>• 如果没有收到邮件，请检查邮箱地址是否正确</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
}

// 成功发送邮件的提示组件
interface SuccessMessageProps {
  email: string;
  onResend: () => void;
  onBackToLogin: () => void;
}

function SuccessMessage({ email, onResend, onBackToLogin }: SuccessMessageProps) {
  const [resendCooldown, setResendCooldown] = useState(0);

  React.useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const handleResend = () => {
    setResendCooldown(60); // 60秒冷却时间
    onResend();
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardContent className="p-6 text-center space-y-4">
        <div className="w-16 h-16 mx-auto bg-success-100 rounded-full flex items-center justify-center">
          <CheckCircle className="w-8 h-8 text-success-600" />
        </div>
        
        <div className="space-y-2">
          <h2 className="text-heading-md font-semibold text-primary">邮件已发送</h2>
          <p className="text-body-sm text-secondary">
            我们已向 <span className="font-medium text-primary">{email}</span> 发送了密码重置邮件
          </p>
        </div>

        <div className="bg-info-50 border border-info-200 rounded-lg p-3">
          <p className="text-body-sm text-info-700">
            请检查您的邮箱并点击重置链接。如果没有收到邮件，请检查垃圾邮件文件夹。
          </p>
        </div>

        <div className="space-y-2">
          <Button
            onClick={onBackToLogin}
            className="w-full btn-primary btn-md"
          >
            返回登录
          </Button>
          
          <Button
            onClick={handleResend}
            variant="outline"
            className="w-full"
            disabled={resendCooldown > 0}
          >
            {resendCooldown > 0 ? `重新发送 (${resendCooldown}s)` : '重新发送邮件'}
          </Button>
        </div>

        <div className="text-center">
          <p className="text-caption text-tertiary">
            没有收到邮件？请联系客服支持
          </p>
        </div>
      </CardContent>
    </Card>
  );
} 