'use client';

import React, { useState } from 'react';
import DeepAnalysisProgress from '@/components/DeepAnalysisProgress';

export default function TestDeepAnalysisFix() {
  const [showAnalysis, setShowAnalysis] = useState(false);
  const [taskId, setTaskId] = useState('18d864c0-7ce1-474e-a1e6-55d179e18e85'); // 最新的完成任务ID

  const mockNewsData = {
    id: 52223,
    title: '【草案显示日本将削减超长期国债销发行量】据草案文件显示，日本将在2025/2026财年将预定的国债销售较最初计划削减5000亿日元至171.8万亿日元',
    content: '【草案显示日本将削减超长期国债销发行量】据草案文件显示，日本将在2025/2026财年将预定的国债销售较最初计划削减5000亿日元至171.8万亿日元；将20年期和30年期国债的发行规模分别削减9000亿日元，至11.1万亿日元和8.7万亿日元',
    source: 'sina_global',
    publish_time: '2025-06-19 11:04:29'
  };

  const handleAnalysisComplete = (result: any) => {
    console.log('分析完成回调:', result);
  };

  const handleAnalysisError = (error: string) => {
    console.error('分析错误回调:', error);
    alert('错误: ' + error);
  };

  const handleClose = () => {
    setShowAnalysis(false);
  };

  if (showAnalysis) {
    return (
      <DeepAnalysisProgress
        taskId={taskId}
        newsData={mockNewsData}
        onComplete={handleAnalysisComplete}
        onError={handleAnalysisError}
        onClose={handleClose}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">深度分析显示修复测试</h1>
          
          <div className="space-y-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">测试新闻</h3>
              <p className="text-sm text-gray-700 mb-2">{mockNewsData.title}</p>
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                <span>来源: {mockNewsData.source}</span>
                <span>时间: {mockNewsData.publish_time}</span>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  测试任务ID (默认为最新完成的任务)
                </label>
                <input
                  type="text"
                  value={taskId}
                  onChange={(e) => setTaskId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入要测试的任务ID"
                />
              </div>

              <button
                onClick={() => setShowAnalysis(true)}
                className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                🔍 开始测试深度分析显示
              </button>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">测试说明</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• 点击按钮将加载指定任务ID的深度分析结果</li>
                <li>• 检查分析报告是否正确显示完整内容</li>
                <li>• 验证统计信息（查询数量、来源数量等）是否正确</li>
                <li>• 测试分享功能是否正常工作</li>
                <li>• 确认关闭按钮功能正常</li>
              </ul>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-900 mb-2">预期结果</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• 应该能看到完整的分析报告内容</li>
                <li>• 统计信息显示正确的数值</li>
                <li>• 分享按钮能够复制分析内容</li>
                <li>• 返回主页按钮正常工作</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 