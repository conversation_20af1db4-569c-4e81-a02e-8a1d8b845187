'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AuthLayout from '@/components/auth/AuthLayout';
import RegisterForm from '@/components/auth/RegisterForm';
import { useAuth } from '@/providers/AuthProvider';

interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  fullName?: string;
}

export default function RegisterPage() {
  const router = useRouter();
  const { register, isLoading, error } = useAuth();

  const handleRegister = async (data: RegisterFormData) => {
    try {
      await register({
        username: data.username,
        email: data.email,
        password: data.password,
        fullName: data.fullName,
      });
      
      // 注册成功后会自动登录，重定向到主页
      router.push('/');
      
    } catch (error) {
      console.error('Register error:', error);
      // 错误已经在AuthProvider中处理
    }
  };

  return (
    <AuthLayout>
      <div className="space-y-6">
        <RegisterForm
          onSubmit={handleRegister}
          isLoading={isLoading}
          error={error}
        />
        
        {/* 登录链接 */}
        <div className="text-center">
          <p className="text-body-sm text-secondary">
            已有账户？{' '}
            <Link 
              href="/login" 
              className="text-brand-primary hover:text-brand-secondary font-medium underline-offset-4 hover:underline transition-colors"
            >
              立即登录
            </Link>
          </p>
        </div>

        {/* 注册说明 */}
        <div className="bg-info-50 border border-info-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-info-600 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="text-body-sm font-medium text-info-800 mb-1">
                注册须知
              </h3>
              <ul className="text-body-sm text-info-700 space-y-1">
                <li>• 注册后您将获得完整的AI金融分析功能</li>
                <li>• 您的数据将被安全加密存储</li>
                <li>• 我们不会向第三方分享您的个人信息</li>
                <li>• 如有问题，请联系客服支持</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 快速注册选项 */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-border-primary" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white px-2 text-tertiary">快速注册</span>
          </div>
        </div>

        <div className="space-y-2">
          <button
            type="button"
            onClick={() => {
              // 演示注册 - 自动填充表单
              const event = new CustomEvent('demo-register', {
                detail: {
                  username: 'demo_user',
                  email: '<EMAIL>',
                  password: 'Demo123456!',
                  confirmPassword: 'Demo123456!',
                  fullName: '演示用户',
                }
              });
              window.dispatchEvent(event);
            }}
            className="w-full px-4 py-2 text-sm font-medium text-secondary bg-tertiary hover:bg-secondary border border-border-primary rounded-md transition-colors"
          >
            使用演示数据快速注册
          </button>
          
          <p className="text-center text-xs text-tertiary">
            演示数据仅用于功能展示，请勿用于生产环境
          </p>
        </div>
      </div>
    </AuthLayout>
  );
} 