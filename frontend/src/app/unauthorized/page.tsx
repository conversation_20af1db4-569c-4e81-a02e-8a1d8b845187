'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Shield, ArrowLeft, Home, LogOut } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/providers/AuthProvider';

export default function UnauthorizedPage() {
  const router = useRouter();
  const { logout, user } = useAuth();

  const handleGoBack = () => {
    router.back();
  };

  const handleGoHome = () => {
    router.push('/');
  };

  const handleLogout = async () => {
    await logout();
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background px-4">
      <Card className="w-full max-w-md">
        <CardContent className="p-8 text-center space-y-6">
          {/* 图标 */}
          <div className="w-20 h-20 mx-auto bg-destructive/10 rounded-full flex items-center justify-center">
            <Shield className="w-10 h-10 text-destructive" />
          </div>
          
          {/* 标题和描述 */}
          <div className="space-y-3">
            <h1 className="text-2xl font-bold text-foreground">访问被拒绝</h1>
            <p className="text-muted-foreground">
              抱歉，您没有权限访问此页面。这可能是因为：
            </p>
            <ul className="text-sm text-muted-foreground text-left space-y-1 bg-muted/50 rounded-lg p-4">
              <li>• 您的账户权限不足</li>
              <li>• 页面需要特殊权限才能访问</li>
              <li>• 您的登录会话可能已过期</li>
              <li>• 页面正在维护中</li>
            </ul>
          </div>

          {/* 用户信息 */}
          {user && (
            <div className="bg-muted/30 rounded-lg p-3 text-sm">
              <p className="text-muted-foreground">当前登录用户:</p>
              <p className="font-medium text-foreground">{user.username} ({user.email})</p>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="space-y-3">
            <div className="flex space-x-2">
              <Button
                onClick={handleGoBack}
                variant="outline"
                className="flex-1"
              >
                <ArrowLeft className="mr-2 h-4 w-4" />
                返回上页
              </Button>
              <Button
                onClick={handleGoHome}
                variant="outline"
                className="flex-1"
              >
                <Home className="mr-2 h-4 w-4" />
                回到首页
              </Button>
            </div>
            
            <Button
              onClick={handleLogout}
              variant="destructive"
              className="w-full"
            >
              <LogOut className="mr-2 h-4 w-4" />
              重新登录
            </Button>
          </div>

          {/* 帮助信息 */}
          <div className="text-xs text-muted-foreground border-t pt-4">
            <p>如果您认为这是一个错误，请联系系统管理员</p>
            <p className="mt-1">或发送邮件至: <EMAIL></p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 