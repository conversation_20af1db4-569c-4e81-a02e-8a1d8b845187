'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AuthLayout from '@/components/auth/AuthLayout';
import LoginForm from '@/components/auth/LoginForm';
import { useAuth } from '@/providers/AuthProvider';

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading, error } = useAuth();
  const [isDemoLoading, setIsDemoLoading] = useState(false);
  const searchParams = new URLSearchParams(typeof window !== 'undefined' ? window.location.search : '');
  const returnUrl = searchParams.get('returnUrl') || '/';

  const handleLogin = async (data: LoginFormData) => {
    try {
      await login(data.email, data.password, data.rememberMe);
      
      // 登录成功，重定向到原页面或主页
      router.push(decodeURIComponent(returnUrl));
      
    } catch (error) {
      console.error('Login error:', error);
      // 错误已经在AuthProvider中处理
    }
  };

  const handleDemoLogin = async () => {
    setIsDemoLoading(true);
    try {
      // 首先尝试注册演示账户（如果不存在）
      try {
        const registerResponse = await fetch('http://127.0.0.1:8000/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            username: 'demo_user',
            email: '<EMAIL>',
            password: 'demo123456',
            full_name: '演示用户'
          }),
        });
        
        if (registerResponse.ok) {
          console.log('演示账户创建成功');
        } else {
          // 账户可能已存在，这是正常的
          console.log('演示账户可能已存在');
        }
      } catch (registerError) {
        console.log('演示账户注册失败，可能已存在:', registerError);
      }

      // 使用演示账户登录
      await login('<EMAIL>', 'demo123456', false);
      
      // 登录成功，重定向到原页面或主页
      router.push(decodeURIComponent(returnUrl));
      
    } catch (error) {
      console.error('Demo login error:', error);
    } finally {
      setIsDemoLoading(false);
    }
  };

  return (
    <AuthLayout>
      <div className="space-y-6">
        <LoginForm
          onSubmit={handleLogin}
          isLoading={isLoading}
          error={error}
        />
        
        {/* 注册链接 */}
        <div className="text-center">
          <p className="text-sm text-gray-600">
            还没有账户？{' '}
            <Link 
              href="/register" 
              className="text-blue-600 hover:text-blue-700 font-medium underline-offset-4 hover:underline transition-colors"
            >
              立即注册
            </Link>
          </p>
        </div>

        {/* 分隔线 */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-300" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white px-2 text-gray-500">或者</span>
          </div>
        </div>

        {/* 演示登录按钮 */}
        <div className="space-y-3">
          <button
            type="button"
            onClick={handleDemoLogin}
            disabled={isLoading || isDemoLoading}
            className="w-full px-4 py-3 text-sm font-medium text-gray-700 bg-gray-50 hover:bg-gray-100 border border-gray-300 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isDemoLoading ? (
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-700 mr-2"></div>
                创建演示账户并登录...
              </div>
            ) : (
              '使用演示账户登录'
            )}
          </button>
          
          <div className="text-center">
            <p className="text-xs text-gray-500">
              演示账户仅用于功能展示，数据不会保存
            </p>
            <p className="text-xs text-gray-400 mt-1">
              账户：<EMAIL> | 密码：demo123456
            </p>
          </div>
        </div>
      </div>
    </AuthLayout>
  );
} 