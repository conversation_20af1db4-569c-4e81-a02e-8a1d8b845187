'use client';

import React, { useState } from 'react';
import DeepAnalysisProgress from '../../components/DeepAnalysisProgress';

const TestDeepAnalysisPage: React.FC = () => {
  const [showAnalysis, setShowAnalysis] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [testType, setTestType] = useState<'valid' | 'invalid' | 'manual'>('valid');

  const testNewsData = {
    valid: {
      id: 9718,
      title: "德邦證券研究所所長程強：資本市場將在支持新質生產力發展上發揮更大作用",
      content: "德邦證券研究所所長程強表示，資本市場將在支持新質生產力發展上發揮更大作用。",
      source: "test_source",
      publish_time: new Date().toISOString()
    },
    invalid: {
      id: 999999,
      title: "无效新闻测试",
      content: "这是一个用于测试错误处理的无效新闻ID",
      source: "test_source",
      publish_time: new Date().toISOString()
    },
    manual: {
      id: 0,
      title: "特朗普宣布对华新贸易政策",
      content: "美国总统特朗普今日宣布了新的对华贸易政策，预计将对全球股市产生重大影响。该政策涉及多个关键行业，包括科技、制造业和能源等。",
      source: "manual_test",
      publish_time: new Date().toISOString()
    }
  };

  const handleStartAnalysis = () => {
    setResult(null);
    setError(null);
    setShowAnalysis(true);
  };

  const handleComplete = (analysisResult: any) => {
    console.log('分析完成:', analysisResult);
    setResult(analysisResult);
    // 不自动关闭分析界面，让用户查看结果
    // setShowAnalysis(false);
  };

  const handleError = (errorMessage: string) => {
    console.error('分析错误:', errorMessage);
    setError(errorMessage);
  };

  const handleClose = () => {
    setShowAnalysis(false);
  };

  if (showAnalysis) {
    return (
      <DeepAnalysisProgress
        newsData={testNewsData[testType]}
        onComplete={handleComplete}
        onError={handleError}
        onClose={handleClose}
        useDirectStream={true}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            🔍 深度分析功能测试
          </h1>

          <div className="space-y-6">
            {/* 测试类型选择 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                选择测试类型：
              </label>
              <div className="space-y-3">
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="testType"
                    value="valid"
                    checked={testType === 'valid'}
                    onChange={(e) => setTestType(e.target.value as 'valid' | 'invalid' | 'manual')}
                    className="form-radio h-4 w-4 text-blue-600"
                  />
                  <span className="text-sm text-gray-700">
                    <span className="font-medium">有效新闻ID</span> - 测试正常流程
                  </span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="testType"
                    value="invalid"
                    checked={testType === 'invalid'}
                    onChange={(e) => setTestType(e.target.value as 'valid' | 'invalid' | 'manual')}
                    className="form-radio h-4 w-4 text-blue-600"
                  />
                  <span className="text-sm text-gray-700">
                    <span className="font-medium">无效新闻ID</span> - 测试错误处理
                  </span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="testType"
                    value="manual"
                    checked={testType === 'manual'}
                    onChange={(e) => setTestType(e.target.value as 'valid' | 'invalid' | 'manual')}
                    className="form-radio h-4 w-4 text-blue-600"
                  />
                  <span className="text-sm text-gray-700">
                    <span className="font-medium">手动新闻数据</span> - 测试无ID流程
                  </span>
                </label>
              </div>
            </div>

            {/* 当前测试数据预览 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-700 mb-2">当前测试数据:</h3>
              <div className="text-xs text-gray-600 space-y-1">
                <div><span className="font-medium">ID:</span> {testNewsData[testType].id}</div>
                <div><span className="font-medium">标题:</span> {testNewsData[testType].title}</div>
                <div><span className="font-medium">内容:</span> {testNewsData[testType].content}</div>
              </div>
            </div>

            {/* 开始测试按钮 */}
            <button
              onClick={handleStartAnalysis}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <span>开始深度分析测试</span>
            </button>

            {/* 错误显示 */}
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <h3 className="text-sm font-medium text-red-800">分析失败</h3>
                </div>
                <p className="mt-2 text-sm text-red-700">{error}</p>
              </div>
            )}

            {/* 结果显示 */}
            {result && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <h3 className="text-sm font-medium text-green-800">分析完成</h3>
                </div>
                <pre className="text-xs text-green-700 bg-green-100 rounded p-3 overflow-auto max-h-64">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            )}

            {/* 功能说明 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-blue-800 mb-2">测试功能说明</h3>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>• <span className="font-medium">有效新闻ID:</span> 测试正常的深度分析流程，验证流式响应和结果展示</li>
                <li>• <span className="font-medium">无效新闻ID:</span> 测试404错误处理，验证友好错误提示</li>
                <li>• <span className="font-medium">手动新闻数据:</span> 测试不依赖数据库的分析流程</li>
                <li>• 所有测试都使用直接流式模式，实时显示分析进度</li>
                <li>• 包含网络错误、解析错误等多种错误场景的处理</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestDeepAnalysisPage; 