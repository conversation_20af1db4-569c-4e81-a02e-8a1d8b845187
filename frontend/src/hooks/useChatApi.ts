import { useState, useCallback, useRef, useEffect } from 'react';
import { Message, StreamData } from '@/types';
import { streamChat, ChatRequest, handleApiError } from '@/utils/api';
import { useAuth } from '@/providers/AuthProvider';
import { messageStorage } from '@/utils/messageStorage';

// Message status types
export type MessageStatus = 'pending' | 'sending' | 'sent' | 'failed' | 'streaming';

// Chat state interface
export interface ChatState {
  messages: Message[];
  isLoading: boolean;
  error: string | null;
  isConnected: boolean;
  currentStreamingMessageId: string | null;
}

// Send message options
export interface SendMessageOptions {
  max_plan_iterations?: number;
  max_step_num?: number;
  enable_background_investigation?: boolean;
  debug?: boolean;
}

// Default send options
const DEFAULT_SEND_OPTIONS: SendMessageOptions = {
  max_plan_iterations: 1,
  max_step_num: 3,
  enable_background_investigation: true,
  debug: false,
};

// Generate unique message ID
const generateMessageId = () => `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

// Custom hook for chat API interactions
export const useChatApi = () => {
  // Get authentication context
  const { user } = useAuth();
  
  // Chat state
  const [chatState, setChatState] = useState<ChatState>({
    messages: [],
    isLoading: false,
    error: null,
    isConnected: true,
    currentStreamingMessageId: null,
  });

  // Refs for managing streaming state
  const abortControllerRef = useRef<AbortController | null>(null);
  const isStreamingRef = useRef<boolean>(false);

  // Update message storage user ID when user changes
  useEffect(() => {
    if (user?.id) {
      messageStorage.setCurrentUserId(user.id.toString());
      console.log(`聊天记录已切换到用户: ${user.username} (ID: ${user.id})`);
    } else {
      messageStorage.setCurrentUserId(null);
      console.log('聊天记录已切换到匿名用户');
    }
  }, [user]);

  // Clear chat state when user changes
  useEffect(() => {
    setChatState({
      messages: [],
      isLoading: false,
      error: null,
      isConnected: true,
      currentStreamingMessageId: null,
    });
    console.log('聊天状态已重置');
  }, [user?.id]);

  // Add user message to state
  const addUserMessage = useCallback((content: string): string => {
    const messageId = generateMessageId();
    const userMessage: Message = {
      id: messageId,
      type: 'user',
      content: content.trim(),
      timestamp: Date.now(),
      isStreaming: false,
    };

    setChatState(prev => ({
      ...prev,
      messages: [...prev.messages, userMessage],
      error: null,
    }));

    return messageId;
  }, []);

  // Add assistant message (for streaming responses)
  const addAssistantMessage = useCallback((id: string, content: string = '', agent?: string): void => {
    setChatState(prev => {
      const existingMessageIndex = prev.messages.findIndex(msg => msg.id === id);
      
      if (existingMessageIndex >= 0) {
        // Update existing message
        const updatedMessages = [...prev.messages];
        updatedMessages[existingMessageIndex] = {
          ...updatedMessages[existingMessageIndex],
          content,
          agent,
          isStreaming: true,
        };
        return {
          ...prev,
          messages: updatedMessages,
        };
      } else {
        // Create new assistant message
        const assistantMessage: Message = {
          id,
          type: 'assistant',
          content,
          agent,
          timestamp: Date.now(),
          isStreaming: true,
        };
        return {
          ...prev,
          messages: [...prev.messages, assistantMessage],
        };
      }
    });
  }, []);

  // Update message content (for streaming)
  const updateMessageContent = useCallback((id: string, content: string, isComplete: boolean = false): void => {
    setChatState(prev => {
      const updatedMessages = prev.messages.map(msg => 
        msg.id === id 
          ? { ...msg, content, isStreaming: !isComplete }
          : msg
      );
      return {
        ...prev,
        messages: updatedMessages,
        currentStreamingMessageId: isComplete ? null : id,
      };
    });
  }, []);

  // Add chart message
  const addChartMessage = useCallback((chartData: any, chartConfig: any): void => {
    const chartMessage: Message = {
      id: generateMessageId(),
      type: 'chart',
      content: 'Interactive Chart',
      timestamp: Date.now(),
      isStreaming: false,
      chartData,
      chartConfig,
    };

    setChatState(prev => ({
      ...prev,
      messages: [...prev.messages, chartMessage],
    }));
  }, []);

  // Handle streaming data
  const handleStreamData = useCallback((data: StreamData, assistantMessageId: string): void => {
    console.log('📡 Received stream data:', data);

    switch (data.type) {
      case 'message':
        if (data.content) {
          updateMessageContent(assistantMessageId, data.content);
        }
        break;

      case 'final_report':
        if (data.content) {
          updateMessageContent(assistantMessageId, data.content, true);
        }
        break;

      case 'chart_data':
        if (data.chart_data) {
          addChartMessage(data.chart_data, data.chart_data);
        }
        break;

      case 'error':
        setChatState(prev => ({
          ...prev,
          error: data.content || data.message || 'Unknown error occurred',
          isLoading: false,
          currentStreamingMessageId: null,
        }));
        break;

      case 'end':
      case 'workflow_complete':
      case 'done':
        setChatState(prev => ({
          ...prev,
          isLoading: false,
          currentStreamingMessageId: null,
        }));
        // Mark the current message as complete
        updateMessageContent(assistantMessageId, '', true);
        break;

      default:
        console.log('[STREAM] Unhandled stream type:', data.type);
    }
  }, [updateMessageContent, addChartMessage]);

  // Handle streaming errors
  const handleStreamError = useCallback((error: string): void => {
    console.error('❌ Stream error:', error);
    setChatState(prev => ({
      ...prev,
      error,
      isLoading: false,
      isConnected: false,
      currentStreamingMessageId: null,
    }));
  }, []);

  // Send message function
  const sendMessage = useCallback(async (
    content: string, 
    options: SendMessageOptions = {}
  ): Promise<void> => {
    if (!content.trim()) {
      console.warn('⚠️ Attempted to send empty message');
      return;
    }

    if (isStreamingRef.current) {
      console.warn('⚠️ Another message is currently being processed');
      return;
    }

    try {
      // Cancel any previous request
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create new abort controller
      abortControllerRef.current = new AbortController();
      isStreamingRef.current = true;

      // Add user message
      const userMessageId = addUserMessage(content);
      
      // Set loading state
      setChatState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
        isConnected: true,
      }));

      // Prepare assistant message for streaming response
      const assistantMessageId = generateMessageId();
      addAssistantMessage(assistantMessageId, '');

      setChatState(prev => ({
        ...prev,
        currentStreamingMessageId: assistantMessageId,
      }));

      // Prepare chat request
      const chatRequest: ChatRequest = {
        message: content,
        ...DEFAULT_SEND_OPTIONS,
        ...options,
      };

      console.log('📤 Sending chat request:', chatRequest);

      // Start streaming
      await streamChat(
        chatRequest,
        (data: StreamData) => handleStreamData(data, assistantMessageId),
        handleStreamError
      );

    } catch (error: any) {
      console.error('💥 Send message error:', error);
      const errorMessage = handleApiError(error);
      handleStreamError(errorMessage);
    } finally {
      isStreamingRef.current = false;
      abortControllerRef.current = null;
    }
  }, [addUserMessage, addAssistantMessage, handleStreamData, handleStreamError]);

  // Retry last message
  const retryLastMessage = useCallback((): void => {
    const lastUserMessage = [...chatState.messages]
      .reverse()
      .find(msg => msg.type === 'user');
    
    if (lastUserMessage) {
      // Remove any error messages and retry
      setChatState(prev => ({
        ...prev,
        messages: prev.messages.filter(msg => 
          !(msg.type === 'assistant' && msg.content.includes('error'))
        ),
        error: null,
      }));
      
      sendMessage(lastUserMessage.content);
    }
  }, [chatState.messages, sendMessage]);

  // Cancel current streaming
  const cancelStream = useCallback((): void => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    isStreamingRef.current = false;
    
    setChatState(prev => ({
      ...prev,
      isLoading: false,
      currentStreamingMessageId: null,
    }));
  }, []);

  // Clear all messages
  const clearMessages = useCallback((): void => {
    cancelStream();
    setChatState(prev => ({
      ...prev,
      messages: [],
      error: null,
    }));
  }, [cancelStream]);

  // Update connection status
  const updateConnectionStatus = useCallback((isConnected: boolean): void => {
    setChatState(prev => ({
      ...prev,
      isConnected,
    }));
  }, []);

  // Get the last user message
  const getLastUserMessage = useCallback((): Message | null => {
    return [...chatState.messages]
      .reverse()
      .find(msg => msg.type === 'user') || null;
  }, [chatState.messages]);

  // Check if there are any messages
  const hasMessages = chatState.messages.length > 0;

  // Check if currently streaming
  const isStreaming = isStreamingRef.current && chatState.currentStreamingMessageId !== null;

  return {
    // State
    messages: chatState.messages,
    isLoading: chatState.isLoading,
    error: chatState.error,
    isConnected: chatState.isConnected,
    isStreaming,
    hasMessages,
    currentStreamingMessageId: chatState.currentStreamingMessageId,

    // Actions
    sendMessage,
    retryLastMessage,
    cancelStream,
    clearMessages,
    updateConnectionStatus,
    getLastUserMessage,

    // Advanced actions for future use
    addUserMessage,
    addAssistantMessage,
    updateMessageContent,
    addChartMessage,
  };
}; 