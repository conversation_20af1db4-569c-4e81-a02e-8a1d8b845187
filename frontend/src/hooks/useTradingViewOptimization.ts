import { useRef, useMemo, useCallback, useEffect, useState } from 'react';

interface TradingViewConfig {
  autosize: boolean;
  symbol: string;
  interval: string;
  timezone: string;
  theme: string;
  style: string;
  locale: string;
  allow_symbol_change: boolean;
  support_host: string;
}

interface UseTradingViewOptimizationProps {
  symbol: string;
  theme: 'light' | 'dark';
  locale: string;
  debounceMs?: number;
}

export function useTradingViewOptimization({
  symbol,
  theme,
  locale,
  debounceMs = 300
}: UseTradingViewOptimizationProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [loadedSymbol, setLoadedSymbol] = useState<string>('');
  const scriptLoadingRef = useRef<boolean>(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Memoized TradingView configuration
  const tradingViewConfig = useMemo<TradingViewConfig>(() => ({
    autosize: true,
    symbol: symbol,
    interval: "D",
    timezone: "Etc/UTC",
    theme: theme,
    style: "1",
    locale: locale,
    allow_symbol_change: true,
    support_host: "https://www.tradingview.com"
  }), [symbol, theme, locale]);

  // Debounced symbol change handler
  const debouncedSymbolChange = useCallback((newSymbol: string) => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      if (newSymbol !== loadedSymbol && !scriptLoadingRef.current) {
        setLoadedSymbol(newSymbol);
      }
    }, debounceMs);
  }, [loadedSymbol, debounceMs]);

  // Load TradingView script
  const loadTradingViewScript = useCallback(() => {
    if (!containerRef.current || scriptLoadingRef.current) return;

    scriptLoadingRef.current = true;
    setIsLoading(true);

    // Clear previous content
    containerRef.current.innerHTML = '';

    const script = document.createElement("script");
    script.src = "https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js";
    script.type = "text/javascript";
    script.async = true;
    script.innerHTML = JSON.stringify(tradingViewConfig);

    script.onload = () => {
      scriptLoadingRef.current = false;
      setIsLoading(false);
      setLoadedSymbol(symbol);
    };

    script.onerror = () => {
      scriptLoadingRef.current = false;
      setIsLoading(false);
      console.error('Failed to load TradingView script');
    };

    containerRef.current.appendChild(script);
  }, [tradingViewConfig, symbol]);

  // Effect to handle symbol changes
  useEffect(() => {
    debouncedSymbolChange(symbol);
  }, [symbol, debouncedSymbolChange]);

  // Effect to load script when symbol changes
  useEffect(() => {
    if (loadedSymbol && loadedSymbol !== symbol) {
      loadTradingViewScript();
    } else if (!loadedSymbol) {
      loadTradingViewScript();
    }
  }, [loadedSymbol, symbol, loadTradingViewScript]);

  // Cleanup function
  const cleanup = useCallback(() => {
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }
    if (containerRef.current) {
      containerRef.current.innerHTML = '';
    }
    scriptLoadingRef.current = false;
  }, []);

  return {
    containerRef,
    isLoading,
    loadedSymbol,
    cleanup,
    tradingViewConfig
  };
} 