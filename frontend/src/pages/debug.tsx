'use client';

import React, { useState, useEffect } from 'react';
import { streamChat } from '@/utils/api';

const DebugPage: React.FC = () => {
  const [message, setMessage] = useState('你好');
  const [response, setResponse] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [apiStatus, setApiStatus] = useState<string>('未检查');
  const [networkTest, setNetworkTest] = useState<string>('未测试');

  // 测试API连接
  const testApiConnection = async () => {
    try {
      const response = await fetch('http://localhost:8000/health');
      if (response.ok) {
        const data = await response.json();
        setApiStatus(`✅ 连接成功: ${data.status}`);
      } else {
        setApiStatus(`❌ 连接失败: ${response.status}`);
      }
    } catch (error) {
      setApiStatus(`❌ 网络错误: ${error}`);
    }
  };

  // 测试网络连接
  const testNetworkConnection = async () => {
    try {
      // 测试简单的网络请求
      const response = await fetch('http://localhost:8000/debug/frontend-health');
      if (response.ok) {
        const data = await response.json();
        setNetworkTest(`✅ 网络正常: ${data.service}`);
      } else {
        setNetworkTest(`❌ 网络异常: ${response.status}`);
      }
    } catch (error) {
      setNetworkTest(`❌ 网络错误: ${error}`);
    }
  };

  // 测试流式聊天
  const testStreamChat = async () => {
    setIsLoading(true);
    setResponse([]);
    
    try {
      console.log('🚀 开始测试流式聊天...');
      await streamChat(
        { message },
        (data) => {
          console.log('📡 收到数据:', data);
          setResponse(prev => [...prev, `📡 ${JSON.stringify(data, null, 2)}`]);
        },
        (error) => {
          console.error('❌ 收到错误:', error);
          setResponse(prev => [...prev, `❌ 错误: ${error}`]);
        }
      );
      console.log('✅ 流式聊天测试完成');
    } catch (error) {
      console.error('💥 异常:', error);
      setResponse(prev => [...prev, `💥 异常: ${error}`]);
    } finally {
      setIsLoading(false);
    }
  };

  // 页面加载时自动测试连接
  useEffect(() => {
    testApiConnection();
    testNetworkConnection();
  }, []);

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>🔧 AI投资助手调试页面</h1>
      
      {/* API状态检查 */}
      <div style={{ marginBottom: '30px', padding: '15px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h2>📡 API连接状态</h2>
        <p><strong>后端健康检查:</strong> {apiStatus}</p>
        <p><strong>网络连接测试:</strong> {networkTest}</p>
        <button onClick={testApiConnection} style={{ marginRight: '10px', padding: '5px 10px' }}>
          重新检查API
        </button>
        <button onClick={testNetworkConnection} style={{ padding: '5px 10px' }}>
          重新测试网络
        </button>
      </div>

      {/* 聊天测试 */}
      <div style={{ marginBottom: '30px', padding: '15px', border: '1px solid #ccc', borderRadius: '5px' }}>
        <h2>💬 聊天功能测试</h2>
        <div style={{ marginBottom: '10px' }}>
          <input
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            style={{ width: '300px', padding: '5px', marginRight: '10px' }}
            placeholder="输入测试消息"
          />
          <button 
            onClick={testStreamChat} 
            disabled={isLoading}
            style={{ padding: '5px 15px' }}
          >
            {isLoading ? '测试中...' : '测试流式聊天'}
          </button>
        </div>
        
        {/* 响应显示 */}
        <div style={{ 
          height: '400px', 
          overflow: 'auto', 
          border: '1px solid #ddd', 
          padding: '10px', 
          backgroundColor: '#f5f5f5',
          whiteSpace: 'pre-wrap'
        }}>
          {response.length === 0 ? (
            <p style={{ color: '#666' }}>等待测试结果...</p>
          ) : (
            response.map((item, index) => (
              <div key={index} style={{ marginBottom: '10px', borderBottom: '1px solid #eee', paddingBottom: '5px' }}>
                {item}
              </div>
            ))
          )}
        </div>
      </div>

      {/* 浏览器控制台提示 */}
      <div style={{ padding: '15px', border: '1px solid #ffa500', borderRadius: '5px', backgroundColor: '#fff3cd' }}>
        <h3>🔍 调试提示</h3>
        <p>1. 打开浏览器开发者工具 (F12)</p>
        <p>2. 查看 Console 标签页中的日志信息</p>
        <p>3. 查看 Network 标签页中的网络请求</p>
        <p>4. 如果有错误，请复制错误信息进行分析</p>
        <p>5. 检查是否有CORS错误或网络连接问题</p>
      </div>

      {/* 快速导航 */}
      <div style={{ marginTop: '20px', padding: '15px', border: '1px solid #007bff', borderRadius: '5px', backgroundColor: '#e7f3ff' }}>
        <h3>🚀 快速导航</h3>
        <p>
          <a href="http://localhost:3000" target="_blank" rel="noopener noreferrer" style={{ marginRight: '15px' }}>
            前端主页
          </a>
          <a href="http://localhost:8000/docs" target="_blank" rel="noopener noreferrer" style={{ marginRight: '15px' }}>
            后端API文档
          </a>
          <a href="http://localhost:8000/health" target="_blank" rel="noopener noreferrer">
            后端健康检查
          </a>
        </p>
      </div>
    </div>
  );
};

export default DebugPage;
