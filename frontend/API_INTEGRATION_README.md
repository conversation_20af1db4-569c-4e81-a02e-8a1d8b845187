# Frontend-Backend API Integration

This document describes the complete API integration implementation between the Next.js frontend and FastAPI backend.

## 🏗️ Architecture Overview

The integration follows a clean architecture pattern with:
- **API Layer**: Centralized HTTP client with error handling
- **Hook Layer**: Custom React hooks for state management
- **Component Layer**: UI components with loading states and error handling
- **Type Safety**: Full TypeScript integration

## 📁 File Structure

```
frontend/src/
├── utils/
│   └── api.ts                 # Centralized API client and functions
├── hooks/
│   └── useApi.ts             # Custom React hooks for API integration
├── components/
│   ├── LoadingSpinner.tsx    # Reusable loading component
│   └── ErrorDisplay.tsx      # Error handling component
├── types/
│   └── index.ts              # TypeScript type definitions
└── app/
    └── page.tsx              # Main page with integrated API calls
```

## 🔧 API Client Configuration

### Base Configuration (`src/utils/api.ts`)

- **Base URL**: `http://localhost:8000` (configurable via environment)
- **Timeout**: 30 seconds
- **CORS**: Pre-configured for development
- **Error Handling**: Centralized error processing
- **Type Safety**: Full TypeScript support

### Available API Functions

#### Health Check
```typescript
healthCheck(): Promise<any>
```

#### Chat/AI Functions
```typescript
streamChat(request: ChatRequest, onMessage: Function, onError: Function): Promise<void>
```

#### Stock Data Functions
```typescript
queryStockData(request: StockDataRequest): Promise<StockQueryResult>
getTechnicalIndicators(request: TechnicalIndicatorRequest): Promise<TechnicalIndicatorResult>
searchStocks(query: string): Promise<any>
```

#### Factor Management Functions
```typescript
getFactors(): Promise<any>
calculateFactors(request: FactorRequest): Promise<any>
calculateFactorsSmart(request: FactorRequest): Promise<any>
getFactorTimeseries(request: FactorRequest): Promise<any>
generateAIFactor(request: AIFactorRequest): Promise<any>
testCustomFactor(request: CustomFactorRequest): Promise<any>
```

#### Divergence Scanner Functions
```typescript
scanMarketDivergence(request: DivergenceScanRequest): Promise<any>
getRecentDivergences(market: string, hours?: number): Promise<any>
getDivergenceHistory(market: string, limit?: number): Promise<any>
getDivergenceChart(symbol: string, market: string): Promise<any>
getSupportedMarkets(): Promise<any>
```

## 🎣 Custom React Hooks

### `useHealthCheck()`
Monitors backend connectivity status.

```typescript
const { isHealthy, loading, error, checkHealth } = useHealthCheck();
```

### `useStockData()`
Manages stock data queries and technical indicators.

```typescript
const { 
  stockData, 
  technicalData, 
  loading, 
  error, 
  queryStock, 
  queryTechnicalIndicators 
} = useStockData();
```

### `useFactors()`
Handles factor management operations.

```typescript
const { 
  factors, 
  factorData, 
  loading, 
  error, 
  loadFactors, 
  calculateFactorData 
} = useFactors();
```

### `useDivergenceScanner()`
Manages market divergence scanning.

```typescript
const { 
  scanResults, 
  recentDivergences, 
  loading, 
  error, 
  scanMarket 
} = useDivergenceScanner();
```

### `useAutoRefresh()`
Enables automatic data refreshing.

```typescript
const { lastRefreshTime } = useAutoRefresh(refreshFunction, interval, enabled);
```

## 🎨 UI Components

### LoadingSpinner
Reusable loading indicator with multiple sizes and colors.

```typescript
<LoadingSpinner size="md" color="primary" text="Loading..." />
```

### ErrorDisplay
Comprehensive error display with retry functionality.

```typescript
<ErrorDisplay 
  error={error} 
  severity="error" 
  onRetry={retryFunction}
  retryText="Retry"
/>
```

## 🔄 Data Flow

1. **Component Mount**: Health check and initial data loading
2. **User Interaction**: API calls triggered by user actions
3. **Loading States**: UI shows loading indicators
4. **Data Reception**: State updates and UI re-renders
5. **Error Handling**: Error display with retry options
6. **Auto-refresh**: Background data updates when enabled

## 📊 Main Page Features

### Health Status Indicator
- Real-time backend connectivity status
- Visual indicator (green/red/yellow dot)
- Manual refresh capability

### Quick Data Preview
- Live preview of fetched data
- Stock information, technical indicators, factors, divergences
- Real-time loading states

### Interactive Controls
- Quick action buttons for common operations
- Auto-refresh toggle
- Error handling with retry buttons

### Module Cards Enhancement
- Status indicators showing data availability
- Loading states during operations
- Error indicators with visual feedback

## 🚀 Getting Started

### 1. Environment Setup
Create a `.env.local` file (or configure in your deployment):
```env
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_TUSHARE_TOKEN=your_token_here
```

### 2. Install Dependencies
Navigate to the `frontend/` directory and run:
```bash
npm install
# or yarn install
```

### 3. Run the Frontend
From the `frontend/` directory, run:
```bash
npm run dev
# or yarn dev
```

### 4. Backend Requirements
Ensure the FastAPI backend is running on port 8000 with:
- CORS middleware configured
- All required endpoints available
- Proper error handling

### 5. Usage Examples

#### Basic Stock Query
```typescript
const { queryStock } = useStockData();

const handleQuery = async () => {
  try {
    await queryStock({
      symbol: 'AAPL',
      period: '1y',
      tushare_token: ''
    });
  } catch (error) {
    console.error('Query failed:', error);
  }
};
```

#### Factor Calculation
```typescript
const { calculateFactorData } = useFactors();

const handleCalculation = async () => {
  try {
    await calculateFactorData({
      symbol: 'AAPL',
      tushare_token: ''
    });
  } catch (error) {
    console.error('Calculation failed:', error);
  }
};
```

## 🛠️ Error Handling

### Levels of Error Handling

1. **API Level**: HTTP errors, network issues, timeouts
2. **Hook Level**: State management, loading states
3. **Component Level**: User feedback, retry mechanisms
4. **Global Level**: Health monitoring, connectivity status

### Error Types

- **Network Errors**: Connection issues, timeouts
- **API Errors**: 4xx/5xx HTTP responses
- **Validation Errors**: Invalid request data
- **Authentication Errors**: Token issues

## 🔧 Configuration Options

### API Client Customization
- Base URL configuration
- Timeout adjustments
- Header customization
- Interceptor configuration

### Hook Customization
- Default values
- Error handling strategies
- Loading state management
- Data transformation

## 📈 Performance Optimizations

- **Request Deduplication**: Prevents duplicate API calls
- **Loading State Management**: Efficient UI updates
- **Error Boundary Integration**: Graceful error handling
- **Auto-refresh Optimization**: Configurable intervals
- **Memory Management**: Proper cleanup in hooks

## 🧪 Testing Considerations

### API Testing
- Mock API responses
- Error scenario testing
- Network condition simulation

### Hook Testing
- State management validation
- Effect cleanup verification
- Error handling testing

### Component Testing
- Loading state verification
- Error display testing
- User interaction testing

## 🔒 Security Features

- **Input Validation**: All user inputs are validated
- **Error Sanitization**: Sensitive information filtered
- **CORS Configuration**: Proper origin restrictions
- **Token Management**: Secure token handling

## 📚 Additional Resources

- [FastAPI Backend Documentation](../backend/README.md)
- [React Hooks Best Practices](https://react.dev/reference/react)
- [TypeScript Integration Guide](https://www.typescriptlang.org/docs/)
- [Error Handling Patterns](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API/Using_Fetch)

---

This integration provides a robust, type-safe, and user-friendly connection between the frontend and backend systems, with comprehensive error handling, loading states, and real-time data capabilities. 