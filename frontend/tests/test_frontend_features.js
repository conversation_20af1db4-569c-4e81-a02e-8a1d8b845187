/**
 * 前端功能测试脚本
 * 测试智能财经资讯功能的前端交互
 */

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:8000',
  timeout: 10000,
  retryCount: 3
};

class FrontendTester {
  constructor() {
    this.testResults = [];
    this.passedTests = 0;
    this.failedTests = 0;
  }

  logTestResult(testName, success, message = '') {
    const status = success ? '✅ PASS' : '❌ FAIL';
    const result = {
      testName,
      success,
      message,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    if (success) {
      this.passedTests++;
    } else {
      this.failedTests++;
    }
    
    console.log(`${status} ${testName}: ${message}`);
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${TEST_CONFIG.baseUrl}${endpoint}`;
    const defaultOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      ...options
    };

    try {
      const response = await fetch(url, defaultOptions);
      const data = await response.json();
      return { success: response.ok, data, status: response.status };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  async testApiEndpoints() {
    console.log('🔗 测试API端点...');

    // 测试获取最新新闻
    try {
      const result = await this.makeRequest('/financial-news/latest?limit=5');
      if (result.success && result.data.success) {
        this.logTestResult(
          'API - 获取最新新闻',
          true,
          `成功获取 ${result.data.news?.length || 0} 条新闻`
        );
      } else {
        this.logTestResult(
          'API - 获取最新新闻',
          false,
          result.error || result.data?.error || '请求失败'
        );
      }
    } catch (error) {
      this.logTestResult('API - 获取最新新闻', false, `请求异常: ${error.message}`);
    }

    // 测试新闻统计
    try {
      const result = await this.makeRequest('/financial-news/statistics');
      if (result.success && result.data.success) {
        const stats = result.data.statistics;
        this.logTestResult(
          'API - 新闻统计',
          true,
          `总新闻数: ${stats.total_news || 0}, 数据源: ${stats.total_sources || 0}`
        );
      } else {
        this.logTestResult(
          'API - 新闻统计',
          false,
          result.error || result.data?.error || '请求失败'
        );
      }
    } catch (error) {
      this.logTestResult('API - 新闻统计', false, `请求异常: ${error.message}`);
    }

    // 测试缓存状态
    try {
      const result = await this.makeRequest('/cache/stats');
      if (result.success && result.data.success) {
        const cacheStats = result.data.cache_stats;
        this.logTestResult(
          'API - 缓存状态',
          true,
          `内存缓存: ${cacheStats.memory_cache?.items || 0} 项, 命中率: ${cacheStats.performance?.hit_rate_percent || 0}%`
        );
      } else {
        this.logTestResult(
          'API - 缓存状态',
          false,
          result.error || result.data?.error || '请求失败'
        );
      }
    } catch (error) {
      this.logTestResult('API - 缓存状态', false, `请求异常: ${error.message}`);
    }

    // 测试Supabase同步状态
    try {
      const result = await this.makeRequest('/supabase-sync/status');
      if (result.success && result.data.success) {
        const syncStatus = result.data.sync_status;
        this.logTestResult(
          'API - Supabase同步状态',
          true,
          `启用: ${syncStatus.enabled}, 运行中: ${syncStatus.running}`
        );
      } else {
        this.logTestResult(
          'API - Supabase同步状态',
          false,
          result.error || result.data?.error || '请求失败'
        );
      }
    } catch (error) {
      this.logTestResult('API - Supabase同步状态', false, `请求异常: ${error.message}`);
    }
  }

  async testAIAnalysis() {
    console.log('🤖 测试AI分析功能...');

    // 首先获取一条新闻用于测试
    try {
      const newsResult = await this.makeRequest('/financial-news/latest?limit=1');
      if (!newsResult.success || !newsResult.data.success || !newsResult.data.news?.length) {
        this.logTestResult('AI分析 - 获取测试新闻', false, '无法获取测试新闻');
        return;
      }

      const testNews = newsResult.data.news[0];
      
      // 测试GLM分析
      try {
        const analysisResult = await this.makeRequest('/news/impact-analysis', {
          method: 'POST',
          body: JSON.stringify({
            news_id: testNews.id,
            news_title: testNews.title,
            news_content: testNews.content,
            model: 'glm'
          })
        });

        if (analysisResult.success && analysisResult.data.success) {
          const analysis = analysisResult.data.analysis;
          this.logTestResult(
            'AI分析 - GLM模型',
            true,
            `分析成功, 整体影响: ${analysis.overall_impact?.level || '未知'}`
          );
        } else {
          this.logTestResult(
            'AI分析 - GLM模型',
            false,
            analysisResult.error || analysisResult.data?.error || 'GLM分析失败'
          );
        }
      } catch (error) {
        this.logTestResult('AI分析 - GLM模型', false, `GLM分析异常: ${error.message}`);
      }

      // 测试Gemini分析
      try {
        const analysisResult = await this.makeRequest('/news/impact-analysis', {
          method: 'POST',
          body: JSON.stringify({
            news_id: testNews.id,
            news_title: testNews.title,
            news_content: testNews.content,
            model: 'gemini'
          })
        });

        if (analysisResult.success && analysisResult.data.success) {
          const analysis = analysisResult.data.analysis;
          this.logTestResult(
            'AI分析 - Gemini模型',
            true,
            `分析成功, 整体影响: ${analysis.overall_impact?.level || '未知'}`
          );
        } else {
          this.logTestResult(
            'AI分析 - Gemini模型',
            false,
            analysisResult.error || analysisResult.data?.error || 'Gemini分析失败'
          );
        }
      } catch (error) {
        this.logTestResult('AI分析 - Gemini模型', false, `Gemini分析异常: ${error.message}`);
      }

    } catch (error) {
      this.logTestResult('AI分析 - 测试准备', false, `测试准备失败: ${error.message}`);
    }
  }

  async testBatchAnalysis() {
    console.log('📊 测试批量分析功能...');

    try {
      // 获取一些新闻用于批量分析测试
      const newsResult = await this.makeRequest('/financial-news/latest?limit=3');
      if (!newsResult.success || !newsResult.data.success || !newsResult.data.news?.length) {
        this.logTestResult('批量分析 - 获取测试数据', false, '无法获取测试新闻');
        return;
      }

      const newsIds = newsResult.data.news.map(news => news.id);

      // 创建批量分析任务
      try {
        const createResult = await this.makeRequest('/news/batch-analysis/create', {
          method: 'POST',
          body: JSON.stringify({
            news_ids: newsIds,
            model: 'glm',
            priority: 'normal'
          })
        });

        if (createResult.success && createResult.data.success) {
          const taskId = createResult.data.task_id;
          this.logTestResult(
            '批量分析 - 任务创建',
            true,
            `成功创建任务: ${taskId}, 新闻数量: ${createResult.data.total_items}`
          );

          // 测试任务状态查询
          try {
            const statusResult = await this.makeRequest(`/news/batch-analysis/${taskId}/status`);
            if (statusResult.success && statusResult.data.success) {
              const status = statusResult.data.task_status;
              this.logTestResult(
                '批量分析 - 状态查询',
                true,
                `任务状态: ${status.status}, 进度: ${status.progress}%`
              );
            } else {
              this.logTestResult(
                '批量分析 - 状态查询',
                false,
                statusResult.error || statusResult.data?.error || '状态查询失败'
              );
            }
          } catch (error) {
            this.logTestResult('批量分析 - 状态查询', false, `状态查询异常: ${error.message}`);
          }

        } else {
          this.logTestResult(
            '批量分析 - 任务创建',
            false,
            createResult.error || createResult.data?.error || '任务创建失败'
          );
        }
      } catch (error) {
        this.logTestResult('批量分析 - 任务创建', false, `任务创建异常: ${error.message}`);
      }

    } catch (error) {
      this.logTestResult('批量分析 - 测试准备', false, `测试准备失败: ${error.message}`);
    }
  }

  async testLocalStorageFeatures() {
    console.log('💾 测试本地存储功能...');

    try {
      // 测试模型偏好存储
      const testModel = 'gemini';
      localStorage.setItem('preferred-ai-model', testModel);
      const savedModel = localStorage.getItem('preferred-ai-model');
      
      if (savedModel === testModel) {
        this.logTestResult(
          '本地存储 - 模型偏好',
          true,
          `成功保存和读取模型偏好: ${savedModel}`
        );
      } else {
        this.logTestResult(
          '本地存储 - 模型偏好',
          false,
          `模型偏好存储失败: 期望 ${testModel}, 实际 ${savedModel}`
        );
      }

      // 清理测试数据
      localStorage.removeItem('preferred-ai-model');

    } catch (error) {
      this.logTestResult('本地存储 - 功能测试', false, `本地存储测试失败: ${error.message}`);
    }
  }

  async runAllTests() {
    console.log('🚀 开始运行前端功能测试...');
    console.log('=' * 60);

    // 运行各项测试
    await this.testApiEndpoints();
    await this.testAIAnalysis();
    await this.testBatchAnalysis();
    await this.testLocalStorageFeatures();

    // 输出测试结果摘要
    const totalTests = this.passedTests + this.failedTests;
    const successRate = totalTests > 0 ? (this.passedTests / totalTests * 100) : 0;

    console.log('=' * 60);
    console.log('📊 测试结果摘要');
    console.log('=' * 60);
    console.log(`总测试数: ${totalTests}`);
    console.log(`通过测试: ${this.passedTests} ✅`);
    console.log(`失败测试: ${this.failedTests} ❌`);
    console.log(`成功率: ${successRate.toFixed(1)}%`);
    console.log('=' * 60);

    // 输出失败的测试详情
    if (this.failedTests > 0) {
      console.log('❌ 失败测试详情:');
      this.testResults
        .filter(result => !result.success)
        .forEach(result => {
          console.log(`  - ${result.testName}: ${result.message}`);
        });
    }

    return {
      totalTests,
      passedTests: this.passedTests,
      failedTests: this.failedTests,
      successRate,
      testResults: this.testResults
    };
  }
}

// 主函数
async function main() {
  const tester = new FrontendTester();
  const results = await tester.runAllTests();
  
  // 在浏览器环境中，将结果存储到全局变量
  if (typeof window !== 'undefined') {
    window.testResults = results;
    console.log('测试结果已保存到 window.testResults');
  }
  
  return results;
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { FrontendTester, main };
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== 'undefined') {
  // 等待页面加载完成后运行测试
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', main);
  } else {
    main();
  }
}
