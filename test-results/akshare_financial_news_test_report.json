{"test_time": "2025-06-13 12:29:06", "total_interfaces": 7, "success_count": 7, "success_rate": "100.0%", "results": {"东方财富财经早餐": "成功", "东方财富全球财经快讯": "成功", "新浪财经全球财经快讯": "成功", "富途牛牛快讯": "成功", "同花顺财经全球财经直播": "成功", "财联社电报": "成功", "新浪财经证券原创": "成功"}, "test_cases": [{"name": "东方财富财经早餐", "func": "<function stock_info_cjzc_em at 0x113901120>", "args": [], "kwargs": {}, "description": "无限制，返回全部历史数据"}, {"name": "东方财富全球财经快讯", "func": "<function stock_info_global_em at 0x1139011c0>", "args": [], "kwargs": {}, "description": "最近200条"}, {"name": "新浪财经全球财经快讯", "func": "<function stock_info_global_sina at 0x113901260>", "args": [], "kwargs": {}, "description": "最近20条"}, {"name": "富途牛牛快讯", "func": "<function stock_info_global_futu at 0x113901300>", "args": [], "kwargs": {}, "description": "最近50条"}, {"name": "同花顺财经全球财经直播", "func": "<function stock_info_global_ths at 0x1139013a0>", "args": [], "kwargs": {}, "description": "最近20条"}, {"name": "财联社电报", "func": "<function stock_info_global_cls at 0x113901440>", "args": [], "kwargs": {"symbol": "全部"}, "description": "最近20条"}, {"name": "新浪财经证券原创", "func": "<function stock_info_broker_sina at 0x1139014e0>", "args": [], "kwargs": {"page": "1"}, "description": "分页数据"}]}