#!/usr/bin/env python3
"""
Akshare 集成功能使用示例
演示如何使用新的智能数据获取功能
"""

import os
import sys
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.data_manager import init_data_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def main():
    """主函数"""
    
    print("=" * 80)
    print("📈 Akshare 集成功能使用示例")
    print("=" * 80)
    
    # 配置参数
    TUSHARE_TOKEN = "d255cb225a58d9daed9f7a86c3319268619c1d8d821d5a8967dc698c"
    
    try:
        # 1. 初始化数据管理器
        print("\n📊 初始化数据管理器...")
        data_manager = init_data_manager(TUSHARE_TOKEN)
        print("✅ 数据管理器初始化成功")
        
        # 2. 获取美股数据示例
        print("\n🇺🇸 获取美股数据示例...")
        
        # 获取苹果公司过去3个月的数据
        print("📱 获取苹果公司(AAPL)数据...")
        aapl_data = data_manager.get_stock_data_intelligent('AAPL')
        
        if not aapl_data.empty:
            print(f"✅ 成功获取 AAPL 数据：{len(aapl_data)} 条记录")
            print(f"   日期范围：{aapl_data['trade_date'].min()} ~ {aapl_data['trade_date'].max()}")
            print(f"   最新收盘价：${aapl_data.iloc[-1]['close']:.2f}")
            volume = aapl_data.iloc[-1]['vol']
            volume_str = f"{volume:,.0f}" if volume is not None and volume > 0 else "N/A"
            print(f"   最新成交量：{volume_str}")
            
            # 显示最近5天的数据
            print("\n📊 最近5天的数据：")
            recent_data = aapl_data.tail(5)[['trade_date', 'open', 'high', 'low', 'close', 'vol']]
            for _, row in recent_data.iterrows():
                volume = row['vol']
                volume_str = f"{volume:,.0f}" if volume is not None and volume > 0 else "N/A"
                print(f"   {row['trade_date']}: 开盘${row['open']:.2f}, 收盘${row['close']:.2f}, 成交量{volume_str}")
        
        # 3. 获取中国股票数据示例
        print("\n🇨🇳 获取中国股票数据示例...")
        
        # 获取平安银行数据
        print("🏦 获取平安银行(000001.SZ)数据...")
        ping_an_data = data_manager.get_stock_data_intelligent('000001.SZ')
        
        if not ping_an_data.empty:
            print(f"✅ 成功获取平安银行数据：{len(ping_an_data)} 条记录")
            print(f"   日期范围：{ping_an_data['trade_date'].min()} ~ {ping_an_data['trade_date'].max()}")
            print(f"   最新收盘价：¥{ping_an_data.iloc[-1]['close']:.2f}")
            print(f"   最新成交量：{ping_an_data.iloc[-1]['vol']:,.0f}")
        
        # 4. 批量获取多只股票数据
        print("\n📈 批量获取多只股票数据...")
        
        stock_symbols = ['MSFT', 'GOOGL', 'TSLA']  # 微软、谷歌、特斯拉
        
        for symbol in stock_symbols:
            print(f"\n📊 获取 {symbol} 数据...")
            data = data_manager.get_stock_data_intelligent(symbol)
            
            if not data.empty:
                latest_price = data.iloc[-1]['close']
                change_pct = data.iloc[-1]['pct_change']
                print(f"   ✅ {symbol}: ${latest_price:.2f} ({change_pct:+.2f}%)")
            else:
                print(f"   ❌ {symbol}: 数据获取失败")
        
        # 5. 获取因子数据示例
        print("\n🧮 获取因子数据示例...")
        
        # 获取 AAPL 的最新因子数据
        latest_date = aapl_data.iloc[-1]['trade_date']
        factors = data_manager.get_factor_data('AAPL', latest_date)
        
        if factors:
            print(f"✅ AAPL ({latest_date}) 的因子数据：")
            # 显示前10个因子
            factor_items = list(factors.items())[:10]
            for factor_name, factor_value in factor_items:
                print(f"   {factor_name}: {factor_value:.4f}")
            print(f"   ... 共 {len(factors)} 个因子")
        else:
            print("❌ 未找到因子数据")
        
        # 6. 指定日期范围获取数据
        print("\n📅 指定日期范围获取数据...")
        
        # 获取最近30天的数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y%m%d')
        
        print(f"📊 获取 AAPL 最近30天数据 ({start_date} ~ {end_date})...")
        recent_data = data_manager.get_stock_data_intelligent(
            'AAPL', 
            start_date=start_date, 
            end_date=end_date
        )
        
        if not recent_data.empty:
            print(f"✅ 成功获取 {len(recent_data)} 条记录")
            
            # 计算一些简单统计
            avg_price = recent_data['close'].mean()
            max_price = recent_data['close'].max()
            min_price = recent_data['close'].min()
            total_volume = recent_data['vol'].sum()
            
            print(f"   平均收盘价：${avg_price:.2f}")
            print(f"   最高价：${max_price:.2f}")
            print(f"   最低价：${min_price:.2f}")
            print(f"   总成交量：{total_volume:,.0f}")
        
        # 7. 数据库状态查看
        print("\n💾 数据库状态查看...")
        
        try:
            with data_manager.db_manager.get_connection() as conn:
                # 查询数据库统计
                stock_count = conn.execute("SELECT COUNT(DISTINCT ts_code) FROM daily_data").fetchone()[0]
                total_records = conn.execute("SELECT COUNT(*) FROM daily_data").fetchone()[0]
                factor_records = conn.execute("SELECT COUNT(*) FROM factor_data").fetchone()[0]
                
                print(f"📊 数据库统计：")
                print(f"   股票数量：{stock_count} 只")
                print(f"   日线记录：{total_records:,} 条")
                print(f"   因子记录：{factor_records:,} 条")
                
                # 查询最活跃的股票（记录数最多）
                top_stocks = conn.execute("""
                    SELECT ts_code, COUNT(*) as record_count 
                    FROM daily_data 
                    GROUP BY ts_code 
                    ORDER BY record_count DESC 
                    LIMIT 5
                """).fetchall()
                
                print(f"\n📈 数据最丰富的股票：")
                for stock, count in top_stocks:
                    print(f"   {stock}: {count:,} 条记录")
                    
        except Exception as e:
            print(f"❌ 查询数据库状态失败：{e}")
        
        print("\n" + "=" * 80)
        print("🎉 示例运行完成！")
        print("💡 提示：")
        print("   - 首次运行会下载数据，后续运行会从数据库读取")
        print("   - 美股数据使用 Akshare，中国股票使用 Tushare")
        print("   - 所有数据都会自动保存到本地数据库")
        print("=" * 80)
        
    except Exception as e:
        print(f"❌ 运行过程中发生错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 