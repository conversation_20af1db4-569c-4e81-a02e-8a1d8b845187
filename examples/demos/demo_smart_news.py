#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能新闻影响分析功能演示脚本
"""

import requests
import json
import time
from datetime import datetime

def demo_smart_news_analysis():
    """演示智能新闻分析功能"""
    
    print("🚀 智能新闻影响分析功能演示")
    print("=" * 60)
    
    # 1. 获取最新新闻
    print("📰 步骤1: 获取最新财经新闻")
    try:
        response = requests.get("http://127.0.0.1:8000/financial-news/latest?limit=3")
        if response.status_code == 200:
            news_data = response.json()
            if news_data.get('success') and news_data.get('data'):
                print(f"✅ 成功获取 {len(news_data['data'])} 条新闻")
                
                # 显示新闻列表
                for i, news in enumerate(news_data['data'], 1):
                    print(f"\n{i}. {news.get('title', '无标题')[:80]}...")
                    print(f"   来源: {news.get('source_name', '未知')} | 时间: {news.get('publish_time', '未知')}")
                
                # 选择第一条新闻进行分析
                selected_news = news_data['data'][0]
                print(f"\n🎯 选择分析新闻: {selected_news.get('title', '无标题')[:50]}...")
                
            else:
                print("❌ 没有获取到新闻数据")
                return
        else:
            print(f"❌ 获取新闻失败: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 网络错误: {e}")
        return
    
    # 2. 进行AI影响分析
    print("\n🤖 步骤2: 进行AI影响分析")
    print("⏳ 正在调用GLM-4-Flash API进行分析，请稍候...")
    
    try:
        analysis_request = {
            "news_id": selected_news.get('id'),
            "news_title": selected_news.get('title'),
            "news_content": selected_news.get('content'),
            "news_source": selected_news.get('source_name'),
            "news_publish_time": selected_news.get('publish_time')
        }
        
        start_time = time.time()
        response = requests.post(
            "http://127.0.0.1:8000/news/impact-analysis",
            json=analysis_request,
            timeout=60
        )
        analysis_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                analysis = result.get('analysis', {})
                print(f"✅ 分析完成 (耗时: {analysis_time:.1f}秒)")
                print(f"📊 分析ID: {result.get('analysis_id')}")
                print(f"💾 使用缓存: {'是' if result.get('cached') else '否'}")
                
                # 3. 展示分析结果
                print("\n📊 步骤3: 分析结果展示")
                print("=" * 60)
                
                # 整体影响
                overall = analysis.get('overall_impact', {})
                print(f"🎯 整体影响评估:")
                print(f"   影响程度: {overall.get('level', '未知')}")
                print(f"   影响概述: {overall.get('summary', '无')[:100]}...")
                
                # 市场影响
                markets = [
                    ('us_market', '🇺🇸 美股市场'),
                    ('a_share_market', '🇨🇳 A股市场'),
                    ('hk_market', '🇭🇰 港股市场')
                ]
                
                print(f"\n📈 市场影响分析:")
                for market_key, market_name in markets:
                    market_data = analysis.get(market_key, {})
                    if market_data:
                        print(f"   {market_name}:")
                        print(f"     影响程度: {market_data.get('impact_level', '未知')}")
                        print(f"     影响方向: {market_data.get('direction', '未知')}")
                        
                        # 推荐股票
                        stocks = market_data.get('recommended_stocks', [])
                        if stocks:
                            print(f"     推荐关注: {stocks[0].get('symbol', '')} - {stocks[0].get('name', '')[:20]} ({stocks[0].get('impact', '')})")
                
                # 主要指数影响
                indices = analysis.get('major_indices', {})
                if indices:
                    print(f"\n📊 主要指数影响:")
                    index_names = {
                        'sp500': 'S&P 500',
                        'nasdaq': '纳斯达克',
                        'shanghai_composite': '上证综指',
                        'hang_seng': '恒生指数'
                    }
                    
                    for index_key, index_name in index_names.items():
                        index_data = indices.get(index_key, {})
                        if index_data:
                            print(f"   {index_name}: {index_data.get('impact', '未知')}")
                
                # 投资建议
                advice = analysis.get('investment_advice', {})
                if advice:
                    print(f"\n💡 投资建议:")
                    print(f"   策略: {advice.get('strategy', '无')[:80]}...")
                    print(f"   时间范围: {advice.get('time_horizon', '未知')}")
                    
                    attention_points = advice.get('attention_points', [])
                    if attention_points:
                        print(f"   注意事项: {attention_points[0][:60]}...")
                
                # 风险评估
                risk = analysis.get('risk_assessment', {})
                if risk:
                    print(f"\n⚠️  风险评估:")
                    print(f"   短期风险: {risk.get('short_term_risk', '未知')}")
                    print(f"   中期风险: {risk.get('medium_term_risk', '未知')}")
                
                print("\n" + "=" * 60)
                print("🎉 演示完成！")
                print("\n📋 功能特点:")
                print("  • 基于GLM-4-Flash的专业金融分析")
                print("  • 多维度市场影响评估")
                print("  • 个股推荐和投资建议")
                print("  • 智能缓存机制")
                print("\n🌐 访问完整界面: http://localhost:3000")
                
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 分析请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")

if __name__ == "__main__":
    demo_smart_news_analysis() 