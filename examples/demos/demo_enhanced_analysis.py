#!/usr/bin/env python3
"""
工具增强分析演示脚本
展示 backend/ai 系统的完整功能，包括工具使用和多代理协作
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# 添加 backend 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

# 导入后端AI系统
from backend.ai.agents import AgentManager
from backend.ai.tools.akshare import get_famous_stock_data_tool
import pandas as pd


async def demo_tool_enhanced_analysis():
    """演示工具增强分析功能"""
    print("=" * 80)
    print("🚀 工具增强分析演示 - backend/ai 系统")
    print("=" * 80)
    
    # 创建智能体管理器
    agent_manager = AgentManager()
    
    print(f"⏰ 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🛠️ 可用工具: {len(agent_manager.available_tools)} 个")
    print(f"📊 分析股票: AAPL (苹果公司)")
    print("-" * 80)
    
    try:
        # 演示工具增强分析
        print("🤖 启动工具增强分析师...")
        analysis_result = await agent_manager.get_tool_enhanced_analysis(
            symbol="AAPL",
            query="请全面分析苹果公司AAPL股票，包括最新股价、技术指标、新闻资讯和投资建议"
        )
        
        if "error" not in analysis_result:
            print("📋 工具增强分析报告:")
            print("=" * 80)
            print(analysis_result["analysis_content"])
            print("=" * 80)
            
            # 保存分析结果
            with open('tool_enhanced_analysis_report.md', 'w', encoding='utf-8') as f:
                f.write(f"# 苹果公司（AAPL）工具增强分析报告\n\n")
                f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(f"**分析系统**: backend/ai 工具增强分析师\n\n")
                f.write(f"**使用工具**: {len(agent_manager.available_tools)}个专业工具\n\n")
                f.write(analysis_result["analysis_content"])
            
            print("✅ 工具增强分析报告已保存到 tool_enhanced_analysis_report.md")
        else:
            print(f"❌ 工具增强分析出错: {analysis_result['error']}")
            
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


async def demo_comprehensive_analysis():
    """演示综合分析功能"""
    print("\n" + "=" * 80)
    print("🔬 综合分析演示")
    print("=" * 80)
    
    # 创建智能体管理器
    agent_manager = AgentManager()
    
    print("📊 开始综合分析苹果公司（AAPL）...")
    
    try:
        # 1. 获取股票数据
        print("📥 正在获取股票数据...")
        stock_data_result = get_famous_stock_data_tool.invoke({
            "company_name": "苹果公司",
            "max_rows": 120
        })
        
        # 解析股票数据
        stock_data_json = json.loads(stock_data_result)
        
        if "data" in stock_data_json and stock_data_json["data"]:
            stock_df = pd.DataFrame(stock_data_json["data"])
            stock_df['date'] = pd.to_datetime(stock_df['date'])
            stock_df = stock_df.set_index('date').sort_index()
            
            print(f"✅ 成功获取 {len(stock_df)} 条股票数据")
            
            # 2. 计算技术指标
            close_prices = pd.to_numeric(stock_df['close'], errors='coerce')
            volumes = pd.to_numeric(stock_df['volume'], errors='coerce')
            
            technical_factors = {
                "current_price": float(close_prices.iloc[-1]),
                "ma5": float(close_prices.rolling(5).mean().iloc[-1]),
                "ma20": float(close_prices.rolling(20).mean().iloc[-1]),
                "ma50": float(close_prices.rolling(50).mean().iloc[-1]),
                "volume_avg": float(volumes.rolling(20).mean().iloc[-1]),
                "price_change_1d": float((close_prices.iloc[-1] - close_prices.iloc[-2]) / close_prices.iloc[-2] * 100),
                "volatility_20d": float(close_prices.pct_change().rolling(20).std().iloc[-1] * 100),
            }
            
            # 3. 准备综合数据
            all_data = {
                "stock_data": stock_df,
                "technical_factors": technical_factors,
                "fundamental_data": {
                    "symbol": "AAPL",
                    "company_name": "Apple Inc.",
                    "sector": "Technology",
                    "market_cap": "3.2T USD (estimate)"
                },
                "risk_data": {
                    "volatility": technical_factors["volatility_20d"],
                    "beta": 1.2,  # estimated
                    "risk_level": "Medium"
                }
            }
            
            # 4. 执行综合分析
            print("🤖 综合分析师正在工作...")
            analysis_result = await agent_manager.get_comprehensive_analysis(
                symbol="AAPL",
                all_data=all_data
            )
            
            if "error" not in analysis_result:
                print("📋 综合分析报告:")
                print("=" * 80)
                print(analysis_result["analysis_content"])
                print("=" * 80)
                
                # 保存分析结果
                with open('comprehensive_analysis_report.md', 'w', encoding='utf-8') as f:
                    f.write(f"# 苹果公司（AAPL）综合分析报告\n\n")
                    f.write(f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write(f"**分析系统**: backend/ai 综合分析师\n\n")
                    f.write(f"**数据维度**: 技术面、基本面、风险评估\n\n")
                    f.write(analysis_result["analysis_content"])
                
                print("✅ 综合分析报告已保存到 comprehensive_analysis_report.md")
            else:
                print(f"❌ 综合分析出错: {analysis_result['error']}")
        else:
            print("❌ 未能获取有效的股票数据")
            
    except Exception as e:
        print(f"❌ 综合分析失败: {e}")
        import traceback
        traceback.print_exc()


async def demo_multi_agent_workflow():
    """演示多代理工作流"""
    print("\n" + "=" * 80)
    print("👥 多代理工作流演示")
    print("=" * 80)
    
    # 创建智能体管理器
    agent_manager = AgentManager()
    
    stocks = ["AAPL", "TSLA", "MSFT"]
    
    print(f"📊 分析股票组合: {', '.join(stocks)}")
    print("🤖 启动多代理协作分析...")
    
    results = {}
    
    for symbol in stocks:
        print(f"\n📈 分析 {symbol}...")
        
        try:
            # 使用工具增强分析
            result = await agent_manager.get_tool_enhanced_analysis(
                symbol=symbol,
                query=f"请分析{symbol}股票的当前状态，包括技术面和基本面"
            )
            
            if "error" not in result:
                results[symbol] = result["analysis_content"]
                print(f"✅ {symbol} 分析完成")
            else:
                print(f"❌ {symbol} 分析失败: {result['error']}")
                
        except Exception as e:
            print(f"❌ {symbol} 分析出错: {e}")
    
    # 生成组合分析报告
    if results:
        print("\n📋 生成投资组合分析报告...")
        
        portfolio_report = f"# 投资组合分析报告\n\n"
        portfolio_report += f"**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        portfolio_report += f"**分析股票**: {', '.join(stocks)}\n\n"
        portfolio_report += f"**分析系统**: backend/ai 多代理系统\n\n"
        
        for symbol, analysis in results.items():
            portfolio_report += f"## {symbol} 分析\n\n"
            # 只显示前300字符作为摘要
            summary = analysis[:300] + "..." if len(analysis) > 300 else analysis
            portfolio_report += summary + "\n\n"
        
        with open('portfolio_analysis_report.md', 'w', encoding='utf-8') as f:
            f.write(portfolio_report)
        
        print("✅ 投资组合分析报告已保存到 portfolio_analysis_report.md")


async def main():
    """主函数"""
    print("🚀 backend/ai 系统功能演示")
    print(f"📂 系统位置: backend/ai")
    print(f"🔧 AkShare 状态: ✅ 已配置 Tushare Token")
    print(f"🛠️ 系统功能: 多代理协作、工具集成、专业分析")
    
    # 演示1: 工具增强分析
    await demo_tool_enhanced_analysis()
    
    # 演示2: 综合分析
    await demo_comprehensive_analysis()
    
    # 演示3: 多代理工作流
    await demo_multi_agent_workflow()
    
    print("\n" + "=" * 80)
    print("🎉 所有演示完成!")
    print("📁 生成的报告文件:")
    print("  - tool_enhanced_analysis_report.md (工具增强分析)")
    print("  - comprehensive_analysis_report.md (综合分析)")
    print("  - portfolio_analysis_report.md (投资组合分析)")
    print("=" * 80)


if __name__ == "__main__":
    asyncio.run(main()) 