/**
 * Message content preprocessing utilities
 */

import { formatBase64ImagesAsMarkdown, detectBase64Images } from './imageUtils';

/**
 * Preprocesses message content to enhance display
 */
export function preprocessMessageContent(content: string): string {
  if (!content || typeof content !== 'string') {
    return content || '';
  }

  let processedContent = content;

  // Step 1: Handle base64 images
  processedContent = formatBase64ImagesAsMarkdown(processedContent);

  // Step 2: Clean up extra whitespace while preserving intentional formatting
  processedContent = cleanupWhitespace(processedContent);

  // Step 3: Enhance code blocks
  processedContent = enhanceCodeBlocks(processedContent);

  // Step 4: Fix common markdown issues
  processedContent = fixMarkdownIssues(processedContent);

  return processedContent;
}

/**
 * Cleans up excessive whitespace while preserving intentional formatting
 */
function cleanupWhitespace(content: string): string {
  return content
    // Remove excessive blank lines (more than 2 consecutive)
    .replace(/\n{4,}/g, '\n\n\n')
    // Clean up trailing whitespace on lines
    .replace(/[ \t]+$/gm, '')
    // Ensure proper spacing around headers
    .replace(/^(#{1,6})\s*(.+)$/gm, '$1 $2')
    // Ensure proper spacing around list items
    .replace(/^(\s*[-*+])\s*(.+)$/gm, '$1 $2')
    // Clean up the final result
    .trim();
}

/**
 * Enhances code blocks with better formatting
 */
function enhanceCodeBlocks(content: string): string {
  // Fix code blocks that might be missing language specification
  return content
    // Add language hint for common patterns
    .replace(/```\n(import |from |def |class |function |const |let |var )/g, '```javascript\n$1')
    .replace(/```\n(SELECT |INSERT |UPDATE |DELETE |CREATE )/gi, '```sql\n$1')
    .replace(/```\n(<\?php|<\?=)/g, '```php\n$1')
    .replace(/```\n(#include|int main|void |char |float )/g, '```c\n$1');
}

/**
 * Fixes common markdown formatting issues
 */
function fixMarkdownIssues(content: string): string {
  return content
    // Fix headers that might be missing space after #
    .replace(/^(#{1,6})([^\s#])/gm, '$1 $2')
    // Fix list items that might be missing space after marker
    .replace(/^(\s*[-*+])([^\s])/gm, '$1 $2')
    // Fix numbered lists
    .replace(/^(\s*\d+\.)([^\s])/gm, '$1 $2')
    // Ensure proper line breaks before headers
    .replace(/([^\n])\n(#{1,6}\s)/g, '$1\n\n$2')
    // Ensure proper line breaks before lists
    .replace(/([^\n])\n(\s*[-*+]\s)/g, '$1\n\n$2')
    .replace(/([^\n])\n(\s*\d+\.\s)/g, '$1\n\n$2');
}

/**
 * Checks if content contains images
 */
export function hasImages(content: string): boolean {
  const images = detectBase64Images(content);
  return images.length > 0;
}

/**
 * Gets image count in content
 */
export function getImageCount(content: string): number {
  const images = detectBase64Images(content);
  return images.length;
}

/**
 * Preprocesses content specifically for streaming (lighter processing)
 */
export function preprocessStreamingContent(content: string): string {
  if (!content || typeof content !== 'string') {
    return content || '';
  }

  // Only do essential processing for streaming to avoid performance issues
  let processedContent = content;

  // Handle base64 images (most important for display)
  processedContent = formatBase64ImagesAsMarkdown(processedContent);

  // Basic whitespace cleanup
  processedContent = processedContent.trim();

  return processedContent;
}

/**
 * Validates and sanitizes message content
 */
export function sanitizeMessageContent(content: string): string {
  if (!content || typeof content !== 'string') {
    return '';
  }

  // Remove potentially dangerous content while preserving functionality
  const sanitized = content
    // Remove script tags
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    // Remove on* event handlers
    .replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')
    // Remove javascript: links
    .replace(/javascript:/gi, '');

  return sanitized;
}

/**
 * Extracts metadata from message content
 */
export function extractMessageMetadata(content: string): {
  hasImages: boolean;
  imageCount: number;
  hasCodeBlocks: boolean;
  hasTables: boolean;
  hasLinks: boolean;
  wordCount: number;
  estimatedReadTime: number; // in seconds
} {
  const images = detectBase64Images(content);
  const codeBlockMatches = content.match(/```[\s\S]*?```/g) || [];
  const tableMatches = content.match(/\|.*\|/g) || [];
  const linkMatches = content.match(/\[([^\]]+)\]\(([^)]+)\)/g) || [];
  
  // Simple word count (excluding code blocks and images)
  const textContent = content
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/data:image\/[^)]+/g, '') // Remove base64 images
    .replace(/[^\w\s]/g, ' ') // Replace non-word chars with spaces
    .trim();
  
  const words = textContent.split(/\s+/).filter(word => word.length > 0);
  const wordCount = words.length;
  
  // Estimate read time (average 200 words per minute)
  const estimatedReadTime = Math.max(Math.ceil(wordCount / 200 * 60), 5);

  return {
    hasImages: images.length > 0,
    imageCount: images.length,
    hasCodeBlocks: codeBlockMatches.length > 0,
    hasTables: tableMatches.length > 0,
    hasLinks: linkMatches.length > 0,
    wordCount,
    estimatedReadTime
  };
} 