-- 深度研究功能数据库迁移脚本
-- 为新闻深度分析功能添加必要的数据库表和字段

-- 1. 扩展现有的 news_impact_analysis 表  
-- 添加深度分析相关字段（SQLite不支持IF NOT EXISTS在ALTER TABLE中）
-- 使用PRAGMA检查表结构，如果字段不存在则添加
PRAGMA table_info(news_impact_analysis);

-- 添加字段（如果表存在且字段不存在，这些命令会正常执行）
ALTER TABLE news_impact_analysis ADD COLUMN deep_analysis_id INTEGER;
ALTER TABLE news_impact_analysis ADD COLUMN research_citations TEXT DEFAULT '[]';
ALTER TABLE news_impact_analysis ADD COLUMN research_sources TEXT DEFAULT '[]';
ALTER TABLE news_impact_analysis ADD COLUMN research_queries TEXT DEFAULT '[]';

-- 2. 创建深度研究结果表
CREATE TABLE IF NOT EXISTS news_deep_research (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id VARCHAR(36) UNIQUE NOT NULL,
    news_id INTEGER,
    news_title TEXT NOT NULL,
    news_content TEXT,
    news_source VARCHAR(100),
    news_publish_time DATETIME,
    research_topic TEXT NOT NULL,
    research_queries TEXT NOT NULL DEFAULT '[]', -- JSON array of queries
    research_results TEXT NOT NULL DEFAULT '[]', -- JSON array of search results
    final_analysis TEXT NOT NULL, -- Final analysis text
    sources_gathered TEXT DEFAULT '[]', -- JSON array of sources
    citations TEXT DEFAULT '[]', -- JSON array of citations
    analysis_context TEXT DEFAULT '{}', -- JSON object with context info
    research_status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed
    priority_level VARCHAR(10) DEFAULT 'medium', -- low, medium, high
    analysis_type VARCHAR(20) DEFAULT 'deep', -- quick, deep, comprehensive
    model_used VARCHAR(50) DEFAULT 'gemini-2.0-flash',
    processing_time_seconds INTEGER DEFAULT 0,
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    
    -- 外键约束
    FOREIGN KEY (news_id) REFERENCES news_impact_analysis(id) ON DELETE SET NULL
);

-- 3. 创建深度研究任务队列表
CREATE TABLE IF NOT EXISTS deep_research_queue (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id VARCHAR(36) UNIQUE NOT NULL,
    news_ids TEXT NOT NULL, -- JSON array of news IDs for batch processing
    priority VARCHAR(10) DEFAULT 'normal', -- low, normal, high, urgent
    status VARCHAR(20) DEFAULT 'queued', -- queued, processing, completed, failed
    max_concurrent INTEGER DEFAULT 2,
    progress_info TEXT DEFAULT '{}', -- JSON object with progress info
    created_by VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME
);

-- 4. 创建研究来源缓存表
CREATE TABLE IF NOT EXISTS research_sources_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source_url TEXT UNIQUE NOT NULL,
    source_title TEXT,
    source_domain VARCHAR(100),
    content_summary TEXT,
    reliability_score REAL DEFAULT 0.5, -- 0.0 to 1.0
    last_accessed DATETIME DEFAULT CURRENT_TIMESTAMP,
    access_count INTEGER DEFAULT 1
);

-- 5. 创建研究性能统计表
CREATE TABLE IF NOT EXISTS deep_research_stats (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    total_analyses INTEGER DEFAULT 0,
    successful_analyses INTEGER DEFAULT 0,
    failed_analyses INTEGER DEFAULT 0,
    avg_processing_time REAL DEFAULT 0.0,
    total_sources_found INTEGER DEFAULT 0,
    unique_sources_found INTEGER DEFAULT 0,
    model_usage TEXT DEFAULT '{}', -- JSON object tracking model usage
    
    UNIQUE(date)
);

-- 6. 创建更新触发器
-- 自动更新 updated_at 字段
CREATE TRIGGER IF NOT EXISTS update_deep_research_updated_at
    AFTER UPDATE ON news_deep_research
    FOR EACH ROW
BEGIN
    UPDATE news_deep_research 
    SET updated_at = CURRENT_TIMESTAMP 
    WHERE id = NEW.id;
END;

-- 7. 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_deep_research_task_id ON news_deep_research(task_id);
CREATE INDEX IF NOT EXISTS idx_deep_research_news_id ON news_deep_research(news_id);
CREATE INDEX IF NOT EXISTS idx_deep_research_status ON news_deep_research(research_status);
CREATE INDEX IF NOT EXISTS idx_deep_research_priority ON news_deep_research(priority_level);
CREATE INDEX IF NOT EXISTS idx_deep_research_created_at ON news_deep_research(created_at);
CREATE INDEX IF NOT EXISTS idx_deep_research_completed_at ON news_deep_research(completed_at);

-- 为队列表创建索引
CREATE INDEX IF NOT EXISTS idx_queue_status ON deep_research_queue(status);
CREATE INDEX IF NOT EXISTS idx_queue_priority ON deep_research_queue(priority);
CREATE INDEX IF NOT EXISTS idx_queue_created_at ON deep_research_queue(created_at);

-- 为来源缓存表创建索引
CREATE INDEX IF NOT EXISTS idx_sources_domain ON research_sources_cache(source_domain);
CREATE INDEX IF NOT EXISTS idx_sources_accessed ON research_sources_cache(last_accessed);

-- 为统计表创建索引
CREATE INDEX IF NOT EXISTS idx_stats_date ON deep_research_stats(date);

-- 8. 插入初始统计记录
INSERT OR IGNORE INTO deep_research_stats (date, total_analyses, successful_analyses, failed_analyses)
VALUES (DATE('now'), 0, 0, 0);

-- 9. 创建视图：便于查询最近的深度研究结果
CREATE VIEW IF NOT EXISTS recent_deep_research AS
SELECT 
    dr.id,
    dr.task_id,
    dr.news_title,
    dr.research_topic,
    dr.research_status,
    dr.priority_level,
    dr.final_analysis,
    json_array_length(dr.sources_gathered) as sources_count,
    json_array_length(dr.research_queries) as queries_count,
    dr.processing_time_seconds,
    dr.created_at,
    dr.completed_at,
    nia.overall_impact_level,
    nia.impact_score
FROM news_deep_research dr
LEFT JOIN news_impact_analysis nia ON dr.news_id = nia.id
WHERE dr.created_at >= datetime('now', '-7 days')
ORDER BY dr.created_at DESC;

-- 10. 创建函数：清理旧的研究数据
-- 注意：SQLite 不支持存储过程，这个功能需要在应用层实现
-- 但我们可以准备清理SQL语句的注释

-- 清理30天前的已完成任务（保留重要的高优先级分析）
-- DELETE FROM news_deep_research 
-- WHERE research_status = 'completed' 
--   AND priority_level != 'high' 
--   AND completed_at < datetime('now', '-30 days');

-- 清理90天前的队列记录
-- DELETE FROM deep_research_queue 
-- WHERE status IN ('completed', 'failed') 
--   AND completed_at < datetime('now', '-90 days');

-- 清理老旧的来源缓存（超过6个月且访问次数少于3次）
-- DELETE FROM research_sources_cache 
-- WHERE last_accessed < datetime('now', '-180 days') 
--   AND access_count < 3; 