-- ================================
-- Supabase新闻表创建脚本
-- ================================

-- 创建news表
CREATE TABLE IF NOT EXISTS public.news (
    id BIGSERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    title TEXT NOT NULL,
    content TEXT,
    published_at TIMESTAMPTZ NOT NULL,
    url TEXT UNIQUE,
    source TEXT NOT NULL,
    related_stocks JSONB DEFAULT '[]'::jsonb,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_news_url ON public.news(url);
CREATE INDEX IF NOT EXISTS idx_news_published_at ON public.news(published_at DESC);
CREATE INDEX IF NOT EXISTS idx_news_source ON public.news(source);
CREATE INDEX IF NOT EXISTS idx_news_created_at ON public.news(created_at DESC);

-- 创建复合索引
CREATE INDEX IF NOT EXISTS idx_news_source_published ON public.news(source, published_at DESC);

-- 为related_stocks JSONB字段创建GIN索引以支持高效的JSON查询
CREATE INDEX IF NOT EXISTS idx_news_related_stocks ON public.news USING gin(related_stocks);

-- 添加表注释
COMMENT ON TABLE public.news IS '新闻数据表，存储从各个数据源采集的金融新闻';
COMMENT ON COLUMN public.news.id IS '唯一标识符';
COMMENT ON COLUMN public.news.created_at IS '记录创建时间';
COMMENT ON COLUMN public.news.title IS '新闻标题';
COMMENT ON COLUMN public.news.content IS '新闻内容摘要';
COMMENT ON COLUMN public.news.published_at IS '新闻发布时间';
COMMENT ON COLUMN public.news.url IS '新闻原始链接，用于去重';
COMMENT ON COLUMN public.news.source IS '新闻来源（如：东方财富、财新网）';
COMMENT ON COLUMN public.news.related_stocks IS '相关股票代码，JSON数组格式';
COMMENT ON COLUMN public.news.updated_at IS '记录最后更新时间';

-- 创建自动更新updated_at字段的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 创建触发器
DROP TRIGGER IF EXISTS update_news_updated_at ON public.news;
CREATE TRIGGER update_news_updated_at
    BEFORE UPDATE ON public.news
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();