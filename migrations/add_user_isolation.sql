-- 数据库用户隔离机制升级脚本
-- 为所有业务表添加 user_id 字段，实现数据隔离

-- 0. 首先创建用户认证相关表（如果不存在）
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    session_token VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIG<PERSON> KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS user_preferences (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER UNIQUE NOT NULL,
    theme VARCHAR(20) DEFAULT 'system',
    language VARCHAR(10) DEFAULT 'zh',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    notifications_enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS user_activity_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    action VARCHAR(100) NOT NULL,
    details TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
);

-- 1. 为股票基本信息表添加用户字段
ALTER TABLE stock_info ADD COLUMN user_id INTEGER;

-- 2. 为中国A股日线数据表添加用户字段
ALTER TABLE cn_daily_data ADD COLUMN user_id INTEGER;

-- 3. 为港股日线数据表添加用户字段
ALTER TABLE hk_daily_data ADD COLUMN user_id INTEGER;

-- 4. 为美股日线数据表添加用户字段
ALTER TABLE us_daily_data ADD COLUMN user_id INTEGER;

-- 5. 为因子数据表添加用户字段
ALTER TABLE factor_data ADD COLUMN user_id INTEGER;

-- 6. 创建用户关注列表表
CREATE TABLE IF NOT EXISTS user_watchlist (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    ts_code TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, ts_code),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 7. 创建用户个人设置表
CREATE TABLE IF NOT EXISTS user_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL UNIQUE,
    risk_preference TEXT DEFAULT 'moderate', -- conservative, moderate, aggressive
    analysis_depth TEXT DEFAULT 'standard', -- basic, standard, detailed
    preferred_markets TEXT DEFAULT 'CN,US,HK', -- 逗号分隔的市场列表
    notification_enabled BOOLEAN DEFAULT TRUE,
    auto_sync_enabled BOOLEAN DEFAULT TRUE,
    theme_preference TEXT DEFAULT 'light', -- light, dark, auto
    language_preference TEXT DEFAULT 'zh-CN',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 8. 创建用户AI分析历史表
CREATE TABLE IF NOT EXISTS user_ai_analysis_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    query_text TEXT NOT NULL,
    analysis_type TEXT, -- technical, fundamental, news, comprehensive
    target_symbols TEXT, -- 逗号分隔的股票代码
    analysis_result TEXT, -- JSON格式的分析结果
    execution_time_ms INTEGER, -- 执行时间（毫秒）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 9. 为财经新闻表添加用户字段（如果表存在）
-- 检查表是否存在，然后添加字段
-- 注意：这些表可能在不同的数据库文件中

-- 10. 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS idx_stock_info_user_id ON stock_info(user_id);
CREATE INDEX IF NOT EXISTS idx_cn_daily_data_user_id ON cn_daily_data(user_id);
CREATE INDEX IF NOT EXISTS idx_hk_daily_data_user_id ON hk_daily_data(user_id);
CREATE INDEX IF NOT EXISTS idx_us_daily_data_user_id ON us_daily_data(user_id);
CREATE INDEX IF NOT EXISTS idx_factor_data_user_id ON factor_data(user_id);
CREATE INDEX IF NOT EXISTS idx_user_watchlist_user_id ON user_watchlist(user_id);
CREATE INDEX IF NOT EXISTS idx_user_ai_analysis_history_user_id ON user_ai_analysis_history(user_id);

-- 11. 创建复合索引以优化常用查询
CREATE INDEX IF NOT EXISTS idx_cn_daily_data_user_ts_date ON cn_daily_data(user_id, ts_code, trade_date);
CREATE INDEX IF NOT EXISTS idx_hk_daily_data_user_ts_date ON hk_daily_data(user_id, ts_code, trade_date);
CREATE INDEX IF NOT EXISTS idx_us_daily_data_user_ts_date ON us_daily_data(user_id, ts_code, trade_date);
CREATE INDEX IF NOT EXISTS idx_factor_data_user_ts_date ON factor_data(user_id, ts_code, trade_date);

-- 12. 创建数据迁移记录表
CREATE TABLE IF NOT EXISTS data_migration_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    migration_name TEXT NOT NULL,
    migration_version TEXT NOT NULL,
    status TEXT NOT NULL, -- pending, running, completed, failed
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    error_message TEXT,
    affected_rows INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 13. 记录此次迁移
INSERT INTO data_migration_log (migration_name, migration_version, status, start_time)
VALUES ('add_user_isolation', '1.0.0', 'completed', CURRENT_TIMESTAMP);

-- 14. 创建系统用户（用于现有数据）
-- 注意：这个操作需要在用户表存在的情况下执行
-- INSERT INTO users (id, username, email, password_hash, full_name, is_active, created_at)
-- VALUES (-1, 'system', 'system@localhost', 'system_hash', 'System User', 1, CURRENT_TIMESTAMP)
-- ON CONFLICT(id) DO NOTHING;

-- 15. 创建数据访问权限视图
-- 为用户数据访问创建便捷视图
CREATE VIEW IF NOT EXISTS user_stock_data_view AS
SELECT 
    d.*,
    s.name as stock_name,
    s.market,
    s.industry
FROM cn_daily_data d
LEFT JOIN stock_info s ON d.ts_code = s.ts_code
WHERE d.user_id IS NOT NULL;

CREATE VIEW IF NOT EXISTS public_stock_data_view AS
SELECT 
    d.*,
    s.name as stock_name,
    s.market,
    s.industry
FROM cn_daily_data d
LEFT JOIN stock_info s ON d.ts_code = s.ts_code
WHERE d.user_id IS NULL;

-- 16. 创建数据统计视图
CREATE VIEW IF NOT EXISTS user_data_statistics AS
SELECT 
    u.id as user_id,
    u.username,
    COUNT(DISTINCT w.ts_code) as watchlist_count,
    COUNT(DISTINCT ah.id) as analysis_count,
    MAX(ah.created_at) as last_analysis_time
FROM users u
LEFT JOIN user_watchlist w ON u.id = w.user_id
LEFT JOIN user_ai_analysis_history ah ON u.id = ah.user_id
GROUP BY u.id, u.username; 