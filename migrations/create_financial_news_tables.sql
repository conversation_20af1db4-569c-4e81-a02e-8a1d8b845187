-- 财经新闻数据库表结构迁移脚本
-- 创建时间: 2025-06-13
-- 版本: 1.0.0

-- 创建财经新闻表
CREATE TABLE IF NOT EXISTS financial_news (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    news_id VARCHAR(255) UNIQUE NOT NULL,  -- 新闻唯一标识（基于URL或内容hash）
    title TEXT NOT NULL,                   -- 新闻标题
    content TEXT,                          -- 新闻内容/摘要
    publish_time DATETIME NOT NULL,        -- 发布时间
    source VARCHAR(100) NOT NULL,          -- 数据源标识（eastmoney_breakfast, sina_global等）
    source_name VARCHAR(100) NOT NULL,     -- 数据源显示名称
    url TEXT,                              -- 新闻链接
    category VARCHAR(100),                 -- 新闻分类
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,  -- 入库时间
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,  -- 更新时间
    is_active BOOLEAN DEFAULT 1           -- 是否有效（软删除标记）
);

-- 创建数据源状态表
CREATE TABLE IF NOT EXISTS news_source_status (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    source VARCHAR(100) NOT NULL,         -- 数据源标识
    last_update DATETIME,                 -- 最后更新时间
    last_success DATETIME,                -- 最后成功时间
    total_count INTEGER DEFAULT 0,        -- 总新闻数量
    status VARCHAR(50) DEFAULT 'active',  -- 状态：active, error, disabled
    error_message TEXT,                   -- 错误信息
    response_time FLOAT,                  -- 响应时间（秒）
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_financial_news_publish_time ON financial_news(publish_time DESC);
CREATE INDEX IF NOT EXISTS idx_financial_news_source ON financial_news(source);
CREATE INDEX IF NOT EXISTS idx_financial_news_created_at ON financial_news(created_at DESC);
CREATE UNIQUE INDEX IF NOT EXISTS idx_financial_news_id ON financial_news(news_id);
CREATE UNIQUE INDEX IF NOT EXISTS idx_news_source_status_source ON news_source_status(source);

-- 插入初始数据源配置
INSERT OR IGNORE INTO news_source_status (source, status) VALUES 
('eastmoney_breakfast', 'active'),
('eastmoney_global', 'active'),
('sina_global', 'active'),
('futu_global', 'active'),
('ths_global', 'active'),
('cls_global', 'active'),
('sina_broker', 'active');

-- 创建触发器：自动更新 updated_at 字段
CREATE TRIGGER IF NOT EXISTS update_financial_news_timestamp 
    AFTER UPDATE ON financial_news
    FOR EACH ROW
BEGIN
    UPDATE financial_news SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER IF NOT EXISTS update_news_source_status_timestamp 
    AFTER UPDATE ON news_source_status
    FOR EACH ROW
BEGIN
    UPDATE news_source_status SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END; 