#!/usr/bin/env python3
"""
前端四层分析功能综合测试脚本
验证前端到后端的完整四层分析流程
"""

import requests
import json
import time
import sys

def test_backend_health():
    """测试后端健康状态"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务运行正常")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接后端服务: {e}")
        return False

def test_frontend_health():
    """测试前端健康状态"""
    try:
        response = requests.get("http://localhost:3000", timeout=5)
        if response.status_code == 200 and "金融投资助手" in response.text:
            print("✅ 前端服务运行正常")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接前端服务: {e}")
        return False

def test_four_layer_api():
    """测试四层分析API"""
    print("\n🧪 测试四层分析API...")
    
    test_data = {
        "news_title": "苹果公司发布新iPhone，预计销量大增",
        "news_content": "苹果公司今日发布了新一代iPhone，配备革命性芯片技术，预计将带动消费电子产业链全面升级。分析师预测新产品将在全球市场取得突破性销量。",
        "news_source": "科技新闻",
        "news_publish_time": "2024-01-01 12:00:00",
        "analysis_type": "deep",
        "use_four_layer_analysis": True
    }
    
    try:
        print("📡 发送四层分析请求...")
        response = requests.post(
            "http://localhost:8000/news/deep-analysis",
            headers={"Content-Type": "application/json"},
            json=test_data,
            stream=True,
            timeout=120
        )
        
        if response.status_code != 200:
            print(f"❌ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            return False
        
        print("✅ API请求成功，开始接收流式数据...")
        
        # 收集关键状态
        received_statuses = []
        four_layer_stages = []
        task_id = None
        final_result = None
        
        for line in response.iter_lines(decode_unicode=True):
            if line.startswith('data: '):
                try:
                    data = json.loads(line[6:])
                    message_type = data.get('type', '')
                    message = data.get('message', '')
                    
                    # 收集任务ID
                    if data.get('task_id') and not task_id:
                        task_id = data['task_id']
                        print(f"📋 任务ID: {task_id}")
                    
                    # 收集关键状态
                    received_statuses.append(message_type)
                    
                    # 特别关注四层分析阶段
                    if message_type.startswith('four_layer_'):
                        four_layer_stages.append(message_type)
                        print(f"🧠 {message}")
                    
                    # 显示重要状态
                    if message_type in ['task_started', 'queries_generated', 'web_research_started', 
                                      'four_layer_analysis_started', 'analysis_completed']:
                        print(f"📊 {message}")
                    
                    # 分析完成
                    if message_type == 'analysis_completed':
                        final_result = data.get('result')
                        print("🎉 分析完成！")
                        break
                        
                except json.JSONDecodeError:
                    continue
                except KeyboardInterrupt:
                    print("\n⚠️ 用户中断测试")
                    break
        
        # 验证结果
        print(f"\n📈 测试结果统计:")
        print(f"   📋 任务ID: {task_id or '未获取'}")
        print(f"   📨 接收状态数: {len(received_statuses)}")
        print(f"   🧠 四层分析阶段: {len(four_layer_stages)}")
        
        if four_layer_stages:
            print("   🧠 四层分析执行阶段:")
            for stage in four_layer_stages:
                stage_names = {
                    'four_layer_analysis_started': '启动四层思维链',
                    'four_layer_l1_perception': '第一层：事件感知',
                    'four_layer_l2_deep_dig': '第二层：深度挖掘', 
                    'four_layer_l3_domestic_impact': '第三层：国内影响',
                    'four_layer_l4_target_screening': '第四层：标的筛选',
                    'four_layer_synthesis': '四层结果综合'
                }
                print(f"      🔹 {stage_names.get(stage, stage)}")
        
        success = (
            task_id is not None and
            len(received_statuses) > 5 and
            len(four_layer_stages) > 0 and
            'analysis_completed' in received_statuses
        )
        
        if success:
            print("✅ 四层分析API测试通过")
        else:
            print("⚠️ 四层分析API测试部分成功")
            print(f"   检查项: task_id={task_id is not None}, statuses={len(received_statuses)}, four_layer={len(four_layer_stages)}")
        
        return success
        
    except Exception as e:
        print(f"❌ 四层分析API测试失败: {e}")
        return False

def test_normal_analysis_api():
    """测试普通分析API（不使用四层分析）"""
    print("\n🧪 测试普通分析API...")
    
    test_data = {
        "news_title": "普通分析测试新闻",
        "news_content": "这是一条用于测试普通分析功能的新闻。",
        "news_source": "测试来源",
        "news_publish_time": "2024-01-01 12:00:00",
        "analysis_type": "deep",
        "use_four_layer_analysis": False
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/news/deep-analysis",
            headers={"Content-Type": "application/json"},
            json=test_data,
            stream=True,
            timeout=60
        )
        
        if response.status_code != 200:
            print(f"❌ 普通分析API请求失败: {response.status_code}")
            return False
        
        received_statuses = []
        four_layer_stages = []
        
        # 只收集前10条消息来快速验证
        count = 0
        for line in response.iter_lines(decode_unicode=True):
            if line.startswith('data: ') and count < 10:
                try:
                    data = json.loads(line[6:])
                    message_type = data.get('type', '')
                    received_statuses.append(message_type)
                    
                    if message_type.startswith('four_layer_'):
                        four_layer_stages.append(message_type)
                    
                    count += 1
                except json.JSONDecodeError:
                    continue
        
        # 验证普通分析不应该有四层分析阶段
        if len(four_layer_stages) == 0:
            print("✅ 普通分析API测试通过（正确地没有四层分析阶段）")
            return True
        else:
            print(f"⚠️ 普通分析中意外检测到四层分析阶段: {four_layer_stages}")
            return False
            
    except Exception as e:
        print(f"❌ 普通分析API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 前端四层分析功能综合测试")
    print("="*60)
    
    # 基础健康检查
    print("📋 Step 1: 基础服务健康检查")
    backend_ok = test_backend_health()
    frontend_ok = test_frontend_health()
    
    if not backend_ok or not frontend_ok:
        print("\n❌ 基础服务不正常，无法进行功能测试")
        sys.exit(1)
    
    # API功能测试
    print("\n📋 Step 2: API功能测试")
    four_layer_ok = test_four_layer_api()
    normal_ok = test_normal_analysis_api()
    
    # 总结
    print("\n" + "="*60)
    print("📊 测试总结")
    print("="*60)
    
    results = {
        "后端服务": "✅" if backend_ok else "❌",
        "前端服务": "✅" if frontend_ok else "❌", 
        "四层分析API": "✅" if four_layer_ok else "❌",
        "普通分析API": "✅" if normal_ok else "❌"
    }
    
    for test_name, result in results.items():
        print(f"   {result} {test_name}")
    
    all_passed = all([backend_ok, frontend_ok, four_layer_ok, normal_ok])
    
    if all_passed:
        print("\n🎉 所有测试通过！四层分析功能已成功集成到前端")
        print("\n📝 用户操作指南:")
        print("   1. 访问 http://localhost:3000")
        print("   2. 在页面右上角找到 '🧠 四层分析' 开关")
        print("   3. 启用开关后，点击任意新闻的 'AI深度分析' 按钮")
        print("   4. 观察分析过程中的四层思维链状态")
        print("   5. 查看最终的四层分析报告")
    else:
        print("\n⚠️ 部分测试失败，请检查相关组件")
        failed_tests = [name for name, result in results.items() if result == "❌"]
        print(f"失败项目: {', '.join(failed_tests)}")
        sys.exit(1)

if __name__ == "__main__":
    main() 