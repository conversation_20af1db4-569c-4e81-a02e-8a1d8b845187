{"test_summary": {"total_tests": 10, "passed_tests": 10, "failed_tests": 0, "warning_tests": 0, "success_rate": 100.0, "test_time": "2025-06-19T11:59:26.899221"}, "detailed_results": [{"test_name": "导入和依赖关系测试", "status": "PASS", "message": "所有核心模块导入成功", "details": null, "timestamp": "2025-06-19T11:50:33.132315"}, {"test_name": "深度研究引擎初始化测试", "status": "PASS", "message": "引擎初始化成功，所有组件就绪", "details": null, "timestamp": "2025-06-19T11:50:33.166938"}, {"test_name": "四层分析器初始化测试", "status": "PASS", "message": "四层分析器初始化成功，所有分析方法就绪", "details": null, "timestamp": "2025-06-19T11:50:33.201063"}, {"test_name": "新闻分析工作流测试 - 案例1: 复杂供应链影响分析", "status": "PASS", "message": "工作流执行成功，共23个步骤", "details": {"analysis_time": "75.6秒", "steps_count": 23, "news_category": "航空运输"}, "timestamp": "2025-06-19T11:51:48.831066"}, {"test_name": "新闻分析工作流测试 - 案例2: 价格传导机制分析", "status": "PASS", "message": "工作流执行成功，共23个步骤", "details": {"analysis_time": "75.7秒", "steps_count": 23, "news_category": "原材料"}, "timestamp": "2025-06-19T11:53:04.580949"}, {"test_name": "新闻分析工作流测试 - 案例3: 政策冲击影响分析", "status": "PASS", "message": "工作流执行成功，共23个步骤", "details": {"analysis_time": "78.6秒", "steps_count": 23, "news_category": "科技政策"}, "timestamp": "2025-06-19T11:54:23.177847"}, {"test_name": "错误处理机制测试 - 空标题和内容", "status": "PASS", "message": "系统正确处理了无效输入，产生了合理的默认结果", "details": null, "timestamp": "2025-06-19T11:55:39.221477"}, {"test_name": "错误处理机制测试 - 超长文本", "status": "PASS", "message": "系统正确处理了无效输入，产生了合理的默认结果", "details": null, "timestamp": "2025-06-19T11:56:53.517241"}, {"test_name": "错误处理机制测试 - 缺少必需字段", "status": "PASS", "message": "系统正确处理了无效输入，产生了合理的默认结果", "details": null, "timestamp": "2025-06-19T11:58:11.726920"}, {"test_name": "性能指标测试", "status": "PASS", "message": "性能良好，总耗时 75.2秒", "details": {"总耗时": "75.2秒", "查询生成耗时": "3.0秒", "网络搜索耗时": "0.0秒", "四层分析耗时": "72.1秒"}, "timestamp": "2025-06-19T11:59:26.899025"}]}