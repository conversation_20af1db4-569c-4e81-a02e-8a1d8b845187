# How to Start the Remote Server

This guide summarizes the steps to connect to and start your EC2 instance for the Cash-Flow project. Ensure you have your SSH key file and EC2 public IP ready.

## Prerequisites
- SSH key file: `/Users/<USER>/AWS_Keys/Cash-Flow.pem` (adjust if your path differs).
- EC2 public IP address: e.g., `************`.
- The instance should be running in AWS.

## Step 1: SSH into the EC2 Instance
Run this command from your local terminal to connect:

```
ssh -i /Users/<USER>/AWS_Keys/Cash-Flow.pem ec2-user@<your-ec2-public-ip>
```
Replace `<your-ec2-public-ip>` with your actual EC2 public IP (e.g., ************). If prompted, type `yes` to continue.

## Step 2: Update System and Install Necessary Tools
Once connected, run these commands one by one to update the system and install required tools like Git, Node.js, and Nginx:

```
sudo dnf update -y
sudo dnf install git -y
sudo dnf install nodejs npm --enablerepo=epel -y
node -v  # Verify Node.js version
npm -v   # Verify npm version
sudo dnf install nginx -y
```

## Step 3: Clone and Start Your Project
Assuming you haven't cloned the project yet:

1. Clone your repository (e.g., if it's in Git):
   ```
sudo git clone <your-repo-url> /path/to/your/project  # Replace with your repo URL and desired path
cd /path/to/your/project
```

2. Start the backend service (based on your project's setup):
   ```
uvicorn backend.server:app --host 0.0.0.0 --port 8000  # Starts the FastAPI server
```

3. If you have a frontend, navigate to that directory and start it (e.g., for a Next.js project):
   ```
cd frontend  # Or wherever your frontend is
npm install  # If not already installed
npm run dev  # Or build and start as needed
```

4. Start Nginx if it's configured as a reverse proxy:
   ```
sudo systemctl start nginx
sudo systemctl enable nginx  # To start on boot
```

## Additional Notes
- Always run commands with `sudo` where necessary, and handle any prompts (use `-y` for automatic yes).
- Monitor logs for errors, e.g., `tail -f /var/log/messages`.
- To stop services, use commands like `sudo systemctl stop nginx` or Ctrl+C for development servers.
- For security, ensure your firewall rules in AWS are set correctly. 