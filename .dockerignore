# ================================
# Docker构建忽略文件
# ================================

# 版本控制
.git
.gitignore
.gitattributes

# 环境配置文件
.env
.env.local
.env.*.local
*.env

# 依赖目录
node_modules/
frontend/node_modules/
backend/__pycache__/
**/__pycache__/
**/*.pyc
**/*.pyo

# 构建产物和缓存
frontend/.next/
frontend/out/
frontend/build/
.cache/
*.log

# 开发工具
.vscode/
.idea/
*.swp
*.swo
*~

# 临时文件
tmp/
temp/
*.tmp

# 测试文件
**/*test*.py
tests/
frontend/src/**/*.test.*
frontend/src/**/*.spec.*
coverage/
.coverage
.pytest_cache/

# 文档
*.md
docs/
README*
CHANGELOG*
LICENSE*

# 备份文件
*backup*
frontend-backup/
src copy/

# 压缩文件
*.zip
*.tar.gz
*.rar

# 系统文件
.DS_Store
Thumbs.db
*.pid
*.lock

# 部署相关
docker-compose.override.yml
Dockerfile*
!Dockerfile

# 日志文件
*.log
logs/
backend.log
frontend.log

# 数据库文件 (将通过卷挂载)
data/*.db
test_*.db

# Python相关
*.egg-info/
dist/
build/
.tox/
.coverage
.pytest_cache/
venv/
env/

# Node.js相关
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 配置文件
conf.yaml
langgraph.json

# 其他
examples/
memory.md
todolist.md
*_todolist.md 